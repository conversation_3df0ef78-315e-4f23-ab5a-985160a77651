package com.dangbei.aisearch.infrastructure.common.helper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson2.JSONObject;
import com.dangbei.aisearch.domain.entity.BaseDocEntity;
import com.dangbei.aisearch.domain.gateway.ExternalCommonGateway;
import com.dangbei.aisearch.domain.service.DocContentSecurityChecker;
import com.dangbei.aisearch.infrastructure.config.properties.DocSecurityProperties;
import com.dangbei.aisearch.infrastructure.config.properties.ShareKnowledgeProperties;
import com.dangbei.aisearch.infrastructure.knowledge.VolcanoKnowledgeClient;
import com.dangbei.aisearch.infrastructure.knowledge.dto.query.VolcDocPointQuery;
import com.dangbei.devbase.client.dto.TencentDocumentReviewQueryResp;
import com.dangbei.devbase.client.dto.TencentDocumentReviewSubmitResp;
import com.dangbei.devbase.client.dto.TencentTextSyncReviewResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 阿里云文档内容安全检测实现
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
@Slf4j
@Component
public class TencentDocContentSecurityChecker implements DocContentSecurityChecker {

    @Resource
    private ExternalCommonGateway externalCommonGateway;
    @Resource
    private DocAccessHelper docAccessHelper;
    @Resource
    private DocSecurityProperties docSecurityProperties;
    @Resource
    private VolcanoKnowledgeClient volcanoKnowledgeClient;
    @Resource
    private ShareKnowledgeProperties shareKnowledgeProperties;

    // 安全检测状态码常量
    private static final String FILE_CHECK_STATUS_COMPLETED = "Success";
    private static final String FILE_CHECK_STATUS_SUBMITTED = "Submitted";
    private static final String FILE_CHECK_STATUS_PROCESSING = "Auditing";
    private static final int SUCCESS_STATUS = 0;

    @Override
    public SecurityCheckResult checkDocContentSecurity(BaseDocEntity docEntity, boolean strictMode) {
        log.info("md文档内容安全检测，docId={}", docEntity.getDocId());
        try {
            String pointContent = collectPoint(docEntity);
            var checkContentResp = externalCommonGateway.tencentSyncReviewContent(pointContent);
            if (checkContentResp == null || !checkContentResp.isSuccess() || checkContentResp.getData() == null) {
                log.warn("提交md文档内容安全检测任务失败，docId={}", docEntity.getDocId());
                // 提交失败，失败
                return SecurityCheckResult.builder().safe(!strictMode).failReason("提交md文档内容安全检测任务失败").build();
            }
            TencentTextSyncReviewResp reviewResp = checkContentResp.getData();
            return SecurityCheckResult.builder()
                .safe(reviewResp.getPass())
                .failReason(reviewResp.getReason())
                .build();
        } catch (Exception e) {
            log.error("md文档内容安全检测异常，docEntity={}", JSONObject.toJSONString(docEntity), e);
            // 异常情况下默认失败
            return SecurityCheckResult.builder().safe(!strictMode).failReason("md文档内容安全检测异常" + e.getMessage()).build();
        }
    }

    private String collectPoint(BaseDocEntity docEntity) {
        var volcDocPointQuery = VolcDocPointQuery
            .builder()
            .knowledgeIdExt(shareKnowledgeProperties.getCollectionName())
            .docIdExtList(List.of(docEntity.getDocIdExt()))
            .build();
        var volcDocPointCo = volcanoKnowledgeClient.listPoint(volcDocPointQuery);
        if (volcDocPointCo != null && volcDocPointCo.isSuccess() && volcDocPointCo.getData() != null) {
            var point = volcDocPointCo.getData();
            if (point.getTotalNum() > 0) {
                var pointList = point.getPointList();
                if (CollUtil.isNotEmpty(pointList)) {
                    StringBuilder content = new StringBuilder();
                    for (var p : pointList) {
                        content.append(p.getContent());
                    }
                    return content.toString();
                }
            }
        }
        return StringUtils.EMPTY;
    }

    @Override
    public SecurityCheckResult checkDocFileSecurity(BaseDocEntity docEntity, boolean strictMode) {
        try {
            // md 类型特殊处理
            if ("md".equals(docEntity.getDocType())) {
                return checkDocContentSecurity(docEntity, strictMode);
            }
            String fileUrl = docAccessHelper.getAccessUrl(docEntity.getStorageTypeEnum(), docEntity.getDocPath());
            if (StringUtils.isBlank(fileUrl)) {
                log.warn("获取文档访问URL失败，docPath={}", docEntity.getDocPath());
                return SecurityCheckResult.builder().safe(!strictMode).failReason("文档访问URL为空").build();
            }
            // 提交文档安全检测任务
            SingleResponse<TencentDocumentReviewSubmitResp> checkResult = externalCommonGateway.tencentFileCheck(fileUrl, docEntity.getDocType());
            if (checkResult == null || !checkResult.isSuccess() || checkResult.getData() == null) {
                log.warn("提交文档安全检测任务失败，fileUrl={}", fileUrl);
                // 提交失败，失败
                return SecurityCheckResult.builder().safe(!strictMode).failReason("提交文档安全检测任务失败").build();
            }
            String jobId = checkResult.getData().getJobId();
            if (StringUtils.isBlank(jobId)) {
                log.warn("获取文档安全检测任务ID失败");
                // 任务ID为空，失败
                return SecurityCheckResult.builder().safe(!strictMode).failReason("获取文档安全检测任务ID失败").build();
            }
            // 轮询检测结果
            return pollDocSecurityCheckResult(jobId, docEntity.getDocId(), strictMode);
        } catch (Exception e) {
            log.error("文档文件安全检测异常，docId={}", docEntity.getDocId(), e);
            // 异常情况下默认失败
            return SecurityCheckResult.builder().safe(!strictMode).failReason("文档文件安全检测异常" + e.getMessage()).build();
        }
    }

    /**
     * 轮询文档安全检测结果
     *
     * @param jobId 任务ID
     * @param docId 文档ID
     * @return 检测结果，true表示通过，false表示不通过
     */
    private SecurityCheckResult pollDocSecurityCheckResult(String jobId, String docId, boolean strictMode) {
        for (int i = 0; i < docSecurityProperties.getMaxRetryCount(); i++) {
            try {
                SingleResponse<TencentDocumentReviewQueryResp> resultResp = externalCommonGateway.queryTencentFileCheckResult(jobId);
                if (resultResp != null && resultResp.isSuccess() && resultResp.getData() != null) {
                    TencentDocumentReviewQueryResp result = resultResp.getData();
                    // 根据状态码判断检测结果(Submitted,Auditing)
                    if (FILE_CHECK_STATUS_PROCESSING.equals(result.getState()) || FILE_CHECK_STATUS_SUBMITTED.equals(result.getState())) {
                        // 处理中，继续轮询
                        log.debug("文档安全检测处理中，jobId={}", jobId);
                        ThreadUtil.sleep(docSecurityProperties.getMaxPollIntervalMillis());
                        continue;
                    } else if (FILE_CHECK_STATUS_COMPLETED.equals(result.getState())) {
                        // 检测完成，返回是否通过
                        log.info("文档安全检测完成，jobId={}", jobId);
                        if (result.getResult() != SUCCESS_STATUS) {
                            log.warn("文档内容不合法，taskId={}, 响应: {}", jobId, result);
                        }
                        return SecurityCheckResult.builder().safe(result.getResult() == SUCCESS_STATUS).failReason(result.getLabel()).build();
                    } else {
                        // 其他状态视为检测失败
                        log.warn("文档安全检测结果异常状态，jobId={}, 响应：{}",
                            jobId, JSONObject.toJSONString(result));
                        // 异常状态
                        return SecurityCheckResult.builder().safe(!strictMode).failReason(String.format("文档安全检测结果异常状态,jobId:%s,响应:%s", jobId, JSONObject.toJSONString(result))).build();
                    }
                }
                log.warn("获取文档安全检测结果失败，jobId={}", jobId);
                // 等待下次轮询
                ThreadUtil.sleep(docSecurityProperties.getMaxPollIntervalMillis());
            } catch (Exception e) {
                log.error("获取文档安全检测结果异常，jobId={}", jobId, e);
                // 等待下次轮询
                ThreadUtil.sleep(docSecurityProperties.getMaxPollIntervalMillis());
            }
        }
        // 超过最大重试次数
        log.warn("文档安全检测结果轮询超时，jobId={}, docId={}", jobId, docId);
        // 轮询超时
        return SecurityCheckResult.builder().safe(!strictMode).failReason("文档安全检测结果轮询超时,jobId:" + jobId).build();
    }
}
