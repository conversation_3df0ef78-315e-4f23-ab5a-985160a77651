package com.dangbei.aisearch.client.dto.cmd;

import com.alibaba.cola.dto.DTO;
import com.alibaba.fastjson2.annotation.JSONField;
import com.dangbei.aisearch.client.enums.KnowledgeTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 聊天命令
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-26
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ChatCmd extends DTO {

    @Schema(description = "会话ID")
    private String conversationId;

    @NotBlank(message = "智能体编码不能为空")
    @Schema(description = "智能体编码")
    private String botCode;

    @Schema(description = "是否流式，默认true")
    private Boolean stream = Boolean.TRUE;

    @Size(max = 8000, message = "用户提问超长")
    @Schema(description = "用户提问")
    private String question;

    @Schema(description = "用户动作 chat-聊天 retry-重新生成 deep-深度搜索 online-联网搜索 deep,online-深度联网搜索")
    private String userAction = "chat";

    @Schema(description = "模型 deepseek-深度求索 doubao-豆包 qwen-通义千问")
    private String model;

    @Schema(description = "附件列表")
    private List<FileItem> files;

    @Schema(description = "引用列表")
    private List<ReferenceItem> reference;

    @Schema(description = "知识库列表")
    private List<KnowledgeItem> knowledgeList;

    @Schema(description = "匿名会话的key")
    private String anonymousKey;

    @Schema(description = "聊天选项参数")
    private ChatOption chatOption = new ChatOption();

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class FileItem extends DTO {

        @Schema(description = "文件类型：file-文件类型；image-图片类型")
        private String type;

        @Schema(description = "文件ID")
        private String fileId;

        @Schema(description = "文件Url")
        private String fileUrl;

        @Schema(description = "文件名称")
        private String fileName;

        @Schema(description = "文档的类型，如docx")
        private String fileType;

        @Schema(description = "文件字节数")
        private Long fileSize;

        @Schema(description = "文档字数")
        private Long wordNum;

        /**
         * 是否图片
         * @return true=是
         */
        @JSONField(serialize = false)
        public boolean isTypeImage() {
            return "image".equals(this.type);
        }

        /**
         * 是否文件
         * @return true=是
         */
        @JSONField(serialize = false)
        public boolean isTypeFile() {
            return "file".equals(this.type);
        }

    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class ReferenceItem extends DTO {

        @Schema(description = "类型：file-文件类型；image-图片类型 text-文本")
        private String type;

        @Schema(description = "文本内容")
        private String text;

        @Schema(description = "文件ID")
        private String fileId;

        @Schema(description = "文件名称")
        private String fileName;

        @Schema(description = "文件路径")
        private String fileUrl;

        @Schema(description = "文档的类型，如docx")
        private String fileType;

        @Schema(description = "文件字节数")
        private Long fileSize;

        @Schema(description = "文档字数")
        private Long wordNum;

    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class KnowledgeItem extends DTO {
        /**
         * {@link KnowledgeTypeEnum}
         */
        @Schema(description = "知识库类型：0-个人知识库，1-共享知识库")
        private Integer knowledgeType;
        @Schema(description = "知识库ID")
        private String knowledgeId;
        @Schema(description = "知识库名称")
        private String name;
        @Schema(description = "是否是创建者")
        private Boolean isCreator;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class ChatOption extends DTO {

        @Schema(description = "帮我写场景编码")
        private String writeCode;

        @Schema(description = "是否搜索所有个人知识库")
        private boolean searchKnowledge;

        @Schema(description = "是否搜索所有共享知识库")
        private boolean searchSharedKnowledge;

        @Schema(description = "是否搜索所有知识库(个人+共享)")
        private boolean searchAllKnowledge;

        @Schema(description = "长链接ID")
        private String wsId;

        @Schema(description = "是否自动播报")
        private boolean autoTts;

    }

}
