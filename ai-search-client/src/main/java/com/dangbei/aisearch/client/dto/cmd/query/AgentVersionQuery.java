package com.dangbei.aisearch.client.dto.cmd.query;

import com.alibaba.cola.dto.DTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2025-02-28 14:58
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class AgentVersionQuery extends DTO {

    @Schema(description = "智能体唯一ID")
    @NotBlank(message = "agentId 不能为空")
    private String agentId;
}
