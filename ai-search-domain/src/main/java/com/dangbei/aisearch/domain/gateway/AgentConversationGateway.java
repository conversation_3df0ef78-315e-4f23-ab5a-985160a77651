package com.dangbei.aisearch.domain.gateway;

import com.dangbei.aisearch.client.dto.UserDeviceDTO;
import com.dangbei.aisearch.domain.common.base.BaseGateway;
import com.dangbei.aisearch.domain.entity.AgentConversationEntity;

import java.util.List;

/**
 * AgentConversation 网关层
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-28
 */
public interface AgentConversationGateway extends BaseGateway<Long, AgentConversationEntity> {

    /**
     * 获取根据用户设备信息和agentId
     * @param userDeviceDTO 用户设备信息
     * @param agentId       agentId
     * @return {@link AgentConversationEntity }
     */
    AgentConversationEntity getByUserDeviceInfoAndAgentId(UserDeviceDTO userDeviceDTO, String agentId);

    /**
     * 根据conversationId获取会话信息
     * @param conversationId 会话ID
     * @return {@link AgentConversationEntity }
     */
    AgentConversationEntity getByConversationId(String conversationId);

    /**
     * 根据userId或deviceId获取会话信息
     * @return 智能体列表
     */
    List<AgentConversationEntity> listByUserIdOrDeviceId(UserDeviceDTO userDeviceDTO, Integer limit);

    /**
     * 更新上次聊天时间
     * @param conversationId 交谈id
     */
    void updateLastChatTime(String conversationId);

    /**
     * 只将新智能体会话合并至账号
     * 例如：游客身份时聊了ABC，账号上聊了CDE，登录后，会把AB智能体会话合并到账号里，C不合并
     * @param deviceId 设备id
     * @param userId   用户id
     */
    void mergeConversation(String deviceId, String userId);
}
