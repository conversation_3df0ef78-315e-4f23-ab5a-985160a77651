package com.dangbei.aisearch.app.assembler;

import com.dangbei.aisearch.client.dto.clientobject.MessageListCo;
import com.dangbei.aisearch.client.dto.clientobject.ShareListCo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-26
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface ShareAssembler {

    ShareListCo toShareListCo(MessageListCo messageListCo);

    ShareListCo toShareListCo(com.dangbei.aidoggyscreenshot.client.dto.clientobject.MessageListCo messageListCo);
}
