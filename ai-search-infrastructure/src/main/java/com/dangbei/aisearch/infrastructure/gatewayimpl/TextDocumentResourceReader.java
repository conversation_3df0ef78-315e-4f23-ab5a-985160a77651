package com.dangbei.aisearch.infrastructure.gatewayimpl;

import cn.hutool.core.io.IoUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.cola.exception.BizException;
import com.dangbei.aisearch.client.enums.DocStorageTypeEnum;
import com.dangbei.aisearch.domain.entity.DocumentResource;
import com.dangbei.aisearch.domain.gateway.DocumentResourceContentReader;
import com.dangbei.aisearch.infrastructure.common.helper.DocAccessHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * 文本文档资源内容读取实现
 * <AUTHOR>
 * @date 2025-06-01
 **/
@Slf4j
@Component
public class TextDocumentResourceReader implements DocumentResourceContentReader {

    /**
     * 支持的文件类型
     */
    private static final Set<String> SUPPORTED_TYPES = new HashSet<>(Arrays.asList("txt", "md"));

    @Resource
    private DocAccessHelper docAccessHelper;

    @Override
    public boolean support(DocumentResource resource) {
        if (Objects.isNull(resource) || StringUtils.isBlank(resource.getDocType())) {
            return false;
        }
        
        // 硬编码支持的类型，不依赖配置
        return SUPPORTED_TYPES.contains(resource.getDocType().toLowerCase());
    }

    @Override
    public String readContent(DocumentResource resource) {
        if (!support(resource)) {
            throw new BizException("不支持读取该类型的文件");
        }
        
        String fileUrl = docAccessHelper.getAccessUrl(
                resource.getStorageType() != null ? 
                DocStorageTypeEnum.getByType(resource.getStorageType()) : null,
                resource.getDocPath());

        if (fileUrl == null) {
            throw new BizException("获取文件访问地址失败");
        }
        
        try (InputStream inputStream = HttpUtil.createGet(fileUrl).execute().bodyStream();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            IoUtil.copy(inputStream, outputStream);
            return outputStream.toString(StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("读取文件内容失败: {}", resource.getDocId(), e);
            throw new BizException("读取文件内容失败: " + e.getMessage());
        }
    }
} 