package com.dangbei.aisearch.app.executor.query;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.Assert;
import com.dangbei.aisearch.app.util.UserContextUtil;
import com.dangbei.aisearch.client.dto.clientobject.MemberPermissionsCo;
import com.dangbei.aisearch.client.dto.cmd.query.MemberPermissionsQuery;
import com.dangbei.aisearch.client.enums.SharedKnowledgePermissionEnum;
import com.dangbei.aisearch.client.enums.SharedKnowledgeRoleEnum;
import com.dangbei.aisearch.domain.entity.SharedKnowledgeEntity;
import com.dangbei.aisearch.domain.gateway.SharedKnowledgeGateway;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 成员权限查询执行器
 * <AUTHOR>
 * @date 2025-05-28
 **/
@Slf4j
@Component
public class MemberPermissionsQueryExe {

    @Resource
    private SharedKnowledgeGateway sharedKnowledgeGateway;

    /**
     * 执行权限查询
     * @param query 查询条件
     * @return 权限信息
     */
    public SingleResponse<MemberPermissionsCo> execute(MemberPermissionsQuery query) {
        String currentUserId = UserContextUtil.getNonNullUserId();
        // 获取知识库信息
        SharedKnowledgeEntity knowledgeEntity = sharedKnowledgeGateway.getByKnowledgeId(query.getKnowledgeId());
        Assert.notNull(knowledgeEntity, "知识库不存在");
        // 获取用户角色
        SharedKnowledgeRoleEnum userRole = knowledgeEntity.getUserRole(currentUserId);
        if (Objects.isNull(userRole)) {
            return SingleResponse.of(MemberPermissionsCo.builder()
                    .isMember(false)
                    .permissions(new ArrayList<>())
                    .build());
        }
        // 获取用户所有权限
        List<String> permissions = new ArrayList<>();
        for (SharedKnowledgePermissionEnum permission : SharedKnowledgePermissionEnum.values()) {
            if (knowledgeEntity.checkPermission(currentUserId, permission)) {
                permissions.add(String.valueOf(permission.getCode()));
            }
        }
        MemberPermissionsCo result = MemberPermissionsCo.builder()
                .isMember(true)
                .role(userRole.getCode())
                .roleName(userRole.getDesc())
                .permissions(permissions)
                .build();
        return SingleResponse.of(result);
    }
}
