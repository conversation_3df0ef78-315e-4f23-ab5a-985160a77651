package com.dangbei.aisearch.infrastructure.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.UserKnowledgeDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.UserKnowledgeMapper;
import com.dangbei.aisearch.infrastructure.repository.UserKnowledgeRepository;
import org.springframework.stereotype.Repository;

/**
 * UserKnowledge 仓库实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-10
 */
@Repository
public class UserKnowledgeRepositoryImpl extends ServiceImpl<UserKnowledgeMapper, UserKnowledgeDO> implements UserKnowledgeRepository {

}
