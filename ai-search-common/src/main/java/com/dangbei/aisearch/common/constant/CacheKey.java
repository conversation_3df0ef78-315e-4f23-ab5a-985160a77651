package com.dangbei.aisearch.common.constant;

import com.dangbei.aisearch.common.enums.SmsSceneCodeEnum;

/**
 * 缓存Key
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-23
 */
public interface CacheKey {

    // RTC凭证缓存Key
    String CredentialsCache = "CredentialsCache:{}";

    // AI PPT授权码用户缓存Key
    String aiPptCodeCacheKey = "aiPptCode:{}";

    // 短信登录验证码缓存Key
    String SMS_LOGIN_CODE = "sms_" + SmsSceneCodeEnum.LOGIN.getSceneCode() + "_" + "%s";

    // oss sts key
    String OSS_STS_KEY = "oss_sts_key";

    // tos sts key
    String TOS_STS_KEY = "tos_sts_key";

    // volc sts key
    String VOLC_STS_KEY = "volc_sts_key";

    // ip获取省市信息
    String IP_AREA_INFO = "ipArea:%s";

    // 疑似爬虫告警
    String CRAWLER_WARN = "crawler_warn:%s";

    // 首页问一问池子
    String RECOMMEND_QUESTION_POOL = "recommend_question_pool";

    // 首页帮我写推荐配置
    String RECOMMEND_WRITE_POOL_CONFIG = "recommend_write_pool_config";

    // 首页智能体池子
    String RECOMMEND_AGENT_POOL = "recommend_agent_pool";

    // 用户信息编辑次数
    String USER_INFO_UPDATE_TIMES = "user_profile_update_times:%s";

    // 限流
    String RATE_LIMIT = "rateLimit:%s:%s";

    // 限流
    String WS_SESSION_ID = "wsSession:%s";

    // IP检验
    String IP_CHECK = "ipCheck:%s";

    // 风控白名单
    String RISK_WHITE_LIST = "risk_white_list:%s";

    // 帮我写配置
    String WRITE_CONFIG = "write_config";

    // 自建 deepseek_v3 并发数
    String LLM_CONCURRENT_NUM_DEEPSEEK_V3 = "llm_concurrent:deepseek_v3";

    // 未读通知数
    String UNREAD_NOTIFICATION_COUNT = "unread_notification_count:%s";
}
