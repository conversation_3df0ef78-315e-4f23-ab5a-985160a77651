package com.dangbei.aisearch.infrastructure.gatewayimpl;

import com.dangbei.aisearch.client.dto.NotificationJumpConfig;
import com.dangbei.aisearch.client.enums.NotificationBizTypeEnum;
import com.dangbei.aisearch.client.enums.NotificationTypeEnum;
import com.dangbei.aisearch.domain.entity.UserNotificationEntity;
import com.dangbei.aisearch.domain.gateway.UserNotificationGateway;
import com.dangbei.aisearch.infrastructure.common.base.BaseGatewayImpl;
import com.dangbei.aisearch.infrastructure.convertor.UserNotificationConvertor;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.UserNotificationDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.UserNotificationMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * UserNotification 网关层实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-05-27
 */
@Component
public class UserNotificationGatewayImpl extends BaseGatewayImpl<Long, UserNotificationEntity, UserNotificationDO, UserNotificationMapper, UserNotificationConvertor> implements UserNotificationGateway {

    @Override
    public boolean send(NotificationTypeEnum type, NotificationBizTypeEnum bizType, String senderId, String userId, String title, String content, String relatedId, NotificationJumpConfig jumpConfig) {
        UserNotificationDO notificationDO = buildUserNotificationDO(type, bizType, senderId, userId, title, content, relatedId, jumpConfig);
        return save(notificationDO);
    }

    private UserNotificationDO buildUserNotificationDO(NotificationTypeEnum type, NotificationBizTypeEnum bizType, String senderId, String userId, String title, String content, String relatedId, NotificationJumpConfig jumpConfig) {
        UserNotificationDO notificationDO = new UserNotificationDO();
        notificationDO.setType(type.getCode());
        notificationDO.setBizType(bizType.getCode());
        notificationDO.setSenderId(senderId);
        notificationDO.setUserId(userId);
        notificationDO.setTitle(title);
        notificationDO.setContent(content);
        notificationDO.setRelatedId(relatedId);
        notificationDO.setJumpConfig(jumpConfig);
        return notificationDO;
    }

    @Override
    public boolean sendText(NotificationBizTypeEnum bizType, String senderId, String userId, String content, String relatedId, NotificationJumpConfig jumpConfig) {
        return send(NotificationTypeEnum.TEXT, bizType, senderId, userId, bizType.getDesc(), content, relatedId, jumpConfig);
    }

    @Override
    public boolean sendTextBatch(NotificationBizTypeEnum bizType, String senderId, Collection<String> userIds, String content, String relatedId, NotificationJumpConfig jumpConfig) {
        if (CollectionUtils.isEmpty(userIds)) {
            return false;
        }
        List<UserNotificationDO> notificationDOList = new ArrayList<>();
        Set<String> userIdSet = new HashSet<>(userIds);
        for (String userId : userIdSet) {
            UserNotificationDO userNotificationDO = buildUserNotificationDO(NotificationTypeEnum.TEXT, bizType, senderId, userId, bizType.getDesc(), content, relatedId, jumpConfig);
            notificationDOList.add(userNotificationDO);
        }
        return saveBatch(notificationDOList);
    }

    @Override
    public boolean sendApprovalBatch(NotificationBizTypeEnum bizType, String senderId, Collection<String> userIds, String content, String relatedId, NotificationJumpConfig jumpConfig) {
        if (CollectionUtils.isEmpty(userIds)) {
            return false;
        }
        List<UserNotificationDO> notificationDOList = new ArrayList<>();
        Set<String> userIdSet = new HashSet<>(userIds);
        for (String userId : userIdSet) {
            UserNotificationDO userNotificationDO = buildUserNotificationDO(NotificationTypeEnum.APPROVAL, bizType, senderId, userId, bizType.getDesc(), content, relatedId, jumpConfig);
            notificationDOList.add(userNotificationDO);
        }
        return saveBatch(notificationDOList);
    }
}
