package com.dangbei.aisearch.infrastructure.gatewayimpl;

import com.dangbei.aisearch.domain.entity.QuickQuestionRecordEntity;
import com.dangbei.aisearch.domain.gateway.QuickQuestionRecordGateway;
import com.dangbei.aisearch.infrastructure.common.base.BaseGatewayImpl;
import com.dangbei.aisearch.infrastructure.convertor.QuickQuestionRecordConvertor;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.QuickQuestionRecordDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.QuickQuestionRecordMapper;
import org.springframework.stereotype.Component;

/**
 * QuickQuestionRecord 网关层实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-05-15
 */
@Component
public class QuickQuestionRecordGatewayImpl extends BaseGatewayImpl<Long, QuickQuestionRecordEntity, QuickQuestionRecordDO, QuickQuestionRecordMapper, QuickQuestionRecordConvertor> implements QuickQuestionRecordGateway {

}
