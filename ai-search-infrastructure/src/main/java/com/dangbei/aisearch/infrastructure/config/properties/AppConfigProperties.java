package com.dangbei.aisearch.infrastructure.config.properties;

import com.alibaba.fastjson2.JSON;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * App 配置项
 * <AUTHOR>
 * @date 2025-02-25 14:43
 **/
@Data
@Component
@NacosConfigurationProperties(dataId = "${nacos.config.data-id:com.dangbei.ai-search.application.yaml}",
    groupId = "${nacos.config.group:ai-search}",
    prefix = "app-config",
    autoRefreshed = true)
public class AppConfigProperties {

    private String config;

    /**
     * 提取 ws 的 appKey
     * @return ws 的 appKey
     */
    public String extractWsAppKey() {
        return Optional.ofNullable(config)
            .map(JSON::parseObject)
            .map(conf -> conf.getJSONObject("ws"))
            .map(wsConfig -> wsConfig.getString("appKey"))
            .orElse(null);
    }

}
