package com.dangbei.aisearch.app.executor;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.exception.Assert;
import com.alibaba.cola.exception.BizException;
import com.dangbei.aisearch.app.util.UserContextUtil;
import com.dangbei.aisearch.client.dto.cmd.JoinSharedKnowledgeCmd;
import com.dangbei.aisearch.client.enums.SharedKnowledgeRoleEnum;
import com.dangbei.aisearch.domain.entity.SharedKnowledgeEntity;
import com.dangbei.aisearch.domain.entity.UserInfoEntity;
import com.dangbei.aisearch.domain.gateway.SharedKnowledgeGateway;
import com.dangbei.aisearch.domain.gateway.UserInfoGateway;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 直接加入知识库执行器
 * <AUTHOR>
 * @date 2025-05-27
 **/
@Slf4j
@Component
public class JoinSharedKnowledgeCmdExe {

    @Resource
    private SharedKnowledgeGateway sharedKnowledgeGateway;
    @Resource
    private UserInfoGateway userInfoGateway;

    /**
     * 执行直接加入知识库
     * @param cmd 加入命令
     * @return 处理结果
     */
    public Response execute(JoinSharedKnowledgeCmd cmd) {
        // 获取当前用户
        String userId = UserContextUtil.getNonNullUserId();
        UserInfoEntity userInfo = userInfoGateway.getByUserId(userId);
        Assert.notNull(userInfo, "用户不存在");

        // 获取知识库
        SharedKnowledgeEntity knowledge = sharedKnowledgeGateway.getByKnowledgeId(cmd.getKnowledgeId());
        Assert.notNull(knowledge, "知识库不存在");

        // 验证是否已经是成员
        if (knowledge.isMember(userId)) {
            throw new BizException("已经是该知识库的成员");
        }

        // 验证知识库是否需要审批
        if (knowledge.getJoinApprovalRequired() == 1) {
            throw new BizException("该知识库需要审批才能加入，请提交申请");
        }

        // 直接加入知识库
        knowledge.addMember(userId, SharedKnowledgeRoleEnum.MEMBER, null);

        return Response.buildSuccess();
    }
}
