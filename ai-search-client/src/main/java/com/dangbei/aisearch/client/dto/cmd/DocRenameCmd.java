package com.dangbei.aisearch.client.dto.cmd;

import com.alibaba.cola.dto.Command;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2025-03-17 21:18
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class DocRenameCmd extends Command {

    @Schema(description = "文档Id")
    @NotBlank(message = "文档Id不能为空")
    private String docId;

    @Schema(description = "新文档名称")
    @NotBlank(message = "文档名称不能为空")
    private String newDocName;
}
