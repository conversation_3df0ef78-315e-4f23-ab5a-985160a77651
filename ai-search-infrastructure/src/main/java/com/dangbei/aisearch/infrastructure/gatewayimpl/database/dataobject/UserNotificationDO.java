package com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.dangbei.aisearch.client.dto.NotificationJumpConfig;
import com.dangbei.aisearch.infrastructure.common.base.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * UserNotification DO对象
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-05-27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "ais_user_notification", autoResultMap = true)
public class UserNotificationDO extends BaseDO<Long> {

    @Schema(description = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "类型(1-普通文本,2-审批)")
    @TableField(value = "type")
    private Integer type;

    @Schema(description = "业务类型(1-知识库加入申请,2-知识库申请结果)")
    @TableField(value = "biz_type")
    private Integer bizType;

    @Schema(description = "发送用户ID")
    @TableField(value = "sender_id")
    private String senderId;

    @Schema(description = "接收用户ID")
    @TableField(value = "user_id")
    private String userId;

    @Schema(description = "标题")
    @TableField(value = "title")
    private String title;

    @Schema(description = "内容")
    @TableField(value = "content")
    private String content;

    @Schema(description = "关联ID")
    @TableField(value = "related_id")
    private String relatedId;

    @Schema(description = "跳转配置JSON")
    @TableField(value = "jump_config", typeHandler = Fastjson2TypeHandler.class)
    private NotificationJumpConfig jumpConfig;

    @Schema(description = "是否已读(0-未读,1-已读)")
    @TableField(value = "is_read")
    private Integer isRead;

    @Schema(description = "阅读时间")
    @TableField(value = "read_time")
    private LocalDateTime readTime;

    @Override
    public Long getPrimaryId() {
        return this.id;
    }

    @Override
    public void setPrimaryId(Long id) {
        this.id = id;
    }
}
