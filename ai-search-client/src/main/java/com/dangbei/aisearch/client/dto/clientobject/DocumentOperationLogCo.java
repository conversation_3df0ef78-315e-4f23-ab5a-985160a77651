package com.dangbei.aisearch.client.dto.clientobject;

import com.alibaba.cola.dto.DTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文档操作日志客户端对象
 * <AUTHOR>
 * @date 2025-05-27
 **/
@Data
@Builder
@EqualsAndHashCode(callSuper = true)
public class DocumentOperationLogCo extends DTO {

    @Schema(description = "操作ID")
    private String operationId;

    @Schema(description = "知识库ID")
    private String knowledgeId;

    @Schema(description = "知识库名称")
    private String knowledgeName;

    @Schema(description = "文档ID")
    private String docId;

    @Schema(description = "文档名称")
    private String docName;

    @Schema(description = "操作类型：1-上传，2-删除，3-重命名，4-复制，5-移动")
    private Integer operationType;

    @Schema(description = "操作类型名称")
    private String operationTypeName;

    @Schema(description = "操作人用户ID")
    private String operatorUserId;

    @Schema(description = "操作人昵称")
    private String operatorNickname;

    @Schema(description = "操作人头像")
    private String operatorAvatar;

    @Schema(description = "操作时间")
    private String operationTime;

    @Schema(description = "操作详情描述")
    private String operationDetail;

    @Schema(description = "源知识库ID（复制/移动操作时）")
    private String sourceKnowledgeId;

    @Schema(description = "源知识库名称（复制/移动操作时）")
    private String sourceKnowledgeName;

    @Schema(description = "目标知识库ID（复制/移动操作时）")
    private String targetKnowledgeId;

    @Schema(description = "目标知识库名称（复制/移动操作时）")
    private String targetKnowledgeName;

    @Schema(description = "操作结果：1-成功，2-失败")
    private Integer operationResult;

    @Schema(description = "失败原因（操作失败时）")
    private String failureReason;

} 