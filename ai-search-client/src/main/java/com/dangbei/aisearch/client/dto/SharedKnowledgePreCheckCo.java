package com.dangbei.aisearch.client.dto;

import com.alibaba.cola.dto.DTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 共享知识库上传前置检查返回对象
 * <AUTHOR>
 * @date 2025-05-27
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SharedKnowledgePreCheckCo extends DTO {

    @Schema(description = "是否通过预检")
    private Boolean pass;

    @Schema(description = "不通过原因")
    private String reason;
}
