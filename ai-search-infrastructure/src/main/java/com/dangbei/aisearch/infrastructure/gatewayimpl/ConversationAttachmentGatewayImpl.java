package com.dangbei.aisearch.infrastructure.gatewayimpl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dangbei.aisearch.domain.entity.ConversationAttachmentEntity;
import com.dangbei.aisearch.domain.gateway.ConversationAttachmentGateway;
import com.dangbei.aisearch.infrastructure.common.base.BaseGatewayImpl;
import com.dangbei.aisearch.infrastructure.convertor.ConversationAttachmentConvertor;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.ConversationAttachmentDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.ConversationAttachmentMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * ConversationAttachment 网关层实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-04-21
 */
@Component
public class ConversationAttachmentGatewayImpl extends BaseGatewayImpl<Long, ConversationAttachmentEntity, ConversationAttachmentDO, ConversationAttachmentMapper, ConversationAttachmentConvertor> implements ConversationAttachmentGateway {

    @Resource
    private ConversationAttachmentConvertor convertor;
    @Resource
    private ConversationAttachmentMapper mapper;

    @Override
    public List<ConversationAttachmentEntity> listByDocIds(List<String> docIds) {
        if (CollUtil.isEmpty(docIds)) {
            return List.of();
        }
        LambdaUpdateWrapper<ConversationAttachmentDO> wrapper = Wrappers.lambdaUpdate();
        wrapper.in(ConversationAttachmentDO::getDocId, docIds);
        return new ArrayList<>(convertor.toEntityList(mapper.selectList(wrapper)));
    }

    @Override
    public ConversationAttachmentEntity getByMd5(String md5) {
        LambdaQueryWrapper<ConversationAttachmentDO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ConversationAttachmentDO::getMd5, md5);
        wrapper.last(LIMIT_ONE);
        return convertor.toEntity(mapper.selectOne(wrapper));
    }

}
