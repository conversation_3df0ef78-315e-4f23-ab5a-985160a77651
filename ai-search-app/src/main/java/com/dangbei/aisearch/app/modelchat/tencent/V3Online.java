package com.dangbei.aisearch.app.modelchat.tencent;

import com.alibaba.cola.extension.Extension;
import com.dangbei.aisearch.app.model.ChatContext;
import com.dangbei.aisearch.app.model.MessageModel;
import com.dangbei.aisearch.common.constant.CommonConst;
import com.dangbei.aisearch.common.enums.I18nValueEnum;
import com.dangbei.aisearch.common.enums.MsgContentTypeEnum;
import com.dangbei.aisearch.infrastructure.config.properties.DashScopeProperties;
import com.dangbei.aisearch.infrastructure.i18n.I18nUtil;
import com.dangbei.aisearch.infrastructure.prompt.PromptParam;
import com.dangbei.aisearch.infrastructure.prompt.PromptUtil;
import com.theokanning.openai.completion.chat.ChatCompletionRequest;
import com.theokanning.openai.completion.chat.ChatMessage;
import com.theokanning.openai.completion.chat.SystemMessage;
import com.theokanning.openai.completion.chat.UserMessage;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.LinkedList;

/**
 * V3 + 联网
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-24
 */
@Slf4j
@Component("v3_online_tencent_model_chat")
@Extension(bizId = CommonConst.ModelIntent.DEEPSEEK_ONLINE_TENCENT)
public class V3Online extends AbstractTencentModelChatExt {

    @Resource
    protected DashScopeProperties dashScopeProperties;

    @Override
    @SneakyThrows
    protected void preHandle(ChatContext ctx) {
        MessageModel.Msg notice = ctx.newSseMsg(MsgContentTypeEnum.PROGRESS, I18nUtil.get(I18nValueEnum.CHAT_WEB_SEARCHING));
        ctx.sendDeltaAnswerSSE(notice);
    }

    @Override
    protected ChatCompletionRequest buildParam(ChatContext ctx) {
        String sysPrompt = promptUtil.format(PromptParam.builder()
            .promptTmpl(dashScopeProperties.getChatDefaultConfig().getSystem())
            .question(ctx.getChatCmd().getQuestion())
            .ip(ctx.getIp())
            .build());

        // 上下文记忆
        LinkedList<ChatMessage> messages = new LinkedList<>();
        messages.addFirst(new SystemMessage(sysPrompt));
        messages.addAll(filterMemoryTurns(ctx.getHistory(), 6));
        messages.add(new UserMessage(ctx.getChatCmd().getQuestion()));

        ChatCompletionRequest build = ChatCompletionRequest.builder()
            .model("deepseek-v3")
            .messages(messages)
            .build();
        build.setEnableCustomSearch(Boolean.TRUE);
        return build;
    }

}
