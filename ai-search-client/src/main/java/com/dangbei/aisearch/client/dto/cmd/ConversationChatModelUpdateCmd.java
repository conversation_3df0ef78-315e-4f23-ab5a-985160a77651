package com.dangbei.aisearch.client.dto.cmd;

import com.alibaba.cola.dto.Command;
import com.dangbei.aisearch.client.dto.ChatModelConfig;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 会话模型配置更新命令
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConversationChatModelUpdateCmd extends Command {

    @NotBlank(message = "会话ID不能为空")
    @Schema(description = "会话ID")
    private String conversationId;

    @NotNull(message = "聊天模型配置不能为空")
    @Schema(description = "聊天模型配置")
    private ChatModelConfig chatModelConfig;
}
