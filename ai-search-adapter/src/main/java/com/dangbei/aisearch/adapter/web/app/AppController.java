package com.dangbei.aisearch.adapter.web.app;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson2.JSONObject;
import com.dangbei.aisearch.app.service.AppService;
import com.dangbei.aisearch.app.service.ConfigService;
import com.dangbei.aisearch.client.dto.clientobject.AppUpgradeCo;
import com.dangbei.aisearch.client.dto.clientobject.VoiceTypePreviewCo;
import com.dangbei.aisearch.client.dto.cmd.AppSendSmsCmd;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2025-02-25 16:11
 **/
@RestController
@RequiredArgsConstructor
@RequestMapping("/app/v1/")
@Tag(name = "AppController", description = "app 相关接口")
public class AppController {

    private final AppService appService;
    private final ConfigService configService;

    @PostMapping("sendSms")
    @Operation(summary = "APP发送短信", description = "APP发送短信")
    public Response appSendSms(@Valid @RequestBody AppSendSmsCmd appSendSmsCmd) {
        return appService.sendSms(appSendSmsCmd);
    }

    @GetMapping("upgrade/check")
    @Operation(summary = "检查app更新")
    public SingleResponse<AppUpgradeCo> appCheckUpdate() {
        return SingleResponse.of(appService.getLatestAppUpgrade());
    }

    @GetMapping("config")
    @Operation(summary = "APP 配置", description = "获取 app 配置")
    public SingleResponse<JSONObject> getAppConfig() {
        return configService.getAppConfig();
    }

}
