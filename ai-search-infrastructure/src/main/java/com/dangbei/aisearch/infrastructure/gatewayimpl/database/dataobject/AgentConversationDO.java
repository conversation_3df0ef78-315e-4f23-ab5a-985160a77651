package com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dangbei.aisearch.infrastructure.common.base.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * AgentConversation DO对象
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-28
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("ais_agent_conversation")
public class AgentConversationDO extends BaseDO<Long> {

    @Schema(description = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "会话ID")
    @TableField(value = "conversation_id")
    private String conversationId;

    @Schema(description = "智能体唯一ID")
    @TableField(value = "agent_id")
    private String agentId;

    @Schema(description = "用户ID")
    @TableField(value = "user_id")
    private String userId;

    @Schema(description = "设备ID")
    @TableField(value = "device_id")
    private String deviceId;

    @Schema(description = "上下文消息ID")
    @TableField(value = "context_msg_id")
    private String contextMsgId;

    @Schema(description = "其他信息")
    @TableField(value = "meta_data")
    private String metaData;

    @Schema(description = "上次对话时间")
    @TableField(value = "last_chat_time")
    private LocalDateTime lastChatTime;

    @Schema(description = "应用类型(1:安卓,2:ios,3:harmony,4:mac,5:win,6:web,7:h5)")
    @TableField(value = "app_type")
    private Integer appType;

    @Override
    public Long getPrimaryId() {
        return this.id;
    }

    @Override
    public void setPrimaryId(Long id) {
        this.id = id;
    }
}
