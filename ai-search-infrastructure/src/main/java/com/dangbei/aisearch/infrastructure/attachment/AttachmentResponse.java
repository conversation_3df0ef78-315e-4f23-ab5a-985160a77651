package com.dangbei.aisearch.infrastructure.attachment;

import com.alibaba.cola.dto.DTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-22 13:31
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class AttachmentResponse extends DTO {

    @Schema(description = "文件附件搜索结果")
    private List<FileAttachmentResult> fileAttachmentResults;

    @Schema
    private ImageAttachmentResult imageAttachmentResult;

    @Data
    @Accessors(chain = true)
    public static class FileAttachmentResult {
        private AttachmentMeta meta;
        private String summary;
    }

    @Data
    @Accessors(chain = true)
    public static class ImageAttachmentResult {
        private List<AttachmentMeta> metas;
        private String comprehension;
    }

    @Data
    @Accessors(chain = true)
    public static class AttachmentMeta {
        private String docId;
        private String docName;
    }


}
