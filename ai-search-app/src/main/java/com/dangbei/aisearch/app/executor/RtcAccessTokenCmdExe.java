package com.dangbei.aisearch.app.executor;

import cn.hutool.core.convert.Convert;
import com.alibaba.cola.dto.SingleResponse;
import com.dangbei.aisearch.app.util.UserDeviceUtil;
import com.dangbei.aisearch.client.dto.clientobject.RtcAccessTokenCo;
import com.dangbei.aisearch.client.dto.cmd.RtcAccessTokenCmd;
import com.dangbei.aisearch.common.util.RtcAccessToken;
import com.dangbei.aisearch.common.util.RtcUtils;
import com.dangbei.aisearch.infrastructure.config.properties.VolcRtcProperties;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * RTC生成token命令执行器
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-24
 */
@Component
public class RtcAccessTokenCmdExe {

    @Resource
    private VolcRtcProperties volcRtcProperties;

    public SingleResponse<RtcAccessTokenCo> execute(RtcAccessTokenCmd cmd) {
        RtcAccessToken token = new RtcAccessToken(volcRtcProperties.getAppId(), volcRtcProperties.getAppKey(), cmd.getRoomId(), UserDeviceUtil.getUserIdDefaultDeviceId());
        token.ExpireTime(RtcUtils.getTimestamp() + 3600);
        token.AddPrivilege(RtcAccessToken.Privileges.PrivSubscribeStream, 0);
        token.AddPrivilege(RtcAccessToken.Privileges.PrivPublishStream, RtcUtils.getTimestamp() + 3600);

        RtcAccessTokenCo tokenInfo = new RtcAccessTokenCo()
            .setAccessToken(token.Serialize())
            .setIssuedAt(Convert.toLong(token.issuedAt))
            .setExpireAt(Convert.toLong(token.expireAt))
            .setAppId(token.appID)
            .setRoomId(token.roomID)
            .setUserId(token.userID);
        return SingleResponse.of(tokenInfo);
    }

    public static void main(String[] args) {
        RtcAccessToken token = new RtcAccessToken("6763e03d5b67c401633aab1d", "db2cfb52ac79452da6fd25bfe7eb5a36", "1004", "1004");
        token.ExpireTime(RtcUtils.getTimestamp() + 3600);
        token.AddPrivilege(RtcAccessToken.Privileges.PrivSubscribeStream, 0);
        token.AddPrivilege(RtcAccessToken.Privileges.PrivPublishStream, RtcUtils.getTimestamp() + 3600);


        String s = token.Serialize();
        System.out.println(s);

        System.out.println(token);

        RtcAccessToken t = RtcAccessToken.Parse(s);
        System.out.println(t);
        System.out.println(t.Verify("db2cfb52ac79452da6fd25bfe7eb5a36"));

    }

}
