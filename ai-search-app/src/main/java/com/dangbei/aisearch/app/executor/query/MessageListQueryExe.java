package com.dangbei.aisearch.app.executor.query;

import cn.dev33.satoken.exception.NotPermissionException;
import cn.hutool.json.JSONUtil;
import com.alibaba.cola.common.enums.YesOrNo;
import com.alibaba.cola.common.util.BeanCopyUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson2.JSON;
import com.dangbei.aisearch.app.util.UserDeviceUtil;
import com.dangbei.aisearch.client.dto.clientobject.MessageCo;
import com.dangbei.aisearch.client.dto.clientobject.MessageListCo;
import com.dangbei.aisearch.client.dto.cmd.ChatCmd;
import com.dangbei.aisearch.client.dto.cmd.query.MessageListQuery;
import com.dangbei.aisearch.client.enums.KnowledgeTypeEnum;
import com.dangbei.aisearch.common.enums.RoleEnum;
import com.dangbei.aisearch.domain.entity.AgentConversationEntity;
import com.dangbei.aisearch.domain.entity.AgentEntity;
import com.dangbei.aisearch.domain.entity.ChatMessageEntity;
import com.dangbei.aisearch.domain.entity.ConversationEntity;
import com.dangbei.aisearch.domain.entity.SharedKnowledgeEntity;
import com.dangbei.aisearch.domain.entity.UserKnowledgeEntity;
import com.dangbei.aisearch.domain.gateway.AgentConversationGateway;
import com.dangbei.aisearch.domain.gateway.AgentGateway;
import com.dangbei.aisearch.domain.gateway.ChatMessageGateway;
import com.dangbei.aisearch.domain.gateway.ConversationGateway;
import com.dangbei.aisearch.domain.gateway.SharedKnowledgeGateway;
import com.dangbei.aisearch.domain.gateway.UserKnowledgeGateway;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 消息查询命令执行器
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-25
 */
@Component
public class MessageListQueryExe {

    @Resource
    private ConversationGateway conversationGateway;
    @Resource
    private ChatMessageGateway chatMessageGateway;
    @Resource
    private AgentConversationGateway agentConversationGateway;
    @Resource
    private AgentGateway agentGateway;
    @Resource
    private SharedKnowledgeGateway sharedKnowledgeGateway;
    @Resource
    private UserKnowledgeGateway userKnowledgeGateway;

    public SingleResponse<MessageListCo> execute(MessageListQuery query) {
        String conversationTitle = null;
        Integer titleSummaryFlag = YesOrNo.YES.getValue();
        if (StringUtils.isBlank(query.getAgentId())) {
            ConversationEntity conversationEntity = conversationGateway.getByConversationId(query.getConversationId());
            if (Objects.isNull(conversationEntity) || !UserDeviceUtil.isOwn(conversationEntity.getUserId(), conversationEntity.getDeviceId())) {
                throw new NotPermissionException("会话不匹配");
            }
            conversationTitle = conversationEntity.getTitle();
            titleSummaryFlag = conversationEntity.getTitleSummaryFlag();
            // 匿名模式校验
            anonymousModeCheck(conversationEntity, query);
        } else {
            AgentConversationEntity agentConversationEntity = agentConversationGateway.getByConversationId(query.getConversationId());
            if (Objects.isNull(agentConversationEntity) || !UserDeviceUtil.isOwn(agentConversationEntity.getUserId(), agentConversationEntity.getDeviceId())) {
                throw new NotPermissionException("会话不匹配");
            }
            AgentEntity agentEntity = agentGateway.getByAgentId(agentConversationEntity.getAgentId());
            if (Objects.nonNull(agentEntity)) {
                conversationTitle = agentEntity.getName();
            }
        }
        List<ChatMessageEntity> list = chatMessageGateway.query(query);
        Long count = chatMessageGateway.count(query);
        return SingleResponse.of(getMessageListCo(list, count, query.getLimit(), conversationTitle, titleSummaryFlag));
    }

    private void anonymousModeCheck(ConversationEntity conversationEntity, MessageListQuery query) {
        if (conversationEntity.isAnonymous()) {
            if (StringUtils.isBlank(query.getAnonymousKey()) || !Objects.equals(conversationEntity.getAnonymousKey(), query.getAnonymousKey())) {
                throw new NotPermissionException("会话不匹配");
            }
        }
    }

    public MessageListCo getMessageListCo(List<ChatMessageEntity> list, Long count, Integer queryCount, String conversationTitle, Integer titleSummaryFlag) {
        MessageListCo resultCo = new MessageListCo();
        resultCo.setConversationTitle(conversationTitle);
        resultCo.setTitleSummaryFlag(titleSummaryFlag);
        if (CollectionUtils.isEmpty(list) || count <= 0) {
            resultCo.setHasMore(false);
            resultCo.setMsgList(Lists.newArrayList());
            return resultCo;
        }
        resultCo.setHasMore(CollectionUtils.size(list) >= queryCount);
        resultCo.setFirstMsgId(list.get(0).getMsgId());
        resultCo.setLastMsgId(list.get(list.size() - 1).getMsgId());

        resultCo.setMsgList(getMsgList(list));
        return resultCo;
    }

    public List<MessageCo> getMsgList(List<ChatMessageEntity> list) {
        return list.stream().map(item -> {
            MessageCo messageCo = BeanCopyUtil.copyOf(item, MessageCo::new);

            // 处理附件、引用
            if (Objects.equals(item.getRole(), RoleEnum.USER.getRoleType())) {
                if (JSONUtil.isTypeJSONArray(item.getFiles())) {
                    messageCo.setFiles(JSON.parseArray(item.getFiles(), ChatCmd.FileItem.class));
                }
                if (Objects.nonNull(item.getExt()) && JSONUtil.isTypeJSONArray(item.getExt().getReferences())) {
                    messageCo.setReference(JSON.parseArray(item.getExt().getReferences(), ChatCmd.ReferenceItem.class));
                }
                if (JSONUtil.isTypeJSONArray(item.getKnowledgeList())) {
                    List<ChatCmd.KnowledgeItem> knowledgeItems = JSON.parseArray(item.getKnowledgeList(), ChatCmd.KnowledgeItem.class);
                    // 处理知识库名称可能会发生变化的情况，从数据库查询最新的名称
                    if (CollectionUtils.isNotEmpty(knowledgeItems)) {
                        knowledgeItems.forEach(this::updateKnowledgeName);
                    }
                    messageCo.setKnowledgeList(knowledgeItems);
                }
            }

            // 不透出模型usage
            if (Objects.nonNull(messageCo.getExt())) {
                messageCo.getExt().setTokenUsageCo(null);
            }

            return messageCo;
        }).collect(Collectors.toList());
    }

    /**
     * 根据知识库ID和类型获取最新的知识库名称
     * @param knowledgeItem 知识库项
     */
    private void updateKnowledgeName(ChatCmd.KnowledgeItem knowledgeItem) {
        try {
            if (Objects.isNull(knowledgeItem.getKnowledgeType()) 
                || StringUtils.isBlank(knowledgeItem.getKnowledgeId())) {
                return;
            }

            // 根据知识库类型选择查询方式
            if (KnowledgeTypeEnum.isPersonal(knowledgeItem.getKnowledgeType())) {
                // 个人知识库
                UserKnowledgeEntity entity = userKnowledgeGateway.getByKnowledgeId(knowledgeItem.getKnowledgeId());
                if (Objects.nonNull(entity) && StringUtils.isNotBlank(entity.getName())) {
                    knowledgeItem.setName(entity.getName());
                }
            } else if (KnowledgeTypeEnum.isShared(knowledgeItem.getKnowledgeType())) {
                // 共享知识库
                SharedKnowledgeEntity entity = sharedKnowledgeGateway.getByKnowledgeId(knowledgeItem.getKnowledgeId());
                if (Objects.nonNull(entity) && StringUtils.isNotBlank(entity.getName())) {
                    knowledgeItem.setName(entity.getName());
                }
            }
        } catch (Exception e) {
            // 获取名称失败，使用原有名称
        }
    }
}
