package com.dangbei.aisearch.domain.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.dangbei.aisearch.domain.common.base.BaseEntity;
import com.dangbei.aisearch.domain.common.base.BaseGateway;
import com.dangbei.aisearch.domain.gateway.FeedbackGateway;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * Feedback 领域对象
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(doNotUseGetters = true)
public class FeedbackEntity extends BaseEntity<Long> {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "内容")
    private String content;

    @Schema(description = "图片url列表")
    private List<String> imageUrls;

    private transient FeedbackGateway feedbackGateway = SpringUtil.getBean(FeedbackGateway.class);

    @Override
    public Long getPrimaryId() {
        return this.id;
    }

    @Override
    public void setPrimaryId(Long id) {
        this.id = id;
    }

    @Override
    protected BaseGateway getGateWay() {
        return this.feedbackGateway;
    }
}
