package com.dangbei.aisearch.infrastructure.knowledge.execute;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-03-07 13:39
 **/
@Slf4j
public class ApacheHttpExecutor {

    public static String executePost(String url,
                                    Map<String, String> headers,
                                    String jsonBody) throws IOException {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(url);

            // 设置通用请求头
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setHeader("Accept", "application/json");

            // 添加自定义请求头
            headers.forEach(httpPost::setHeader);

            // 设置请求体
            httpPost.setEntity(new StringEntity(jsonBody, StandardCharsets.UTF_8));

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                int statusCode = response.getStatusLine().getStatusCode();
                String responseBody = EntityUtils.toString(response.getEntity(), "UTF-8");

                if (log.isDebugEnabled()) {
                    if (statusCode >= 200 && statusCode < 300) {
                        log.debug("请求成功: {}", responseBody);
                    } else {
                        log.debug("请求失败[{}]: {}", statusCode, responseBody);
                    }
                }
                return responseBody;
            }
        }
    }
}
