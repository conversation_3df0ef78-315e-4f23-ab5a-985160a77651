//package com.dangbei.aisearch.adapter.web;
//
//import com.alibaba.cola.dto.MultiResponse;
//import com.dangbei.aisearch.app.executor.query.BotListAllQueryExe;
//import com.dangbei.aisearch.client.dto.clientobject.BotInfoCo;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//
///**
// * 智能体服务接口
// * <AUTHOR> href="<EMAIL>"><EMAIL></a>
// * @version 1.0.0
// * @since 2024-12-26
// */
//
//@RestController
//@RequestMapping("/botApi/v1")
//@Tag(name = "BotController", description = "智能体服务")
//public class BotController {
//
//    @Resource
//    private BotListAllQueryExe botListAllQueryExe;
//
//    @PostMapping("/listAll")
//    @Operation(summary = "获取所有智能体", description = "获取所有智能体")
//    public MultiResponse<BotInfoCo> listAll() {
//        return botListAllQueryExe.execute();
//    }
//
//}
