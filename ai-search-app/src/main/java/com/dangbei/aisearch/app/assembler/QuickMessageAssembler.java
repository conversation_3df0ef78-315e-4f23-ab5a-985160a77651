package com.dangbei.aisearch.app.assembler;

import com.dangbei.aisearch.domain.entity.ChatMessageEntity;
import com.dangbei.aisearch.domain.entity.QuickChatMessageEntity;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

import java.util.List;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-11
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface QuickMessageAssembler {

    List<QuickChatMessageEntity> toQuickEntityList(List<ChatMessageEntity> chatMessageEntityList);

    List<ChatMessageEntity> toEntityList(List<QuickChatMessageEntity> quickChatMessageEntityList);
}
