package com.dangbei.aisearch.infrastructure.monitor;

import cn.hutool.extra.spring.SpringUtil;
import com.dangbei.framework.insight.redis.util.RedisUtil;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Token监控
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-19
 */
public class TokenMonitor {
    private final Map<Long, Integer> secondTokenMap = new ConcurrentHashMap<>();
    private final Map<Long, Integer> minuteTokenMap = new ConcurrentHashMap<>();
    public static final Map<String, AtomicLong> requestCounters = new ConcurrentHashMap<>();

    public static void reportTokenConsumption(String model, String requestId, long timestamp, int costToken) {
        long sequence = requestCounters.computeIfAbsent(requestId, k -> new AtomicLong(0)).incrementAndGet();

        // 构造唯一键
        String detailKey = String.format("token:detail:%s:%s:%d:%d", model, requestId, timestamp, sequence);


        RedisUtil.hSet(detailKey, "", costToken);

        // Lua 脚本（原子性计算增量）
        String LUA_SCRIPT = """
            local key = KEYS[1]
            local current = tonumber(ARGV[1])
            local ts = tonumber(ARGV[2])
            local last = tonumber(redis.call('HGET', key, 'cost')) or 0
            local delta = current - last
            redis.call('HSET', key, 'cost', current)
            redis.call('HSET', key, 'ts', ts)
            redis.call('EXPIRE', key, 86400)
            return tostring(delta)
            """;
        DefaultRedisScript<String> script = new DefaultRedisScript<>(LUA_SCRIPT, String.class);
        List<String> keys = Collections.singletonList(requestId);
        Object[] args = {String.valueOf(costToken), String.valueOf(timestamp)};

        // 执行 Lua 脚本
        RedisTemplate redisTemplate = SpringUtil.getBean("redisTemplate", RedisTemplate.class);
        String deltaStr = (String) redisTemplate.execute(script, keys, args);
        long delta = Long.parseLong(deltaStr);

        System.out.println("delta:" + delta);

        // 写入时序数据库
//        tsDB.write(new DataPoint(
//            data.timestamp,
//            delta,
//            Map.of("requestId", data.requestId)
//        ));
    }

    public int getSecondTokenConsumption(long timestamp) {
        long secondWindow = timestamp / 1000 * 1000;
        return secondTokenMap.getOrDefault(secondWindow, 0);
    }

    public int getMinuteTokenConsumption(long timestamp) {
        long minuteWindow = timestamp / 60000 * 60000;
        return minuteTokenMap.getOrDefault(minuteWindow, 0);
    }

}
