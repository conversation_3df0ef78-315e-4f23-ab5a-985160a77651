package com.dangbei.aisearch.domain.gateway;

import com.dangbei.aisearch.common.enums.ShareSourceEnum;
import com.dangbei.aisearch.domain.common.base.BaseGateway;
import com.dangbei.aisearch.domain.entity.ShareEntity;

/**
 * Share 网关层
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-06
 */
public interface ShareGateway extends BaseGateway<Long, ShareEntity> {

    /**
     * 根据分享ID查询
     * @param shareId 分享ID
     * @return {@link ShareEntity }
     */
    ShareEntity getByShareId(String shareId);

    /**
     * 生成分享ID
     * @return {@link String }
     */
    String genShareId(ShareSourceEnum sourceEnum);

    /**
     * 递增点赞数
     * @param shareId 分享ID
     * @return boolean
     */
    boolean incrLikeNum(String shareId);

    /**
     * 递减点赞数
     * @param shareId 分享ID
     */
    boolean decrLikeNum(String shareId);

    /**
     * 根据知识库id查询
     * @param knowledgeId 知识库id
     * @return {@link ShareEntity }
     */
    ShareEntity getByKnowledgeId(String knowledgeId);
}
