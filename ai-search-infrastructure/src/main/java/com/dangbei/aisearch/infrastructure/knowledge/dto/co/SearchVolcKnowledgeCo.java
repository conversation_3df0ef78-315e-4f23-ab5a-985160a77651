package com.dangbei.aisearch.infrastructure.knowledge.dto.co;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-07 13:49
 **/
@Data
public class SearchVolcKnowledgeCo implements Serializable {

    @JSONField(name = "collection_name")
    private String collectionName;

    @JSONField(name = "token_usage")
    private TokenUsage tokenUsage;

    @JSONField(name = "count")
    private Integer count;

    @J<PERSON>NField(name = "rewrite_query")
    private String rewriteQuery;

    @JSONField(name = "result_list")
    private List<SearchResult> resultList;


    @Data
    public static class SearchResult {

        @JSONField(name = "id")
        private String id;

        @JSONField(name = "point_id")
        private String pointId;

        @JSONField(name = "content")
        private String content;

        @JSONField(name = "recall_position")
        private String recallPosition;

        @JSONField(name = "score")
        private Double score;

        @JSONField(name = "chunk_id")
        private Integer chunkId;

        @JSONField(name = "chunk_title")
        private String chunkTitle;

        @JSONField(name = "chunk_type")
        private String chunkType;

        @JSONField(name = "chunk_source")
        private String chunkSource;

        @JSONField(name = "chunk_attachment")
        private List<ChunkAttachment> chunkAttachment;

        @JSONField(name = "doc_info")
        private DocInfo docInfo;

        @JSONField(name = "process_time")
        private Long process_time;

        @JSONField(name = "rerank_score")
        private Double rerankScore;

        @JSONField(name = "update_time")
        private Long updateTime;

        @Data
        public static class DocInfo {
            @JSONField(name = "doc_id")
            private String docId;

            @JSONField(name = "doc_name")
            private String docName;

            @JSONField(name = "title")
            private String title;

            @JSONField(name = "source")
            private String source;

            @JSONField(name = "doc_meta")
            private String docMeta;

            @JSONField(name = "doc_type")
            private String docType;

            @JSONField(name = "create_time")
            private Long createTime;
        }

        @Data
        private static class ChunkAttachment {

            @JSONField(name = "caption")
            private String caption;

            @JSONField(name = "type")
            private String type;

            @JSONField(name = "uuid")
            private String uuid;
        }

    }

    @Data
    public static class TokenUsage {

        @JSONField(name = "rerank_token_usage")
        private String rerankTokenUsage;

        @Data
        public static class EmbeddingTokenUsage {
            @JSONField(name = "completion_tokens")
            private Integer completionTokens;
            @JSONField(name = "prompt_tokens")
            private Integer promptTokens;
            @JSONField(name = "total_tokens")
            private Integer totalTokens;
        }
    }
}
