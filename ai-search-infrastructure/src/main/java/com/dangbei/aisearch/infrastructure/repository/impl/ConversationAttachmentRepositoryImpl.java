package com.dangbei.aisearch.infrastructure.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.ConversationAttachmentDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.ConversationAttachmentMapper;
import com.dangbei.aisearch.infrastructure.repository.ConversationAttachmentRepository;
import org.springframework.stereotype.Repository;

/**
 * ConversationAttachment 仓库实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-04-21
 */
@Repository
public class ConversationAttachmentRepositoryImpl extends ServiceImpl<ConversationAttachmentMapper, ConversationAttachmentDO> implements ConversationAttachmentRepository {

}
