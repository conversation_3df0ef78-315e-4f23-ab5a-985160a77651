package com.dangbei.aisearch.domain.gateway;

import com.dangbei.aisearch.client.dto.NotificationJumpConfig;
import com.dangbei.aisearch.client.enums.NotificationBizTypeEnum;
import com.dangbei.aisearch.client.enums.NotificationTypeEnum;
import com.dangbei.aisearch.domain.common.base.BaseGateway;
import com.dangbei.aisearch.domain.entity.UserNotificationEntity;

import java.util.Collection;

/**
 * UserNotification 网关层
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-05-27
 */
public interface UserNotificationGateway extends BaseGateway<Long, UserNotificationEntity> {

    /**
     * 发送通知
     * @param type       通知类型
     * @param bizType    通知业务类型
     * @param senderId   发送人id
     * @param userId     接收用户ID
     * @param title      标题
     * @param content    内容
     * @param relatedId  关联ID
     * @param jumpConfig 跳转配置JSON
     * @return boolean
     */
    boolean send(NotificationTypeEnum type,
                 NotificationBizTypeEnum bizType,
                 String senderId,
                 String userId,
                 String title,
                 String content,
                 String relatedId,
                 NotificationJumpConfig jumpConfig);

    /**
     * 发送文本通知
     * @param bizType    通知业务类型
     * @param senderId   发送人id
     * @param userId     接收用户ID
     * @param content    内容
     * @param relatedId  关联ID
     * @param jumpConfig 跳转配置JSON
     * @return boolean
     */
    boolean sendText(NotificationBizTypeEnum bizType,
                     String senderId,
                     String userId,
                     String content,
                     String relatedId,
                     NotificationJumpConfig jumpConfig);

    /**
     * 批量发送文本通知
     * @param bizType    通知业务类型
     * @param senderId   发送人id
     * @param userIds    接收用户ID集合
     * @param content    内容
     * @param relatedId  关联ID
     * @param jumpConfig 跳转配置JSON
     * @return boolean
     */
    boolean sendTextBatch(NotificationBizTypeEnum bizType,
                          String senderId,
                          Collection<String> userIds,
                          String content,
                          String relatedId,
                          NotificationJumpConfig jumpConfig);

    /**
     * 批量发送审批通知
     * @param bizType    我们类型
     * @param senderId   发送人id
     * @param userIds    接收用户ID集合
     * @param content    内容
     * @param relatedId  关联ID
     * @param jumpConfig 跳转配置JSON
     * @return boolean
     */
    boolean sendApprovalBatch(NotificationBizTypeEnum bizType,
                              String senderId,
                              Collection<String> userIds,
                              String content,
                              String relatedId,
                              NotificationJumpConfig jumpConfig);


}
