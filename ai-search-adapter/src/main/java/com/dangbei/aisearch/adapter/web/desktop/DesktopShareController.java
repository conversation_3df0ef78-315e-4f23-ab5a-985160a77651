package com.dangbei.aisearch.adapter.web.desktop;

import com.alibaba.cola.dto.SingleResponse;
import com.dangbei.aisearch.app.service.ShareService;
import com.dangbei.aisearch.client.dto.clientobject.QuickShareCo;
import com.dangbei.aisearch.client.dto.cmd.QuickShareCreateCmd;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-04-01
 */
@RestController
@RequestMapping("/desktop/shareApi/v1")
@Tag(name = "DesktopShareController", description = "分享控制器服务")
public class DesktopShareController {

    @Resource
    private ShareService shareService;

    @PostMapping("/createShare")
    @Operation(summary = "创建分享", description = "创建分享")
    public SingleResponse<QuickShareCo> create(@Valid @RequestBody QuickShareCreateCmd cmd) {
        return shareService.create(cmd);
    }
}
