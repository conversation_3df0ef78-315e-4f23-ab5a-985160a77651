package com.dangbei.aisearch.infrastructure.config.properties;

import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 意图识别配置类
 * <AUTHOR> href="mailto:<EMAIL>">yinyanta<PERSON>@dangbei.com</a>
 * @version 1.0.0
 * @since 2025-01-17
 */
@Data
@Component
@NacosConfigurationProperties(dataId = "${nacos.config.data-id:com.dangbei.ai-search.application.yaml}",
    groupId = "${nacos.config.group:ai-search}",
    prefix = "intent-detect", autoRefreshed = true)
public class IntentDetectProperties {


    /**
     * 模型名称
     */
    private String model = "qwen-plus";

    /**
     * 系统提示词模板
     */
    private String systemPrompt = """
        You are Qwen, created by Aliba<PERSON> Cloud. You are a helpful assistant.\s
        You should choose one tag from the tag list:
         {} just reply with the chosen tag.""";

    /**
     * 模型温度
     * temperature越高，生成的文本更多样，反之，生成的文本更确定。取值范围： [0, 2)
     * qwen-max系列、qwen-plus系列、qwen-turbo系列以及qwen开源系列、qwen-coder系列：0.7；
     * qwen-long：1.0；
     * qwen-vl-ocr：0.1；
     * qwen-vl系列：0.01；
     * qwen-math系列：0
     */
    private Float temperature = 0.3f;

    /**
     * 核采样的概率阈值，控制模型生成文本的多样性。
     * top_p越高，生成的文本更多样。反之，生成的文本更确定。取值范围：（0,1.0]
     */
    private Double topP = 0.9d;

    /**
     * 意图列表
     */
    private Map<String, String> intents = new HashMap<>();

}
