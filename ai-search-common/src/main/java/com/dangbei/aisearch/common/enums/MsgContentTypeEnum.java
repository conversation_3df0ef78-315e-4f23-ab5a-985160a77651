package com.dangbei.aisearch.common.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * .
 * <p>
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-01-07
 */
@Getter
@AllArgsConstructor
public enum MsgContentTypeEnum {

    UNKNOWN("unknown"),
    // Text.
    TEXT("text"),

    // Multimodal content, that is, a combination of text and files, or a combination of text and
    // images.
    OBJECT_STRING("object_string"),

    // Message card. This enum value only appears in the interface response and is not supported as an
    // input parameter.
    CARD("card"),

    // If there is a voice message in the input message, the conversation.audio.delta event will be
    // returned in the streaming response event.
    AUDIO("audio"),

    // If there is a voice message in the input message, the conversation.audio.delta event will be
    // returned in the streaming response event.
    PROGRESS("progress"),

    // 处理推荐问题
    FOLLOW_UP("follow_up"),

    // 思考内容
    THINKING("thinking");

    private final String value;

    @JsonValue
    public String getValue() {
        return value;
    }

}
