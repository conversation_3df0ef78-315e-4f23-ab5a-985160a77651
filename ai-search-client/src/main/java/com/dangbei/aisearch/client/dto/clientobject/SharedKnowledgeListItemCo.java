package com.dangbei.aisearch.client.dto.clientobject;

import com.alibaba.cola.dto.DTO;
import com.dangbei.aisearch.client.enums.SharedKnowledgeRoleEnum;
import com.dangbei.aisearch.client.enums.UserApplyStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 共享知识库列表项客户端对象
 * <AUTHOR>
 * @date 2025-05-27
 **/
@Data
@Builder
@EqualsAndHashCode(callSuper = true)
public class SharedKnowledgeListItemCo extends DTO {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "知识库ID")
    private String knowledgeId;

    @Schema(description = "知识库名称")
    private String name;

    @Schema(description = "封面图片URL")
    private String coverUrl;

    @Schema(description = "知识库描述")
    private String description;

    @Schema(description = "创建人名称")
    private String creatorName;

    @Schema(description = "成员数量")
    private Long memberCount;

    @Schema(description = "文档数量")
    private Long docCount;

    /**
     * {@link SharedKnowledgeRoleEnum}
     */
    @Schema(description = "角色：1-创建人，2-管理员，3-成员")
    private Integer role;

    @Schema(description = "角色名称")
    private String roleName;

    /**
     * {@link UserApplyStatusEnum}
     */
    @Schema(description = "申请状态：0-未申请，1-申请中，2-已通过，3-已拒绝")
    private Integer applyStatus;

    @Schema(description = "创建时间")
    private String createTime;

    @Schema(description = "是否需要审批加入")
    private Boolean joinApprovalRequired;

    @Schema(description = "成员是否可下载")
    private Boolean downloadEnabled;

    @Schema(description = "知识库是否包含违规内容")
    private Boolean containsIllegalContent;

}
