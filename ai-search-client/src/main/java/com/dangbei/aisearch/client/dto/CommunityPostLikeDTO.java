package com.dangbei.aisearch.client.dto;

import com.alibaba.cola.dto.DTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * .
 * <p>
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CommunityPostLikeDTO extends DTO {
    @Schema(description = "帖子ID")
    private String postId;

    @Schema(description = "点赞次数")
    private Long likeNum;
}
