package com.dangbei.aisearch.infrastructure.gatewayimpl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.common.util.NanoIdUtil;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.dangbei.aisearch.client.dto.clientobject.IpAreaCo;
import com.dangbei.aisearch.common.constant.CacheKey;
import com.dangbei.aisearch.common.constant.CommonConst;
import com.dangbei.aisearch.domain.gateway.ExternalCommonGateway;
import com.dangbei.devbase.client.dto.GreenCheckResp;
import com.dangbei.devbase.client.dto.GreenFileCheckResp;
import com.dangbei.devbase.client.dto.GreenFileResultResp;
import com.dangbei.devbase.client.dto.TencentCaptchaVerifyResp;
import com.dangbei.devbase.client.dto.TencentDocumentReviewQueryResp;
import com.dangbei.devbase.client.dto.TencentDocumentReviewSubmitResp;
import com.dangbei.devbase.client.dto.TencentTextSyncReviewResp;
import com.dangbei.devbase.client.dto.clientobject.TencentEncryptedCaptchaAppIdCo;
import com.dangbei.devbase.client.dto.cmd.CaptchaVerifyCmd;
import com.dangbei.devbase.client.dto.cmd.DingTalkCmd;
import com.dangbei.devbase.client.dto.cmd.GreenFileCheckCmd;
import com.dangbei.devbase.client.dto.cmd.GreenImageCheckCmd;
import com.dangbei.devbase.client.dto.cmd.GreenTextCheckCmd;
import com.dangbei.devbase.client.dto.cmd.SmsSendCmd;
import com.dangbei.devbase.client.dto.cmd.TencentCaptchaEncryptAppIdCmd;
import com.dangbei.devbase.client.dto.cmd.TencentCaptchaVerifyCmd;
import com.dangbei.devbase.client.dto.cmd.TencentDocumentReviewSubmitCmd;
import com.dangbei.devbase.client.dto.cmd.TencentTextSyncReviewCmd;
import com.dangbei.devbase.client.dto.cmd.query.GreenFileResultQuery;
import com.dangbei.devbase.client.dto.cmd.query.TencentDocumentReviewQuery;
import com.dangbei.devbase.client.enums.GreenTextServiceEnum;
import com.dangbei.devbase.client.facade.CaptchaFacade;
import com.dangbei.devbase.client.facade.DingTalkFacade;
import com.dangbei.devbase.client.facade.GreenFacade;
import com.dangbei.devbase.client.facade.SmsFacade;
import com.dangbei.devbase.client.facade.TencentCaptchaFacade;
import com.dangbei.devbase.client.facade.TencentDocumentReviewFacade;
import com.dangbei.devbase.client.facade.TencentTextSyncReviewFacade;
import com.dangbei.framework.insight.http.OkHttpUtil;
import com.dangbei.framework.insight.redis.util.RedisCacheUtil;
import com.dangbei.platform.idgenerator.client.api.IdGeneratorService;
import com.dangbei.platform.wsserver.client.dto.cmd.PushBinaryMessageCmd;
import com.dangbei.platform.wsserver.client.dto.cmd.PushMessageCmd;
import com.dangbei.platform.wsserver.client.facade.WsPushMsgFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;
import java.util.Objects;

/**
 * 外部通用网关实现类
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-11-27
 */
@Slf4j
@Component
public class ExternalCommonGatewayImpl implements ExternalCommonGateway {

    @NacosValue(value = "${ai-search.remote-url.ipServiceUrl}", autoRefreshed = true)
    private String ipServiceUrl;
    @DubboReference(group = CommonConst.Dubbo.GROUP)
    private IdGeneratorService idGeneratorService;
    @DubboReference(group = CommonConst.Dubbo.GROUP)
    private CaptchaFacade captchaFacade;
    @DubboReference(group = CommonConst.Dubbo.GROUP)
    private SmsFacade smsFacade;
    @DubboReference(group = CommonConst.Dubbo.GROUP)
    private DingTalkFacade dingTalkFacade;
    @DubboReference(group = CommonConst.Dubbo.GROUP)
    private GreenFacade greenFacade;
    @DubboReference(group = CommonConst.Dubbo.GROUP_AI)
    private WsPushMsgFacade wsPushMsgFacade;
    @DubboReference(group = CommonConst.Dubbo.GROUP)
    private TencentCaptchaFacade tencentCaptchaFacade;
    @DubboReference(group = CommonConst.Dubbo.GROUP)
    private TencentDocumentReviewFacade tencentDocumentReviewFacade;
    @DubboReference(group = CommonConst.Dubbo.GROUP)
    private TencentTextSyncReviewFacade tencentTextSyncReviewFacade;

    @Override
    public String getDistributedId() {
        try {
            return idGeneratorService.getId();
        } catch (Exception e) {
            log.error("[调用ID生成器服务]异常", e);
            return NanoIdUtil.randomNumberNanoId(18);
        }
    }

    @Override
    public boolean captchaVerify(String captchaVerifyParam, String sceneId) {
        try {
            CaptchaVerifyCmd verifyCmd = new CaptchaVerifyCmd();
            verifyCmd.setTenantKey(CommonConst.PROJECT_NAME);
            verifyCmd.setCaptchaVerifyParam(captchaVerifyParam);
            verifyCmd.setSceneId(sceneId);
            Response response = captchaFacade.verify(verifyCmd);
            if (Objects.nonNull(response)) {
                return response.isSuccess();
            }
        } catch (Exception e) {
            log.error("[调用滑块验证服务]异常", e);
        }
        return true;
    }

    @Override
    public Response sendSms(String mobile, String templateCode, Map<String, Object> templateParamMap) {
        try {
            SmsSendCmd smsSendCmd = new SmsSendCmd();
            smsSendCmd.setTenantKey(CommonConst.PROJECT_NAME);
            smsSendCmd.setMobile(mobile);
            smsSendCmd.setTemplateCode(templateCode);
            smsSendCmd.setTemplateParamMap(templateParamMap);
            return smsFacade.send(smsSendCmd);
        } catch (Exception e) {
            log.error("[调用发送短信服务]异常", e);
        }
        return Response.buildFailure("-1", "发送短信失败");
    }

    @Override
    public IpAreaCo getIpArea(String ip) {
        if (StrUtil.isBlank(ip)) {
            return null;
        }
        return RedisCacheUtil.getObject(() -> remoteCall(ip), IpAreaCo.class, String.format(CacheKey.IP_AREA_INFO, ip), 7200L);
    }

    /**
     * 远程调用，获取IP省市
     * @param ip IP地址
     * @return 省市信息
     */
    private IpAreaCo remoteCall(String ip) {
        try {
            String url = String.format(ipServiceUrl, ip);
            return OkHttpUtil.getJson(url, new TypeReference<IpAreaCo>() {
            });
        } catch (IOException ex) {
            log.error(ex.getMessage(), ex);
            return null;
        }
    }

    @Override
    public Response dingTalk(String sceneCode, String title, String content) {
        try {
            DingTalkCmd dingTalkCmd = new DingTalkCmd();
            dingTalkCmd.setTenantKey(CommonConst.PROJECT_NAME);
            dingTalkCmd.setSceneCode(sceneCode);
            dingTalkCmd.setTitle(title);
            dingTalkCmd.setContent(content);
            return dingTalkFacade.send(dingTalkCmd);
        } catch (Exception e) {
            log.error("[调用钉钉通知服务]异常", e);
        }
        return Response.buildFailure("-1", "钉钉通知失败");
    }

    @Override
    public SingleResponse<GreenCheckResp> textGreenCheck(String content, Float confidenceScore, GreenTextServiceEnum service) {
        try {
            GreenTextCheckCmd cmd = new GreenTextCheckCmd();
            cmd.setTenantKey(CommonConst.PROJECT_NAME);
            cmd.setContent(content);
            cmd.setService(service.getCode());
            cmd.setConfidenceScore(confidenceScore);
            SingleResponse<GreenCheckResp> response = greenFacade.textCheck(cmd);
            log.info("[文本检测]结果：{}", JSON.toJSONString(response));
            return response;
        } catch (Exception ex) {
            log.error("[文本检测]异常：{}", ex.getMessage(), ex);
        }
        return SingleResponse.buildFailure("-1", "系统异常，请重试", GreenCheckResp.class);
    }

    @Override
    public SingleResponse<GreenCheckResp> imageGreenCheck(String imgUrl, Float confidenceScore) {
        try {
            GreenImageCheckCmd cmd = new GreenImageCheckCmd();
            cmd.setTenantKey(CommonConst.PROJECT_NAME);
            cmd.setImageUrl(imgUrl);
            cmd.setConfidenceScore(confidenceScore);
            SingleResponse<GreenCheckResp> response = greenFacade.imageCheck(cmd);
            log.info("[图像检测]结果：{}", JSON.toJSONString(response));
            return response;
        } catch (Exception ex) {
            log.error("[图像检测]异常：{}", ex.getMessage(), ex);
        }
        return SingleResponse.buildFailure("-1", "系统异常，请重试", GreenCheckResp.class);
    }

    @Override
    public SingleResponse<GreenFileCheckResp> fileCheck(String fileUrl, String docType) {
        try {
            GreenFileCheckCmd cmd = new GreenFileCheckCmd();
            cmd.setTenantKey(CommonConst.PROJECT_NAME);
            cmd.setFileUrl(fileUrl);
            cmd.setDocType(docType);
            SingleResponse<GreenFileCheckResp> response = greenFacade.fileCheck(cmd);
            log.info("[提交文档检测任务]结果：{}", JSON.toJSONString(response));
            return response;
        } catch (Exception ex) {
            log.error("[提交文档检测任务]异常：{}", ex.getMessage(), ex);
        }
        return SingleResponse.buildFailure("-1", "系统异常，请重试", GreenFileCheckResp.class);
    }

    @Override
    public SingleResponse<GreenFileResultResp> fileCheckResult(String taskId) {
        try {
            GreenFileResultQuery cmd = new GreenFileResultQuery();
            cmd.setTenantKey(CommonConst.PROJECT_NAME);
            cmd.setTaskId(taskId);
            SingleResponse<GreenFileResultResp> response = greenFacade.fileCheckResult(cmd);
            log.info("[获取文档检测任务结果]结果：{}", JSON.toJSONString(response));
            return response;
        } catch (Exception ex) {
            log.error("[获取文档检测任务结果]异常：{}", ex.getMessage(), ex);
        }
        return SingleResponse.buildFailure("-1", "系统异常，请重试", GreenFileResultResp.class);
    }

    @Override
    public SingleResponse<TencentDocumentReviewSubmitResp> tencentFileCheck(String fileUrl, String docType) {
        try {
            TencentDocumentReviewSubmitCmd cmd = new TencentDocumentReviewSubmitCmd();
            cmd.setTenantKey(CommonConst.PROJECT_NAME);
            cmd.setUrl(fileUrl);
            cmd.setType(docType);
            SingleResponse<TencentDocumentReviewSubmitResp> response = tencentDocumentReviewFacade.submit(cmd);
            log.info("[腾讯提交文档检测任务]结果：{}", JSON.toJSONString(response));
            return response;
        } catch (Exception ex) {
            log.error("[腾讯提交文档检测任务]异常：{}", ex.getMessage(), ex);
        }
        return SingleResponse.buildFailure("-1", "系统异常，请重试", TencentDocumentReviewSubmitResp.class);
    }

    @Override
    public SingleResponse<TencentDocumentReviewQueryResp> queryTencentFileCheckResult(String jobId) {
        try {
            TencentDocumentReviewQuery cmd = new TencentDocumentReviewQuery();
            cmd.setTenantKey(CommonConst.PROJECT_NAME);
            cmd.setJobId(jobId);
            SingleResponse<TencentDocumentReviewQueryResp> response = tencentDocumentReviewFacade.query(cmd);
            log.info("[腾讯获取文档检测任务结果]结果：{}", JSON.toJSONString(response));
            return response;
        } catch (Exception ex) {
            log.error("[腾讯获取文档检测任务结果]异常：{}", ex.getMessage(), ex);
        }
        return SingleResponse.buildFailure("-1", "系统异常，请重试", TencentDocumentReviewQueryResp.class);
    }

    @Override
    public SingleResponse<TencentTextSyncReviewResp> tencentSyncReviewContent(String content) {
        try {
            TencentTextSyncReviewCmd cmd = new TencentTextSyncReviewCmd();
            cmd.setTenantKey(CommonConst.PROJECT_NAME);
            cmd.setContent(content);
            SingleResponse<TencentTextSyncReviewResp> response = tencentTextSyncReviewFacade.syncReview(cmd);
            log.info("[腾讯文本检测] 结果：{}", JSON.toJSONString(response));
            return response;
        } catch (Exception ex) {
            log.error("[腾讯文本检测]异常：{}", ex.getMessage(), ex);
        }
        return SingleResponse.buildFailure("-1", "系统异常，请重试", TencentTextSyncReviewResp.class);
    }

    @Override
    public Response pushBinaryMessage(PushBinaryMessageCmd cmd) {
        try {
            return wsPushMsgFacade.pushBinaryMessage(cmd);
        } catch (Exception ex) {
            log.error("[调用ws-server推送音频消息]异常：{}", ex.getMessage(), ex);
            return Response.buildFailure("-1", "系统异常，请重试");
        }
    }

    @Override
    public Response pushMessage(PushMessageCmd cmd) {
        try {
            return wsPushMsgFacade.pushMessage(cmd);
        } catch (Exception ex) {
            log.error("[调用ws-server推送消息]异常：{}", ex.getMessage(), ex);
            return Response.buildFailure("-1", "系统异常，请重试");
        }
    }

    @Override
    public TencentCaptchaVerifyResp tencentCaptchaVerify(String ticket, String randStr, String captchaAppId) {
        try {
            TencentCaptchaVerifyCmd cmd = new TencentCaptchaVerifyCmd();
            cmd.setTenantKey(CommonConst.PROJECT_NAME);
            cmd.setTicket(ticket);
            cmd.setRandStr(randStr);
            cmd.setCaptchaAppId(captchaAppId);
            log.debug("[调用腾讯云验证码校验]入参：{}", JSON.toJSONString(cmd));
            SingleResponse<TencentCaptchaVerifyResp> response = tencentCaptchaFacade.verify(cmd);
            log.debug("[调用腾讯云验证码校验]结果：{}", JSON.toJSONString(response));
            if (response.isSuccess() && Objects.nonNull(response.getData())) {
                return response.getData();
            }
        } catch (Exception ex) {
            log.error("[调用腾讯云验证码校验]异常：{}", ex.getMessage(), ex);
        }
        return null;
    }

    @Override
    public TencentEncryptedCaptchaAppIdCo getTencentCaptchaEncryptedAppId(String captchaAppId) {
        try {
            TencentCaptchaEncryptAppIdCmd cmd = new TencentCaptchaEncryptAppIdCmd();
            cmd.setTenantKey(CommonConst.PROJECT_NAME);
            cmd.setCaptchaAppId(captchaAppId);
            log.debug("[调用腾讯云验证码获取加密AppId]入参：{}", JSON.toJSONString(cmd));
            SingleResponse<TencentEncryptedCaptchaAppIdCo> response = tencentCaptchaFacade.getEncryptedAppId(cmd);
            log.debug("[调用腾讯云验证码获取加密AppId]结果：{}", JSON.toJSONString(response));
            if (response.isSuccess() && Objects.nonNull(response.getData())) {
                return response.getData();
            }
        } catch (Exception ex) {
            log.error("[调用腾讯云验证码获取加密AppId]异常：{}", ex.getMessage(), ex);
        }
        return null;
    }
}
