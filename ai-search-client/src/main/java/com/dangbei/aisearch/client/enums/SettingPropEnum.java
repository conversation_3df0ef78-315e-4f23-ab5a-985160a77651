package com.dangbei.aisearch.client.enums;

import com.dangbei.aisearch.client.dto.clientobject.SettingConfigItemCo;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Setting配置属性枚举
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-04-12
 */
@Getter
@AllArgsConstructor
public enum SettingPropEnum {

    /**
     * 语音合成音色配置
     */
    ttsVoiceType("ttsVoiceType", AgentVoiceEnum.zh_female_tianmeixiaoyuan_moon_bigtts.getValue(), "甜美小源"),

    /**
     * 最近使用模型
     */
    recentModels("recentModels", null, "最近使用模型");

    private final String propKey;
    private final String defaultVal;
    private final String valText;

    /**
     * 检查propKey是否在枚举中
     * @param propKey 属性Key
     * @return true: 在枚举中; false: 不在枚举中
     */
    public static boolean isValidPropKey(String propKey) {
        for (SettingPropEnum settingPropEnum : SettingPropEnum.values()) {
            if (settingPropEnum.getPropKey().equals(propKey)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取所有默认属性枚举
     * @return 所有带默认属性值的枚举
     */
    public static List<SettingPropEnum> listDefaultValEnum() {
        return Arrays.stream(SettingPropEnum.values())
            .filter(item -> StringUtils.isNoneBlank(item.getDefaultVal()))
            .collect(Collectors.toList());
    }

    /**
     * 获取所有默认属性枚举
     * @return 所有带默认属性值的枚举
     */
    public static Map<String, SettingConfigItemCo> buildDefaultConfigItemMap() {
        return listDefaultValEnum().stream().collect(Collectors.toMap(SettingPropEnum::getPropKey, item -> {
            SettingConfigItemCo configItem = new SettingConfigItemCo();
            configItem.setPropKey(item.getPropKey());
            configItem.setPropVal(item.getDefaultVal());
            configItem.setValText(item.getValText());
            return configItem;
        }));
    }

}
