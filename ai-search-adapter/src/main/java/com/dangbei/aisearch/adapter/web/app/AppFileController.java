package com.dangbei.aisearch.adapter.web.app;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.alibaba.cola.dto.SingleResponse;
import com.dangbei.aisearch.app.executor.OssCmdExe;
import com.dangbei.aisearch.app.service.TosService;
import com.dangbei.aisearch.client.dto.clientobject.OssStsCo;
import com.dangbei.aisearch.client.dto.clientobject.TosStsCo;
import com.dangbei.aisearch.client.enums.StorageDirTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 文件服务接口
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-25
 */
@RestController
@RequestMapping("/app/fileApi/v1")
@Tag(name = "AppFileController", description = "文件服务")
public class AppFileController {

    @Resource
    private OssCmdExe ossCmdExe;
    @Resource
    private TosService tosService;

    @SaCheckLogin
    @Operation(summary = "oss sts授权", description = "oss sts授权")
    @GetMapping("/sts")
    public SingleResponse<OssStsCo> sts(@Schema(description = "对象存储目录类型, file,public",requiredMode = Schema.RequiredMode.NOT_REQUIRED)
                                            @RequestParam(required = false) String dirType) {
        var storageDirType = StorageDirTypeEnum.getByDirType(dirType);
        SingleResponse<OssStsCo> response = ossCmdExe.sts(storageDirType);
        if (Objects.nonNull(response) && Objects.nonNull(response.getData()) && StringUtils.isNotBlank(response.getData().getOssEndpoint())) {
            response.getData().setOssEndpoint(response.getData().getOssEndpoint()
                .replace("http://", "")
                .replace("https://", ""));
        }
        return response;
    }

    @SaCheckLogin
    @Operation(summary = "tos sts授权", description = "tos sts授权")
    @GetMapping("/tos/sts")
    public SingleResponse<TosStsCo> tosSts(@Schema(description = "对象存储目录类型, file,public",requiredMode = Schema.RequiredMode.NOT_REQUIRED)
                                               @RequestParam(required = false) String dirType) {
        var storageDirType = StorageDirTypeEnum.getByDirType(dirType);
        return SingleResponse.of(tosService.getSts(storageDirType));
    }
}
