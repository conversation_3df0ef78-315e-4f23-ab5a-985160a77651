package com.dangbei.aisearch.infrastructure.config.properties;

import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * 生成头像配置
 *
 * <AUTHOR>
 * @date 2025-03-26 16:50
 **/
@Data
@Component
@NacosConfigurationProperties(dataId = "${nacos.config.data-id:com.dangbei.ai-search.application.yaml}",
    groupId = "${nacos.config.group:ai-search}",
    prefix = "gen-avatar", autoRefreshed = true)
public class GenAvatarProperties {

    /**
     * 生成头像每日次数限制
     */
    private Integer genLimitPreDay = 10;

    /**
     * 默认方图头像尺寸
     */
    private String squareAvatarSize = "512*512";

    /**
     * 默认长图头像尺寸
     */
    private String appAvatarSize = "720*1280";

    /**
     * 头像生成锁过期时间 24h
     */
    public Long lockTimeSeconds = 86400L;

}
