package com.dangbei.aisearch.client.dto.cmd.query;

import com.alibaba.cola.dto.DTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 消息批量查询
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-25
 */
@Data
@Accessors(chain = true)
public class MessageListQuery extends DTO {

    @NotBlank(message = "会话ID不能为空")
    @Schema(description = "会话ID")
    private String conversationId;

    @Schema(description = "智能体唯一ID")
    private String agentId;

    @Schema(description = "排序方式" +
        "desc-（默认）按创建时间倒序;" +
        "asc-按创建时间正序")
    private String order = "desc";

    @Schema(description = "查看指定位置之后的消息；默认为 0，表示不指定位置。如需向后翻页，则指定为返回结果中的 last_id")
    private String afterId = "0";

    @Schema(description = "after_id比较，取值：gt-大于; lt-小于; ge-大于等于; le-小于等于;")
    private String afterIdCompare;

    @Schema(description = "每次查询返回的数据量。默认为20")
    private Integer limit = 20;

    @Schema(description = "消息ID列表")
    private List<String> msgIds;

    @Schema(description = "匿名会话的key")
    private String anonymousKey;

}
