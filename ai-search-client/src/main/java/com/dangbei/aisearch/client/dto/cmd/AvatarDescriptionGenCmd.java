package com.dangbei.aisearch.client.dto.cmd;

import com.alibaba.cola.dto.DTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025-03-26 16:44
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "生成智能体形象描述")
public class AvatarDescriptionGenCmd extends DTO {

    @Schema(description = "风格")
    private String style;

    @Schema(description = "智能体角色类型：0-角色类 1-助理类")
    @NotNull(message = "智能体角色类型不能为空")
    @Max(value = 1, message = "智能体角色类型错误")
    @Min(value = 0, message = "智能体角色类型错误")
    private Integer agentRole;

    @Schema(description = "智能体名称")
    private String agentName;

    @Schema(description = "角色/工具简介")
    private String intro;

    @Schema(description = "智能体性别：0-男 1-女 2-非人类角色")
    private Integer gender;
}
