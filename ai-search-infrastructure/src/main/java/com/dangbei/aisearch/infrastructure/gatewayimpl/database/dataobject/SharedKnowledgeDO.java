package com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dangbei.aisearch.infrastructure.common.base.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * SharedKnowledge DO对象
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-05-27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("ais_shared_knowledge")
public class SharedKnowledgeDO extends BaseDO<Long> {

    @Schema(description = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "当贝知识库ID")
    @TableField(value = "knowledge_id")
    private String knowledgeId;

    @Schema(description = "外部知识库ID")
    @TableField(value = "knowledge_id_ext")
    private String knowledgeIdExt;

    @Schema(description = "命名空间")
    @TableField(value = "namespace")
    private String namespace;

    @Schema(description = "创建人ID")
    @TableField(value = "create_user_id")
    private String createUserId;

    @Schema(description = "知识库名称")
    @TableField(value = "name")
    private String name;

    @Schema(description = "封面图片URL")
    @TableField(value = "cover_url")
    private String coverUrl;

    @Schema(description = "知识库描述")
    @TableField(value = "description")
    private String description;

    @Schema(description = "是否需要审批加入")
    @TableField(value = "join_approval_required")
    private Integer joinApprovalRequired;

    @Schema(description = "成员是否可下载")
    @TableField(value = "download_enabled")
    private Integer downloadEnabled;

    @Schema(description = "数据类型")
    @TableField(value = "data_type")
    private String dataType;

    @Override
    public Long getPrimaryId() {
        return this.id;
    }

    @Override
    public void setPrimaryId(Long id) {
        this.id = id;
    }
}
