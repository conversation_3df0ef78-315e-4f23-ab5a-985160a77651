package com.dangbei.aisearch.infrastructure.config;

import cn.hutool.core.map.MapUtil;
import com.alibaba.cola.common.constant.RequestConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.core.task.TaskDecorator;
import org.springframework.lang.NonNull;

import java.util.Locale;
import java.util.Map;
import java.util.UUID;

/**
 * .
 * <p>
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-15
 */
@Slf4j
public class AsyncDecorator implements TaskDecorator {
    /**
     * 装饰
     * @param runnable 任务
     * @return {@link Runnable }
     */
    @NonNull
    @Override
    public Runnable decorate(@NonNull Runnable runnable) {
        Locale locale = LocaleContextHolder.getLocale();
        Map<String, String> map = MDC.getCopyOfContextMap();
        String requestId = StringUtils.isBlank(MDC.get(RequestConstant.REQUEST_ID)) ? UUID.randomUUID().toString() : MDC.get(RequestConstant.REQUEST_ID);
        return () -> {
            try {
                if (MapUtil.isNotEmpty(map)) {
                    MDC.setContextMap(map);
                }
                MDC.put(RequestConstant.REQUEST_ID, requestId);

                LocaleContextHolder.setLocale(locale, true);
            } catch (Exception e) {
                log.error("设置 MDC requestId 异常", e);
            }
            try {
                runnable.run();
            } catch (Exception e) {
                /*
                 * @Resource(name = "asyncExecutor")
                 * private ThreadPoolTaskExecutor executor;
                 * executor.execute(runnable);
                 * 此处可以捕获如上写法抛出的异常
                 * 若使用 @Async，则异常只会被 AsyncConfig 中的 AsyncUncaughtExceptionHandler 捕获，但是此处 finally 中的 MDC.clear 仍然会执行
                 *
                 * 如果使用 executor.execute(runnable) 来执行一个  @Async 的方法，runnable 只会执行一次，finally 会执行两次，异常只会被 AsyncUncaughtExceptionHandler 捕获
                 * 建议使用 executor.execute(runnable) 的方式，@Async 是通过aop实现的，有很多场景可能导致失效
                 */
                log.error("[异步任务执行异常]", e);
            } finally {
                MDC.clear();
            }
        };
    }
}
