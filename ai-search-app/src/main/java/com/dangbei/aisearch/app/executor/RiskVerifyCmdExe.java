package com.dangbei.aisearch.app.executor;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.exception.Assert;
import com.dangbei.aisearch.app.util.UserDeviceUtil;
import com.dangbei.aisearch.client.dto.cmd.RiskVerifyCmd;
import com.dangbei.aisearch.domain.gateway.ExternalCommonGateway;
import com.dangbei.aisearch.domain.gateway.RiskGateway;
import com.dangbei.devbase.client.dto.TencentCaptchaVerifyResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 风控校验执行器
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-05-15
 */
@Slf4j
@Component
public class RiskVerifyCmdExe {

    @Resource
    private ExternalCommonGateway externalCommonGateway;
    @Resource
    private RiskGateway riskGateway;

    public Response execute(RiskVerifyCmd cmd) {
        TencentCaptchaVerifyResp resp = externalCommonGateway.tencentCaptchaVerify(cmd.getTicket(), cmd.getRandStr(), null);
        Assert.isTrue(Objects.nonNull(resp) && resp.isSuccess(), "验证码校验失败");

        // 校验通过，加入白名单
        String userIdDefaultDeviceId = UserDeviceUtil.getUserIdDefaultDeviceId();
        riskGateway.addWhiteList(userIdDefaultDeviceId);
        return Response.buildSuccess();
    }
}
