package com.dangbei.aisearch.client.dto.cmd;

import com.alibaba.cola.dto.DTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 会话删除命令
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-25
 */
@Data
public class ConversationDeleteCmd extends DTO {

    @NotBlank(message = "会话ID不能为空")
    @Schema(description = "会话ID")
    private String conversationId;

}
