package com.dangbei.aisearch.infrastructure.knowledge.dto.query;

import com.dangbei.framework.insight.volcopenai.model.completion.chat.ChatMessage;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-06 16:49
 **/
@Data
@Builder
@Accessors
public class SearchVolcKnowledgeQuery implements Serializable {

    /**
     * 项目空间
     */
    @Builder.Default
    private String namespace = "default";
    /**
     * 火山知识库名称
     */
    private String knowledgeIdExt;

    /**
     * 查询关联的 doc
     */
    private List<String> docIdExtList;

    /**
     * 用户输入
     */
    private String query;

    /**
     * 检索结果数量[1, 200]
     */
    @Builder.Default
    private Integer limit = 10;

    /**
     * 是否拼接 instruction 进行检索
     */
    private boolean needInstruction;

    /**
     * 是否对 query 进行改写
     */
    private boolean rewrite;

    /**
     * 上下文消息
     */
    private List<ChatMessage> messages;

    /**
     * 自动对结果做 rerank
     */
    private boolean rerankSwitch;

    /**
     * 进入重排的切片数量，默认为 25
     * 只有在 rerank_switch 为 True 时生效。retrieve_count 需要大于等于 limit，否则会抛出错误
     */
    @Builder.Default
    private int retrieveCount = 25;

    /**
     * 检索阶段返回命中文本片上下几片文本片
     * 默认为 0，表示不进行 chunk diffusion。范围 [0, 5]
     */
    @Builder.Default
    private int chunkDiffusionCount = 0;

    /**
     * 文本聚合
     * 默认不聚合，对于非结构化文件，考虑到原始文档内容语序对大模型的理解，可开启文本聚合。开启后，会根据文档及文档顺序，对切片进行重新聚合排序返回
     */
    @Builder.Default
    private boolean chunkGroup = true;

    /**
     * rerank 模型选择
     * 仅在 "rerank_switch" == True 的时候生效
     * 可选模型：
     * "m3-v2-rerank"：轻量小模型，具有强大的多语言能力，推理速度快
     */
    @Builder.Default
    private String rerankModel = "base-multilingual-rerank";

    /**
     * 是否仅根据 chunk 内容计算重排分数
     * 可选值：
     * True： 只根据 chunk 内容计算分
     * False：根据 chunk title + 内容 一起计算排序分
     */
    private boolean rerankOnlyChunk;

    /**
     * 是否获取切片中图片的临时下载链接
     */
    private boolean getAttachmentLink;

}
