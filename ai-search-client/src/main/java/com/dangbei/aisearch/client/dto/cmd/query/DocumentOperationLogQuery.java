package com.dangbei.aisearch.client.dto.cmd.query;

import com.alibaba.cola.dto.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 文档操作日志查询
 * <AUTHOR>
 * @date 2025-05-27
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class DocumentOperationLogQuery extends PageQuery {

    @NotBlank(message = "知识库ID不能为空")
    @Schema(description = "知识库ID", example = "kb_shared_1706428800001")
    private String knowledgeId;

    @Schema(description = "文档ID（可选，查询特定文档的操作日志）", example = "doc_1706428800001")
    private String docId;

    @Schema(description = "操作类型筛选：1-上传，2-删除，3-重命名，4-复制，5-移动", example = "4")
    private Integer operationType;

    @Schema(description = "操作人筛选（用户昵称）", example = "张三")
    private String operatorKeyword;

} 