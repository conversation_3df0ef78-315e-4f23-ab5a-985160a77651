-- auto-generated definition
create table ais_user_knowledge
(
    id                  BIGINT UNSIGNED auto_increment comment '主键id'
        primary key,
    knowledge_id        VARCHAR(128)  default ''                   not null comment '当贝知识库ID',
    knowledge_id_ext    VARCHAR(128)                               null comment '外部知识库ID',
    namespace           VARCHAR(64)   default 'default'            not null comment '命名空间',
    user_id             VARCHAR(64)   default ''                   not null comment '用户ID',
    device_id           VARCHAR(64)   default ''                   not null comment '设备ID',
    name                VARCHAR(64)   default ''                   not null comment '知识库名称',
    data_type           VARCHAR(64)   default 'unstructured_data'  not null comment '数据类型 unstructured_data-非结构化 structured_data-结构化',
    create_person       VARCHAR(16)   default ''                   null comment '创建人',
    create_time         DATETIME(19)  default CURRENT_TIMESTAMP    not null comment '业务创建时间',
    update_person       VARCHAR(16)   default ''                   null comment '修改人',
    update_time         DATETIME(19)  default CURRENT_TIMESTAMP    not null comment '业务更新时间',
    is_deleted          BIGINT(19)    default 0                    not null comment '逻辑删除标识，0-未删除,其他-已删除',
    db_modify_timestamp TIMESTAMP(23) default CURRENT_TIMESTAMP(3) not null comment '数据库变更时间',
    constraint uk_knowledge_id
        unique (knowledge_id),
    constraint uk_user_id
        unique (user_id),
    constraint uk_user_id_knowledge_id_ext
        unique (user_id, knowledge_id_ext)
);

-- auto-generated definition
create table ais_user_knowledge_doc
(
    id                      BIGINT UNSIGNED auto_increment comment '主键id'
        primary key,
    knowledge_id            VARCHAR(128)    default ''                   not null comment '当贝知识库ID',
    doc_id                  VARCHAR(128)    default ''                   not null comment '当贝文档ID',
    doc_name                VARCHAR(128)                                 null comment '用户文档名称',
    doc_path                VARCHAR(128)                                 null comment 'OSS文件地址',
    doc_type                VARCHAR(16)                                  null comment '文档的类型，如docx',
    doc_size                INT(10)         default 0                    not null comment '文件字节数',
    md5                     VARCHAR(64)     default ''                   not null comment '文件MD5',
    process_status          INT(10)         default 0                    not null comment '文档的处理状态 0-处理中 1-处理完成 2-处理失败',
    point_num               INT(10)         default 0                    not null comment '文档提取出的point数量',
    word_num                BIGINT UNSIGNED default 0                    not null comment '文档字数',
    doc_id_ext              VARCHAR(128)    default ''                   not null comment '外部文档ID',
    fail_reason             VARCHAR(128)    default ''                   not null comment '失败原因',
    volc_delete_flag        TINYINT(3)      default 0                    not null comment '火山知识库文档删除标记 0-未删除 1-已删除 2-删除失败',
    volc_delete_fail_reason VARCHAR(128)    default ''                   not null comment '火山知识库文档删除失败原因',
    create_person           VARCHAR(16)     default ''                   null comment '创建人',
    create_time             DATETIME(19)    default CURRENT_TIMESTAMP    not null comment '业务创建时间',
    update_person           VARCHAR(16)     default ''                   null comment '修改人',
    update_time             DATETIME(19)    default CURRENT_TIMESTAMP    not null comment '业务更新时间',
    is_deleted              BIGINT(19)      default 0                    not null comment '逻辑删除标识，0-未删除,其他-已删除',
    db_modify_timestamp     TIMESTAMP(23)   default CURRENT_TIMESTAMP(3) not null comment '数据库变更时间',
    constraint uk_doc_id
        unique (doc_id),
    constraint uk_doc_path
        unique (doc_path)
);

create index idx_create_time
    on ais_user_knowledge_doc (create_time);

create index idx_doc_id_ext
    on ais_user_knowledge_doc (doc_id_ext);

create index idx_doc_name
    on ais_user_knowledge_doc (doc_name);


-- 知识库文档迁移表
create table ais_user_knowledge_doc_migrate
(
    id                   BIGINT UNSIGNED auto_increment comment '主键id',
    user_id              VARCHAR(64)                                not null comment '用户ID',
    knowledge_id         VARCHAR(128)                               not null comment '当贝知识库ID',
    doc_id               VARCHAR(128)                               not null comment '当贝文档ID',
    old_knowledge_id_ext VARCHAR(128)                               not null comment '迁移前外部知识库ID',
    new_knowledge_id_ext VARCHAR(128)                               not null comment '迁移后外部知识库ID',
    old_doc_id_ext       VARCHAR(128)                               not null comment '迁移前外部文档ID',
    new_doc_id_ext       VARCHAR(128)  default ''                   not null comment '迁移后外部文档ID',
    doc_name             VARCHAR(128)                               null comment '用户文档名称',
    doc_path             VARCHAR(128)                               null comment 'OSS文件地址',
    doc_type             VARCHAR(16)                                null comment '文档的类型，如docx',
    doc_size             INT(10)       default 0                    not null comment '文件字节数',
    process_status       INT(10)       default 0                    not null comment '文档的处理状态 0-处理中 1-处理完成 2-处理失败',
    `create_person`       varchar(16) NULL DEFAULT '' COMMENT '创建人',
    `create_time`         datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '业务创建时间',
    `update_person`       varchar(16) NULL DEFAULT '' COMMENT '修改人',
    `update_time`         datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '业务更新时间',
    `is_deleted`          bigint       NOT NULL DEFAULT 0 COMMENT '逻辑删除标识，0-未删除,其他-已删除',
    `db_modify_timestamp` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '数据库变更时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1000 COMMENT = '知识库迁移任务文档'
  ROW_FORMAT = DYNAMIC;

-- 调整  ais_file 表 file_url 字段长度
alter table ais_file
    modify file_url VARCHAR(256) null comment '文件地址';


