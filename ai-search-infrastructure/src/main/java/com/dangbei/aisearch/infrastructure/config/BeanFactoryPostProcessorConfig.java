package com.dangbei.aisearch.infrastructure.config;

import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.config.BeanFactoryPostProcessor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.config.TaskManagementConfigUtils;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-14
 */
@Configuration
public class BeanFactoryPostProcessorConfig {

    /**
     * 在调用当前类的 @Async 方法时，如果直接通过 this 调用，异步并不会生效，因为 @Async 是通过 aop 实现的，得通过代理对象调用才会生效。
     * <p>
     * <p>
     * 此时需要从 spring 容器中获取代理对象，也就是 AopContext.currentProxy()，需要确保当前 Bean 已经被自动代理创建器提前代理。
     * <p>
     * <p>
     * 即使已经有了 @EnableAspectJAutoProxy(exposeProxy = true)，直接使用 @Async，调用 AopContext.currentProxy() 仍然获取不到。
     * <p>
     * <p>
     * 代理对象，除非再在本类中加上 @Transactional 或其他可以创建代理对象的注解，才能获取到。
     * <p>
     * <p>
     * 因此这里显示的指定 exposeProxy 为 true。
     * <p>
     * <p>
     * 具体参见 <a href="https://blog.csdn.net/Sophisticated_/article/details/102793703">@Async分析exposeProxy=true不生效原因</a>
     * @return {@link BeanFactoryPostProcessor }
     */
    @Bean
    public BeanFactoryPostProcessor beanFactoryPostProcessor() {
        return configurableListableBeanFactory -> {
            BeanDefinition beanDefinition = configurableListableBeanFactory.getBeanDefinition(TaskManagementConfigUtils.ASYNC_ANNOTATION_PROCESSOR_BEAN_NAME);
            beanDefinition.getPropertyValues().add("exposeProxy", Boolean.TRUE);
        };
    }
}
