package com.dangbei.aisearch.app.util;

import com.alibaba.cola.exception.BizException;
import com.dangbei.aisearch.common.util.converter.HtmlToWordConverter;
import com.spire.doc.packages.sprmfz;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-25
 */
@Slf4j
@UtilityClass
public class WordUtil {

    public static void download(String fileName, String content, HttpServletResponse response) {
        content = content.replaceAll("\\[\\d+]", "")
            .replace("<hr/>", "")
            .replace("<hr />", "");
        try (InputStream inputStream = HtmlToWordConverter.convertHtmlToWord(content);
             OutputStream outputStream = response.getOutputStream()) {
            IOUtils.copy(Objects.requireNonNull(inputStream), outputStream);
            outputStream.flush();
            setWordResponse(response, fileName);
        } catch (sprmfz e) {
            if (StringUtils.contains(e.getMessage(), "limited to 500 paragraphs")) {
                throw new BizException("Word文本内容超过500个段落,无法下载", e);
            } else {
                throw new RuntimeException("下载word异常", e);
            }
        } catch (Exception e) {
            throw new RuntimeException("下载word异常");
        }
    }

    private static void setWordResponse(HttpServletResponse response, String fileName) {
        response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        response.setCharacterEncoding("utf-8");
        String fileNameEncode = URLEncoder.encode(fileName, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileNameEncode + ".docx");
        response.setHeader("fileName", fileNameEncode);
    }
}
