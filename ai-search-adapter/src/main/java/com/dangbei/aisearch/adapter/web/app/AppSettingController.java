package com.dangbei.aisearch.adapter.web.app;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.dangbei.aisearch.app.service.SettingService;
import com.dangbei.aisearch.client.dto.BaseDTO;
import com.dangbei.aisearch.client.dto.clientobject.SettingConfigCo;
import com.dangbei.aisearch.client.dto.clientobject.VoiceTypePreviewCo;
import com.dangbei.aisearch.client.dto.cmd.SettingUpdateCmd;
import com.dangbei.aisearch.client.dto.cmd.query.SettingConfigQuery;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * AppSetting服务接口
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-01-07
 */
@Tag(name = "AppSettingController", description = "AppSettingController服务")
@RequestMapping("/app/settingApi/v1")
@RestController
public class AppSettingController {

    @Resource
    private SettingService settingService;

    @Operation(summary = "获取Setting配置", description = "获取Setting配置")
    @PostMapping("/getSettingConfig")
    public SingleResponse<SettingConfigCo> getSettingConfig(@RequestBody SettingConfigQuery query) {
        return settingService.getSettingConfig(query);
    }

    @PostMapping("listVoiceType")
    @Operation(summary = "获取音色预览列表", description = "获取音色预览列表")
    public MultiResponse<VoiceTypePreviewCo> listVoiceType(@RequestBody BaseDTO headerDTO) {
        return settingService.listVoiceType();
    }

    @PostMapping("/updSettingConfig")
    @Operation(summary = "更新Setting配置", description = "更新Setting配置")
    public Response updSettingConfig(@RequestBody SettingUpdateCmd cmd) {
        return settingService.updSettingConfig(cmd);
    }

}
