package com.dangbei.aisearch.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-04-02
 */
@Getter
@AllArgsConstructor
public enum ModelEnum {
    DEEPSEEK_R1("deepseek", I18nValueEnum.DEEPSEEK_R1, I18nValueEnum.DEEPSEEK_R1_HOVER_TEXT),
    DEEPSEEK_V3("deepseek-v3", I18nValueEnum.DEEPSEEK_V3, I18nValueEnum.DEEPSEEK_V3_HOVER_TEXT),
    DOUBAO("doubao", I18nValueEnum.DOUBAO, I18nValueEnum.DOUBAO_HOVER_TEXT),
    TONGYI("qwen", I18nValueEnum.TONGYI, I18nValueEnum.TONGYI_HOVER_TEXT),
    ZHIPU("glm3", I18nValueEnum.ZHIPU, I18nValueEnum.ZHIPU_HOVER_TEXT),
    KIMI("moonshot_v1", I18nValueEnum.KIMI, I18nValueEnum.KIMI_HOVER_TEXT),
    ;

    private final String value;

    private final I18nValueEnum i18nValueEnum;
    private final I18nValueEnum hoverTextEnum;

    public static I18nValueEnum getI18nValueEnum(String value) {
        for (ModelEnum modelEnum : values()) {
            if (modelEnum.value.equals(value)) {
                return modelEnum.getI18nValueEnum();
            }
        }
        return null;
    }

    public static I18nValueEnum getHoverTextEnum(String value) {
        for (ModelEnum modelEnum : values()) {
            if (modelEnum.value.equals(value)) {
                return modelEnum.getHoverTextEnum();
            }
        }
        return null;
    }
}
