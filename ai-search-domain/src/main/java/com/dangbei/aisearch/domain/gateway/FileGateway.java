package com.dangbei.aisearch.domain.gateway;

import com.dangbei.aisearch.client.dto.cmd.ChatCmd;
import com.dangbei.aisearch.domain.common.base.BaseGateway;
import com.dangbei.aisearch.domain.entity.FileEntity;

import java.util.List;
import java.util.Map;

/**
 * File 网关层
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-01-20
 */
public interface FileGateway extends BaseGateway<Long, FileEntity> {

    /**
     * 根据文件MD5查询
     * @param md5 文件MD5
     * @return 文件内容
     */
    FileEntity getByMd5(String md5);

    /**
     * 根据文件MD5查询
     * @param url 文件地址
     * @return 文件内容
     */
    FileEntity getByUrl(String url);

    /**
     * 获取文件ID映射
     * @return 文件ID映射Mapping
     */
    Map<String, FileEntity> fileIdMapping(List<ChatCmd.FileItem> files);


    /**
     * 根据文件ID查询文件列表
     * @param fileIds 文件IDs
     * @return 文件列表
     */
    List<FileEntity> listMyFileId(List<String> fileIds);

}
