package com.dangbei.aisearch.infrastructure.config;

import cn.hutool.core.util.RandomUtil;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;

import static com.dangbei.aisearch.common.constant.DcParamConst.Cache.IPS_CHECK_CACHE_NAME;
import static com.dangbei.aisearch.common.constant.DcParamConst.Cache.PARAM_TYPE_CACHE_NAME;
import static com.dangbei.aisearch.common.constant.DcParamConst.Cache.PARAM_TYPE_CODE_CACHE_NAME;

/**
 * 参数缓存配置类
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-01-11
 */
@EnableCaching
@Configuration
public class CaffeineCacheConfig {

    @Bean("caffeineCacheManager")
    public CaffeineCacheManager cacheManager() {
        CaffeineCacheManager caffeineCacheManager = new CaffeineCacheManager();
        caffeineCacheManager.setCaffeine(Caffeine.newBuilder()
            .initialCapacity(100)
            .maximumSize(1000)
            .expireAfterWrite(RandomUtil.randomInt(10) + 30, TimeUnit.SECONDS));
        caffeineCacheManager.setCacheNames(Arrays.asList(PARAM_TYPE_CODE_CACHE_NAME, PARAM_TYPE_CACHE_NAME, IPS_CHECK_CACHE_NAME));
        return caffeineCacheManager;
    }
}

