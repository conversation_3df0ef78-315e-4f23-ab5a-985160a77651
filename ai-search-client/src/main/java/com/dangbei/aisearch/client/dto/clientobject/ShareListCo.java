package com.dangbei.aisearch.client.dto.clientobject;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-07
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ShareListCo extends MessageListCo {

    @Schema(description = "会话ID")
    private String conversationId;

    @Schema(description = "分享状态(1:正常;2:已停止分享)")
    private Integer shareStatus;

    @Schema(description = "点赞数")
    private Integer likeNum;

    @Schema(description = "过期时间")
    private LocalDateTime expireTime;

    @Schema(description = "是否过期")
    private boolean expired;

    @Schema(description = "分享创建时间")
    private LocalDateTime createTime;

    @Schema(description = "是否是自己的")
    private Boolean isOwn;

}
