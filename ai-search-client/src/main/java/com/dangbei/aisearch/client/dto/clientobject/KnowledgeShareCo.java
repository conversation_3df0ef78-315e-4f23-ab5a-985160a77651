package com.dangbei.aisearch.client.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;
import com.dangbei.aisearch.client.enums.SharedKnowledgeRoleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-05-29
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class KnowledgeShareCo extends ClientObject {

    @Schema(description = "知识库ID")
    private String knowledgeId;

    @Schema(description = "知识库名称")
    private String name;

    @Schema(description = "知识库描述")
    private String description;

    @Schema(description = "封面图片URL")
    private String coverUrl;

    @Schema(description = "创建人ID")
    private String createUserId;

    @Schema(description = "创建人名称")
    private String createUserName;

    @Schema(description = "创建人头像")
    private String createUserAvatar;

    @Schema(description = "成员数量")
    private Long memberCount;

    @Schema(description = "文档数量")
    private Long docCount;

    @Schema(description = "是否需要审批加入")
    private Boolean joinApprovalRequired;

    @Schema(description = "成员是否可下载")
    private Boolean downloadEnabled;

    /**
     * {@link SharedKnowledgeRoleEnum}
     */
    @Schema(description = "角色：1-创建人，2-管理员，3-成员")
    private Integer role;

    @Schema(description = "角色名称")
    private String roleName;

    @Schema(description = "创建时间")
    private String createTime;

    @Schema(description = "更新时间")
    private String updateTime;

    @Schema(description = "是否是成员")
    private Boolean member;
}
