package com.dangbei.aisearch.app.util;

import java.util.Objects;

/**
 * APP版本信息
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-04-28
 */
public class AppVersionUtil {

    /**
     * 判断当前版本是否小于指定版本
     */
    public static boolean lessThan(Integer targetVersion) {
        Integer reqVersionCode = RequestThreadLocalUtil.getAppVersionCode();
        return Objects.nonNull(reqVersionCode) && reqVersionCode < targetVersion;
    }

    /**
     * 判断当前版本是否小于等于指定版本
     */
    public static boolean lessThanEq(Integer targetVersion) {
        Integer reqVersionCode = RequestThreadLocalUtil.getAppVersionCode();
        return Objects.nonNull(reqVersionCode) && reqVersionCode <= targetVersion;
    }

    /**
     * 判断当前版本是否大于指定版本
     */
    public static boolean greaterThan(Integer targetVersion) {
        Integer reqVersionCode = RequestThreadLocalUtil.getAppVersionCode();
        return Objects.nonNull(reqVersionCode) && reqVersionCode > targetVersion;
    }


    /**
     * 判断当前版本是否大于等于指定版本
     */
    public static boolean greaterThanEq(Integer targetVersion) {
        Integer reqVersionCode = RequestThreadLocalUtil.getAppVersionCode();
        return Objects.nonNull(reqVersionCode) && reqVersionCode >= targetVersion;
    }

}
