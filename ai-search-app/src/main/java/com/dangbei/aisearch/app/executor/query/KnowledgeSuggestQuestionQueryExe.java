package com.dangbei.aisearch.app.executor.query;

import com.alibaba.cola.dto.MultiResponse;
import com.dangbei.aisearch.infrastructure.config.properties.KnowledgeDocProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025-03-18 10:26
 **/
@Component
@Slf4j
public class KnowledgeSuggestQuestionQueryExe {

    @Resource
    private KnowledgeDocProperties knowledgeDocProperties;
    public MultiResponse<String> execute() {
        return MultiResponse.of(knowledgeDocProperties.getSuggestQuestion());
    }
}
