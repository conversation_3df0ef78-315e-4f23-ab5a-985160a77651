package com.dangbei.aisearch.app.executor;

import com.alibaba.cola.dto.Response;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dangbei.aisearch.app.util.UserDeviceUtil;
import com.dangbei.aisearch.client.dto.UserDeviceDTO;
import com.dangbei.aisearch.client.dto.cmd.ConversationBatchDeleteCmd;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.CommunityPostDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.ConversationDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.CommunityPostMapper;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.ConversationMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 会话删除命令执行器
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-25
 */
@Component
public class ConversationBatchDeleteCmdExe {

    @Resource
    private ConversationMapper conversationMapper;
    @Resource
    private CommunityPostMapper communityPostMapper;

    @Transactional(rollbackFor = Exception.class)
    public Response execute(ConversationBatchDeleteCmd cmd) {
        if (!cmd.isDeleteAll() && CollectionUtils.isEmpty(cmd.getConversationIdList())) {
            return Response.buildSuccess();
        }

        UserDeviceDTO userDeviceInfo = UserDeviceUtil.getUserDeviceInfo();
        LambdaQueryWrapper<ConversationDO> conversationWrapper = Wrappers.lambdaQuery(ConversationDO.class);
        if (StringUtils.isNotBlank(userDeviceInfo.getUserId())) {
            conversationWrapper.eq(ConversationDO::getUserId, userDeviceInfo.getUserId());
        } else if (StringUtils.isNotBlank(userDeviceInfo.getDeviceId())) {
            conversationWrapper.eq(ConversationDO::getDeviceId, userDeviceInfo.getDeviceId());
        }
        boolean deleteSelected = !cmd.isDeleteAll() && CollectionUtils.isNotEmpty(cmd.getConversationIdList());
        if (deleteSelected) {
            conversationWrapper.in(ConversationDO::getConversationId, cmd.getConversationIdList());
        }
        conversationMapper.delete(conversationWrapper);

        // 删除包含点亮的会话时，从广场帖子中删除
        LambdaQueryWrapper<CommunityPostDO> postWrapper = Wrappers.lambdaQuery(CommunityPostDO.class);
        if (StringUtils.isNotBlank(userDeviceInfo.getUserId())) {
            postWrapper.eq(CommunityPostDO::getUserId, userDeviceInfo.getUserId());
        } else if (StringUtils.isNotBlank(userDeviceInfo.getDeviceId())) {
            postWrapper.eq(CommunityPostDO::getDeviceId, userDeviceInfo.getDeviceId());
        }
        if (deleteSelected) {
            postWrapper.in(CommunityPostDO::getConversationId, cmd.getConversationIdList());
        }
        communityPostMapper.delete(postWrapper);
        return Response.buildSuccess();
    }
}
