package com.dangbei.aisearch.app.executor;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.exception.Assert;
import com.dangbei.aisearch.app.util.UserDeviceUtil;
import com.dangbei.aisearch.client.dto.ConversationMetaData;
import com.dangbei.aisearch.client.dto.cmd.ConversationChatModelUpdateCmd;
import com.dangbei.aisearch.domain.entity.ConversationEntity;
import com.dangbei.aisearch.domain.gateway.ConversationGateway;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * 会话聊天模型编辑命令执行器
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-25
 */
@Component
public class ConversationChatModelUpdateCmdExe {

    @Resource
    private ConversationGateway conversationGateway;

    public Response execute(ConversationChatModelUpdateCmd cmd) {
        ConversationEntity conversationEntity = conversationGateway.getByConversationId(cmd.getConversationId());
        Assert.notNull(conversationEntity, "会话不存在");
        UserDeviceUtil.checkUserDevice(conversationEntity.getUserId(), conversationEntity.getDeviceId());
        ConversationEntity entity = new ConversationEntity();
        entity.setId(conversationEntity.getId());
        ConversationMetaData metaData = Optional.ofNullable(conversationEntity.getMetaData()).orElse(new ConversationMetaData());
        metaData.setChatModelConfig(cmd.getChatModelConfig());
        entity.setMetaData(metaData);
        entity.update();
        return Response.buildSuccess();
    }
}
