package com.dangbei.aisearch.domain.gateway;

import com.dangbei.aisearch.domain.common.base.BaseGateway;
import com.dangbei.aisearch.domain.entity.RiskEntity;

/**
 * Risk 网关层
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-04-19
 */
public interface RiskGateway extends BaseGateway<Long, RiskEntity> {

    /**
     * 是否为风险IP
     * @param ip IP地址
     * @return boolean
     */
    boolean isIpRisk(String ip);

    /**
     * 是否在白名单中
     * @param userIdDefaultDeviceId 用户ID或设备ID
     * @return boolean
     */
    boolean inWhiteList(String userIdDefaultDeviceId);

    /**
     * 添加到白名单
     * @param userIdDefaultDeviceId 用户ID或设备ID
     * @return boolean
     */
    boolean addWhiteList(String userIdDefaultDeviceId);
}
