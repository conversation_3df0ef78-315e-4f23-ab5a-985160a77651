package com.dangbei.aisearch.infrastructure.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.CommunityPostLikeDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.CommunityPostLikeMapper;
import com.dangbei.aisearch.infrastructure.repository.CommunityPostLikeRepository;
import org.springframework.stereotype.Repository;

/**
 * CommunityPostLike 仓库实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-31
 */
@Repository
public class CommunityPostLikeRepositoryImpl extends ServiceImpl<CommunityPostLikeMapper, CommunityPostLikeDO> implements CommunityPostLikeRepository {

}
