package com.dangbei.aisearch.app.executor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.common.util.NanoIdUtil;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson2.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.dangbei.aisearch.client.dto.clientobject.SendSmsCo;
import com.dangbei.aisearch.client.dto.cmd.AppSendSmsCmd;
import com.dangbei.aisearch.client.dto.cmd.SendSmsCmd;
import com.dangbei.aisearch.common.constant.CacheKey;
import com.dangbei.aisearch.common.constant.LockKey;
import com.dangbei.aisearch.common.enums.SmsSceneCodeEnum;
import com.dangbei.aisearch.domain.gateway.ExternalCommonGateway;
import com.dangbei.aisearch.infrastructure.config.properties.SmsProperties;
import com.dangbei.framework.insight.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 发送验证码 <a href="https://alidocs.dingtalk.com/i/nodes/qnYMoO1rWxDLD6oAuM2knE6vW47Z3je9">...</a>
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-01-07
 */
@Slf4j
@Component
public class SendSmsCmdExe {

    @Resource
    private SmsProperties smsProperties;
    @Resource
    private ExternalCommonGateway externalCommonGateway;
    public static final String SEND_MSG_ERROR = "短信发送失败,请稍后再试";
    public static final String OPERATE_FREQUENT_MSG = "操作频繁,请稍后再试";
    public static final String DAILY_OPERATE_FREQUENT_MSG = "每日短信发送次数已达上限";
    public static final Long LOCK_TIME = 60L;
    public static final Long LOCK_TIME_SECONDS = 86400L;
    @NacosValue(value = "${ai-search.mockLogin.enabled:false}", autoRefreshed = true)
    private boolean mockEnabled;
    @NacosValue(value = "${sa-token.mock-mobiles}", autoRefreshed = true)
    private String mockMobiles;

    @Lock4j(keys = "#cmd.mobile", expire = 5000L)
    public SingleResponse<SendSmsCo> execute(SendSmsCmd cmd) {
        // ①检查redis，判断是否频繁
        Object value = RedisUtil.get(String.format(LockKey.SEND_SMS_CODE, cmd.getMobile()));
        if (Objects.nonNull(value)) {
            log.info("短信发送频繁={}", cmd.getMobile());
            return SingleResponse.of(new SendSmsCo()
                .setCaptchaResult(false)
                .setBizResult(false)
                .setBizErrorMsg(OPERATE_FREQUENT_MSG)
            );
        }

        // ②调用阿里云发起验证咨询，计费点
        boolean captchaVerify = externalCommonGateway.captchaVerify(cmd.getCaptchaVerifyParam(), cmd.getSceneId());
        log.info("阿里云风险验证结果={}", captchaVerify);
        if (!captchaVerify) {
            boolean captchaFailFrequent = isCaptchaVerifyFailFrequent(cmd.getMobile());
            log.info("是否频繁失败={}", captchaFailFrequent);
            SendSmsCo build;
            if (captchaFailFrequent) {
                // 一分钟内三次失败，关闭弹窗
                build = new SendSmsCo()
                    .setCaptchaResult(false)
                    .setBizResult(false)
                    .setBizErrorMsg(OPERATE_FREQUENT_MSG);
            } else {
                // 单次验证失败，让用户继续验证
                build = new SendSmsCo()
                    .setCaptchaResult(false)
                    .setBizResult(false)
                    .setBizErrorMsg(StrUtil.EMPTY);
            }
            return SingleResponse.of(build);
        }

        // ③生成验证码，发送短信
        String code = NanoIdUtil.randomNumberNanoId(6);
        log.info("{}生成的验证码={}", cmd.getMobile(), code);
        Response sendResult = parseSendResult(sendSms(cmd.getMobile(), cmd.getSceneCode(), code));
        if (!sendResult.isSuccess()) {
            RedisUtil.set(String.format(LockKey.SEND_SMS_CODE, cmd.getMobile()), 1, LOCK_TIME);
            return SingleResponse.of(new SendSmsCo()
                .setCaptchaResult(true)
                .setBizResult(false)
                .setBizErrorMsg(sendResult.getErrMessage())
            );
        }

        // ④短信发送成功，设置code缓存
        RedisUtil.set(String.format(LockKey.SEND_SMS_CODE, cmd.getMobile()), 1, LOCK_TIME);
        RedisUtil.set(genLoginCodeCacheKey(cmd.getMobile()), code, 60 * 5);

        log.info("验证码下发成功");
        return SingleResponse.of(new SendSmsCo()
            .setCaptchaResult(true)
            .setBizResult(true)
            .setBizErrorMsg(StrUtil.EMPTY)
        );
    }


    /**
     * app 发送验证码，不需要滑块
     * @param cmd
     * @return
     */
    @Lock4j(keys = "#cmd.mobile", expire = 5000L)
    public Response execute(AppSendSmsCmd cmd) {
        // mock登录逻辑
        if (mockEnabled) {
            boolean isMockLogin = isMockLogin(cmd.getMobile());
            if (isMockLogin) {
                log.info("MOCK登录，不下发验证码");
                return SingleResponse.of(new SendSmsCo()
                    .setCaptchaResult(true)
                    .setBizResult(true)
                    .setBizErrorMsg(StrUtil.EMPTY)
                );
            }
        }

        var mobile = cmd.getMobile();
        // ①检查redis，判断是否频繁
        Object value = RedisUtil.get(String.format(LockKey.SEND_SMS_CODE, mobile));
        if (Objects.nonNull(value)) {
            log.warn("短信发送频繁={}", cmd.getMobile());
            throw new BizException(OPERATE_FREQUENT_MSG);
        }

        // 每个自然日最多发10条
        String dailyLimitKey = String.format(LockKey.SEND_SMS_DAILY_LIMIT, mobile, LocalDate.now());
        if (RedisUtil.isFrequent(dailyLimitKey, 10, LOCK_TIME_SECONDS)) {
            log.warn("短信发送次数超过每日限制={}", mobile);
            throw new BizException(DAILY_OPERATE_FREQUENT_MSG);
        }

        // ③生成验证码，发送短信
        String code = NanoIdUtil.randomNumberNanoId(6);
        log.info("{}生成的验证码={}", mobile, code);
        Response sendResult = parseSendResult(sendSms(mobile, cmd.getSceneCode(), code));
        if (!sendResult.isSuccess()) {
            RedisUtil.set(String.format(LockKey.SEND_SMS_CODE, cmd.getMobile()), 1, LOCK_TIME);
            throw new BizException(sendResult.getErrMessage());
        }

        // ④短信发送成功，设置code缓存
        RedisUtil.set(String.format(LockKey.SEND_SMS_CODE, cmd.getMobile()), 1, LOCK_TIME);
        RedisUtil.set(genLoginCodeCacheKey(cmd.getMobile()), code, 60 * 5);

        log.info("验证码下发成功");
        return SingleResponse.of(new SendSmsCo()
            .setCaptchaResult(true)
            .setBizResult(true)
            .setBizErrorMsg(StrUtil.EMPTY)
        );
    }

    /**
     * 解析SMS服务发送结果
     * @param smsResponse SMS服务发送结果
     * @return 解析后的结果
     */
    private Response parseSendResult(Response smsResponse) {
        if (Objects.nonNull(smsResponse) && smsResponse.isSuccess()) {
            return Response.buildSuccess();
        }

        String errMessage = SEND_MSG_ERROR;
        if (JSON.isValid(smsResponse.getErrMessage())) {
            String errorCode = Optional.ofNullable(JSON.parseObject(smsResponse.getErrMessage()))
                .map(jsonObject -> jsonObject.getString("code"))
                .orElse(null);
            if (StringUtils.equalsIgnoreCase(errorCode, "isv.BUSINESS_LIMIT_CONTROL")) {
                errMessage = OPERATE_FREQUENT_MSG;
            }
        }
        return Response.buildFailure("-1", errMessage);
    }


    /**
     * 拼图验证是否频繁失败
     * @param mobile 手机号
     * @return 验证结果
     */
    private boolean isCaptchaVerifyFailFrequent(String mobile) {
        return RedisUtil.isFrequent(String.format(LockKey.CAPTCHA_VERIFY_FAIL, mobile), 5, LOCK_TIME);
    }


    /**
     * 生成短信缓存key
     * @param mobile 手机号
     * @return {@link String }
     */
    public static String genLoginCodeCacheKey(String mobile) {
        return String.format(CacheKey.SMS_LOGIN_CODE, mobile);
    }

    /**
     * 发送短信
     * @param mobile 手机号
     * @return {@link String }
     */
    public Response sendSms(String mobile, String sceneCode, String code) {
        String templateCode = smsProperties.getTemplateCode(sceneCode);
        Map<String, Object> templateParamMap = new HashMap<>();
        SmsSceneCodeEnum sceneCodeEnum = SmsSceneCodeEnum.getBySceneCode(sceneCode);
        if (Objects.equals(sceneCodeEnum, SmsSceneCodeEnum.LOGIN)) {
            templateParamMap.put("code", code);
        }
        return externalCommonGateway.sendSms(mobile, templateCode, templateParamMap);
    }

    public boolean isMockLogin(String mobile) {
        if (StrUtil.isBlank(mockMobiles)) {
            return false;
        }

        List<String> mockList = List.of(mockMobiles.split(StringPool.COMMA));
        if (CollUtil.isEmpty(mockList)) {
            return false;
        }

        return mockList.contains(mobile);
    }

}
