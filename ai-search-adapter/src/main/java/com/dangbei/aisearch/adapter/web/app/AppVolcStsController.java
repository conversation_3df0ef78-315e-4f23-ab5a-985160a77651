package com.dangbei.aisearch.adapter.web.app;

import com.alibaba.cola.dto.SingleResponse;
import com.dangbei.aisearch.app.executor.VolcStsTokenCmdExe;
import com.dangbei.aisearch.client.dto.clientobject.VolcStsTokenCo;
import com.dangbei.aisearch.common.enums.VolcStsTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025-03-24 22:20
 **/
@Tag(name = "AppUserInfoController", description = "AppUserInfoController服务")
@RequestMapping("/app/volcStsApi/v1")
@RestController
public class AppVolcStsController {

    @Resource
    private VolcStsTokenCmdExe volcStsTokenCmdExe;

    @PostMapping("/tts/jwtToken")
    @Operation(summary = "获取火山引擎tts临时token", description = "获取火山引擎tts临时token")
    public SingleResponse<VolcStsTokenCo> ttsJwtToken() {
        return volcStsTokenCmdExe.execute(VolcStsTypeEnum.TTS);
    }

    @PostMapping("/asr/jwtToken")
    @Operation(summary = "获取火山引擎asr临时token", description = "获取火山引擎asr临时token")
    public SingleResponse<VolcStsTokenCo> asrJwtToken() {
        return volcStsTokenCmdExe.execute(VolcStsTypeEnum.ASR);
    }
}
