package com.dangbei.aisearch.app.executor;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.dashscope.common.Message;
import com.alibaba.dashscope.common.Role;
import com.alibaba.fastjson2.JSON;
import com.dangbei.aisearch.client.dto.cmd.AgentSystemPromptGenCmd;
import com.dangbei.aisearch.client.enums.AgentRoleEnum;
import com.dangbei.aisearch.infrastructure.config.properties.DashScopeProperties;
import com.dangbei.aisearch.infrastructure.i18n.I18nUtil;
import com.dangbei.aisearch.infrastructure.prompt.PromptParam;
import com.dangbei.aisearch.infrastructure.prompt.PromptUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 智能体系统提示词润色执行器
 *
 * <AUTHOR>
 * @date 2025-03-27 10:57
 **/
@Component
@Slf4j
public class AgentSystemPromptPolishCmdExe extends TextGenerationCmdExe {

    @Resource
    private DashScopeProperties dashScopeProperties;
    @Resource
    private PromptUtil promptUtil;

    public SingleResponse<String> execute(AgentSystemPromptGenCmd cmd) {
        List<Message> messages = new ArrayList<>();
        DashScopeProperties.ModelConfig modelConfig = dashScopeProperties.getPolishSystemPromptConfig();
        String promptTmpl = I18nUtil.isChinese() ? modelConfig.getSystem() : modelConfig.getEnSystem();

        Map<String, Object> extParam = Maps.newHashMap();
        if (StringUtils.isNotBlank(cmd.getName())) {
            extParam.put("name", cmd.getName());
        }
        extParam.put("role", AgentRoleEnum.getDesc(cmd.getAgentRole()));
        extParam.put("userPrompt", cmd.getUserPrompt());

        extParam.put("gender", cmd.getGender());
        if (StringUtils.isNotBlank(cmd.getPersonality())) {
            extParam.put("personality", cmd.getPersonality());
        }
        if (CollectionUtils.isNotEmpty(cmd.getChatExample())) {
            extParam.put("chatExample", JSON.toJSON(cmd.getChatExample()));
        }

        String prompt = promptUtil.format(PromptParam.builder()
            .promptTmpl(promptTmpl)
            .extParam(extParam)
            .build());

        messages.add(Message.builder().role(Role.SYSTEM.getValue()).content(prompt).build());

        var polishedSystemPrompt = execute(messages, modelConfig.getModel(), ex -> {
            log.error("润色智能体系统提示词异常:{}", ex.getMessage(), ex);
            return SingleResponse.buildFailure("-1", "润色智能体系统提示词异常", String.class);
        });

        return SingleResponse.of(polishedSystemPrompt);
    }
}
