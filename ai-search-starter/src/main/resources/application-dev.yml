nacos:
  config:
    # 开启预加载，必须
    bootstrap:
      enable: true
    # 主配置 配置文件类型
    type: yaml
    server-addr: mse-3b18dad6-nacos-ans.mse.aliyuncs.com:8848
    access-key: LTAI5tDx4G5wrKk1b1D2u5PA
    secret-key: ******************************
    data-id: com.dangbei.ai-search.application.yaml
    auto-refresh: true
    namespace: 7e2ac45d-cdb6-4378-836c-af5f4a70a1c3
    group: ai-search

sign:
  isEnable: false

spring:
  schedulerx2:
    enabled: false

## ==========
## 当贝AI公众号配置
## ==========
wx:
  mp:
    app-id: wx945777c0fd2d2b33
    secret: c5ca2a22804338554d54cfdcbeb0b4bf
    config-storage:
      http-client-type: okhttp
      type: RedisTemplate

# ==========
# insight配置
# ==========
insight:
  signature:
    enable: false
  rocketmq:
    endpoint: rmq-cn-zvp32tzyp29.cn-shenzhen.rmq.aliyuncs.com:8080
    access-key: kuo93j0j5kG2GagU
    secret-key: F5x0Fty0Md97I3XH
    producer:
      request-timeout: 5
      max-attempts: 3
      topic: 'TOPIC_AI_SEARCH'
    push-consumer:
      consumption-thread-count: 20
