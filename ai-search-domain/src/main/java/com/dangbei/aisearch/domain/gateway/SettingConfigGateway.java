package com.dangbei.aisearch.domain.gateway;

import com.dangbei.aisearch.domain.common.base.BaseGateway;
import com.dangbei.aisearch.domain.entity.SettingConfigEntity;

/**
 * SettingConfig 网关层
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-04-11
 */
public interface SettingConfigGateway extends BaseGateway<Long, SettingConfigEntity> {

    /**
     * 获取Setting配置
     * @param userId   用户ID
     * @param deviceId 设备ID
     * @return 配置
     */
    SettingConfigEntity getConfig(String userId, String deviceId);

}
