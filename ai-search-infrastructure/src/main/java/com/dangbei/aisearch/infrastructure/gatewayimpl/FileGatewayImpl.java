package com.dangbei.aisearch.infrastructure.gatewayimpl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dangbei.aisearch.client.dto.cmd.ChatCmd;
import com.dangbei.aisearch.domain.entity.FileEntity;
import com.dangbei.aisearch.domain.gateway.FileGateway;
import com.dangbei.aisearch.infrastructure.common.base.BaseGatewayImpl;
import com.dangbei.aisearch.infrastructure.convertor.FileConvertor;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.FileDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.FileMapper;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * File 网关层实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-01-20
 */
@Component
public class FileGatewayImpl extends BaseGatewayImpl<Long, FileEntity, FileDO, FileMapper, FileConvertor> implements FileGateway {

    @Resource
    private FileMapper fileMapper;
    @Resource
    private FileConvertor fileConvertor;

    @Override
    public FileEntity getByMd5(String md5) {
        LambdaQueryWrapper<FileDO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(FileDO::getMd5, md5);
        wrapper.last(LIMIT_ONE);
        return fileConvertor.toEntity(fileMapper.selectOne(wrapper));
    }

    @Override
    public FileEntity getByUrl(String url) {
        LambdaQueryWrapper<FileDO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(FileDO::getFileUrl, url);
        wrapper.last(LIMIT_ONE);
        return fileConvertor.toEntity(fileMapper.selectOne(wrapper));
    }

    @Override
    public Map<String, FileEntity> fileIdMapping(List<ChatCmd.FileItem> files) {
        if (CollUtil.isEmpty(files)) {
            return Maps.newHashMap();
        }
        List<String> fileIds = files.stream().
            filter(i -> "file".equals(i.getType()) && StrUtil.startWith(i.getFileId(), "file-fe"))
            .map(ChatCmd.FileItem::getFileId)
            .collect(Collectors.toList());
        if (CollUtil.isEmpty(fileIds)) {
            return Maps.newHashMap();
        }

        LambdaUpdateWrapper<FileDO> wrapper = Wrappers.lambdaUpdate();
        wrapper.in(FileDO::getFileId, fileIds);
        return fileConvertor.toEntityList(fileMapper.selectList(wrapper))
            .stream()
            .collect(Collectors.toMap(FileEntity::getFileId, Function.identity()));
    }

    @Override
    public List<FileEntity> listMyFileId(List<String> fileIds) {
        if (CollUtil.isEmpty(fileIds)) {
            return List.of();
        }
        LambdaUpdateWrapper<FileDO> wrapper = Wrappers.lambdaUpdate();
        wrapper.in(FileDO::getFileId, fileIds);
        return new ArrayList<>(fileConvertor.toEntityList(fileMapper.selectList(wrapper)));
    }
}
