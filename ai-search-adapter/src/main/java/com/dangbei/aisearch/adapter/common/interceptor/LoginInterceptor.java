package com.dangbei.aisearch.adapter.common.interceptor;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import com.dangbei.aisearch.common.constant.RequestHeaderConst;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.DispatcherType;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * 登录会话拦截器
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-24
 */
@Slf4j
@Order(0)
@Component
public class LoginInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) {
        if (!Objects.equals(request.getDispatcherType(), DispatcherType.REQUEST)) {
            return true;
        }
        // 登录token校验
        if (StrUtil.isNotBlank(request.getHeader(RequestHeaderConst.TOKEN))) {
            StpUtil.checkLogin();
        }
        return true;
    }
}
