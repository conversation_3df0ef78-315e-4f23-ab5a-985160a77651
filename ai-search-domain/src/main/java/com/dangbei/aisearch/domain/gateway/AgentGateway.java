package com.dangbei.aisearch.domain.gateway;

import com.dangbei.aisearch.domain.common.base.BaseGateway;
import com.dangbei.aisearch.domain.entity.AgentEntity;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Agent 网关层
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-28
 */
public interface AgentGateway extends BaseGateway<Long, AgentEntity> {

    /**
     * 根据agentId查询
     * @param agentId 智能体唯一ID
     * @return {@link AgentEntity }
     */
    AgentEntity getByAgentId(String agentId);

    /**
     * 根据agentId列表查询
     * @param agentIds 智能体唯一ID列表
     * @return {@link List }<{@link AgentEntity }>
     */
    Map<String, AgentEntity> getAgentMap(Collection<String> agentIds);

    /**
     * 查询所有的agentId
     * @return {@link List }<{@link String }>
     */
    List<String> listAllOfficialAgentId();

    /**
     * 条件更新智能体信息（仅当 online_status 与预期值一致时才更新）
     * @param agentEntity 智能体信息
     * @param expectedOnlineStatus 预期的 online_status 值
     * @return 是否更新成功
     */
    boolean updateWithOnlineStatusCheck(AgentEntity agentEntity, Integer expectedOnlineStatus);


}
