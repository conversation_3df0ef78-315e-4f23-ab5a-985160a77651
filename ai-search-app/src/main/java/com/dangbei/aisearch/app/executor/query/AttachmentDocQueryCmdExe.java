package com.dangbei.aisearch.app.executor.query;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.file.FileNameUtil;
import com.dangbei.aisearch.client.dto.clientobject.AttachmentDocInfoCo;
import com.dangbei.aisearch.client.dto.cmd.query.DocInfosQuery;
import com.dangbei.aisearch.common.util.TimeUtil;
import com.dangbei.aisearch.domain.entity.ConversationAttachmentEntity;
import com.dangbei.aisearch.domain.gateway.ConversationAttachmentGateway;
import com.dangbei.aisearch.infrastructure.common.helper.DocAccessHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 附件查询
 *
 * <AUTHOR>
 * @date 2025-04-18 17:07
 **/
@Component
@Slf4j
public class AttachmentDocQueryCmdExe {

    @Resource
    private ConversationAttachmentGateway conversationAttachmentGateway;
    @Resource
    private DocAccessHelper docAccessHelper;

    public List<AttachmentDocInfoCo> execute(DocInfosQuery docInfosQuery) {
        var attachments = conversationAttachmentGateway.listByDocIds(docInfosQuery.getDocIds());
        if (CollUtil.isEmpty(attachments)) {
            return List.of();
        }
        List<AttachmentDocInfoCo> cos = new ArrayList<>();
        for (ConversationAttachmentEntity attachment : attachments) {
            cos.add(
                AttachmentDocInfoCo.builder()
                    .docId(attachment.getDocId())
                    .filePath(attachment.getDocPath())
                    .fileUrl(docAccessHelper.getAttachmentDocAccessUrl(attachment))
                    .processStatus(attachment.getProcessStatus())
                    .docName(attachment.getDocName())
                    .docType(FileNameUtil.getSuffix(attachment.getDocName()).toLowerCase())
                    .docSize(Long.parseLong(attachment.getDocSize().toString()))
                    .createTime(TimeUtil.getTimestamp(attachment.getCreateTime()))
                    .build()
            );
        }
        return cos;
    }
}
