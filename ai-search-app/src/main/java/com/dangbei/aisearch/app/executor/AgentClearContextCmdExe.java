package com.dangbei.aisearch.app.executor;

import com.alibaba.cola.dto.Response;
import com.dangbei.aisearch.app.util.UserDeviceUtil;
import com.dangbei.aisearch.client.dto.ChatMsgExt;
import com.dangbei.aisearch.client.dto.UserDeviceDTO;
import com.dangbei.aisearch.client.dto.cmd.AgentClearContextCmd;
import com.dangbei.aisearch.domain.entity.AgentConversationEntity;
import com.dangbei.aisearch.domain.entity.ChatMessageEntity;
import com.dangbei.aisearch.domain.gateway.AgentConversationGateway;
import com.dangbei.aisearch.domain.gateway.ChatMessageGateway;
import io.jsonwebtoken.lang.Assert;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 智能体上下文清楚命令执行器
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-28
 */
@Component
public class AgentClearContextCmdExe {

    @Resource
    private ChatMessageGateway chatMessageGateway;
    @Resource
    private AgentConversationGateway agentConversationGateway;

    public Response execute(AgentClearContextCmd cmd) {
        UserDeviceDTO userDeviceInfo = UserDeviceUtil.getUserDeviceInfo();
        AgentConversationEntity conversationEntity = agentConversationGateway.getByUserDeviceInfoAndAgentId(userDeviceInfo, cmd.getAgentId());
        Assert.notNull(conversationEntity, "智能体上下文不存在");

        ChatMessageEntity latestMsg = chatMessageGateway.getLatestMsg(conversationEntity.getConversationId());
        if (Objects.isNull(latestMsg)) {
            return Response.buildSuccess();
        }

        // 增加上下文清空标记
        ChatMsgExt ext = latestMsg.getExt();
        if (Objects.nonNull(ext) && Objects.nonNull(ext.getCtxClearFlag())) {
            ext.setCtxClearFlag(true);
            latestMsg.setExt(ext);
            latestMsg.update();
        }

        // 记录最新一条会话ID
        conversationEntity.setContextMsgId(latestMsg.getMsgId());
        conversationEntity.update();
        return Response.buildSuccess();
    }

}
