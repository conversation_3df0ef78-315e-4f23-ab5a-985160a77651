package com.dangbei.aisearch.app.util;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.stp.StpUtil;
import com.dangbei.aisearch.client.dto.clientobject.UserInfoCo;
import lombok.experimental.UtilityClass;

import java.util.Objects;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-01-10
 */
@UtilityClass
public class UserContextUtil {

    public static UserInfoCo getUserInfo() {
        return RequestThreadLocalUtil.getUserInfo();
    }

    public static String getUserId() {
        UserInfoCo userInfoCo = getUserInfo();
        return Objects.isNull(userInfoCo) ? null : userInfoCo.getUserId();
    }

    public static UserInfoCo getNonNullUserInfo() {
        UserInfoCo userInfoCo = getUserInfo();
        if (Objects.isNull(userInfoCo)) {
            throw new NotLoginException("用户未登录", StpUtil.getLoginType(), NotLoginException.NOT_TOKEN);
        }
        return userInfoCo;
    }

    public static String getNonNullUserId() {
        UserInfoCo userInfoCo = getNonNullUserInfo();
        return userInfoCo.getUserId();
    }
}
