package com.dangbei.aisearch.client.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;
import com.dangbei.aisearch.client.dto.AgentChatExample;
import com.dangbei.aisearch.client.dto.AgentFollowUp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-15 10:34
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class AgentVersionCo extends ClientObject {

    @Schema(description = "智能体Id")
    private String agentId;

    @Schema(description = "智能体角色类型：0-角色类 1-助理类")
    private Integer agentRole;

    @Schema(description = "智能体")
    @NotBlank(message = "智能体头像不能为空")
    private String agentAvatar;

    @Schema(description = "APP端背景图")
    private String appBgUrl;

    @Schema(description = "智能体背景主色调")
    private String appBgColorTone;

    @Schema(description = "智能体名称")
    @NotBlank(message = "智能体名称不能为空")
    private String agentName;

    @Schema(description = "智能体性别：0-男 1-女 2-非人类角色")
    private Integer gender;

    @Schema(description = "角色/工具设定")
    private String systemPrompt;

    @Schema(description = "智能体语音开关 0-关 1-开")
    private Integer voiceEnabled;

    @Schema(description = "智能体音色：https://www.volcengine.com/docs/6561/1257544")
    private String voiceType;

    @Schema(description = "智能体公开状态：0-公开 1-私密 2-部分公开")
    private Integer visibility;

    @Schema(description = "角色/工具简介")
    private String intro;

    @Schema(description = "智能体开场白")
    private String greeting;

    @Schema(description = "推荐提问")
    private List<AgentFollowUp> followUp;

    @Schema(description = "角色性格描述")
    private String personality;

    @Schema(description = "智能体对话示例")
    private List<AgentChatExample> chatExample;

    @Schema(description = "智能体技能")
    private List<String> skills;

    @Schema(description = "审核状态：0-审核中 1-审核通过 2-机器审核拒绝 3-人工审核拒绝 4-审核中止")
    private Integer approveStatus;

}
