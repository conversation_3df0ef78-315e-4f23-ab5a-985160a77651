// package com.dangbei.aisearch.app.executor;
//
// import cn.dev33.satoken.exception.NotPermissionException;
// import cn.dev33.satoken.stp.StpUtil;
// import cn.hutool.core.collection.CollUtil;
// import cn.hutool.core.util.StrUtil;
// import com.alibaba.cola.common.constant.RequestConstant;
// import com.alibaba.cola.common.util.HttpUtil;
// import com.alibaba.cola.exception.Assert;
// import com.alibaba.cola.extension.BizScenario;
// import com.alibaba.cola.extension.ExtensionExecutor;
// import com.dangbei.aisearch.app.model.ChatContext;
// import com.dangbei.aisearch.app.modelchat.ModelChatSceneExtPt;
// import com.dangbei.aisearch.client.dto.cmd.ChatCmd;
// import com.dangbei.aisearch.client.dto.cmd.FileSyncCmd;
// import com.dangbei.aisearch.client.dto.cmd.query.MessageListQuery;
// import com.dangbei.aisearch.common.constant.CommonConst;
// import com.dangbei.aisearch.common.enums.BotCodeEnum;
// import com.dangbei.aisearch.common.enums.I18nValueEnum;
// import com.dangbei.aisearch.common.util.SafeExecuteUtil;
// import com.dangbei.aisearch.domain.entity.ChatMessageEntity;
// import com.dangbei.aisearch.domain.entity.ConversationEntity;
// import com.dangbei.aisearch.domain.entity.FileEntity;
// import com.dangbei.aisearch.domain.gateway.ChatMessageGateway;
// import com.dangbei.aisearch.domain.gateway.ConversationGateway;
// import com.dangbei.aisearch.infrastructure.config.properties.DeepSeekModelRateProperties;
// import com.dangbei.aisearch.infrastructure.i18n.I18nUtil;
// import lombok.extern.slf4j.Slf4j;
// import org.apache.commons.lang3.StringUtils;
// import org.slf4j.MDC;
// import org.springframework.stereotype.Component;
// import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
//
// import javax.annotation.Resource;
// import javax.servlet.http.HttpServletRequest;
// import java.util.LinkedList;
// import java.util.Objects;
//
// /**
//  * 聊天对话V2接口命令执行器
//  * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
//  * @version 1.0.0
//  * @since 2025-01-15
//  */
// @Slf4j
// @Component
// @Deprecated
// public class ChatV2CmdExe {
//
//     @Resource
//     private ExtensionExecutor extensionExecutor;
//     @Resource
//     private ConversationGateway conversationGateway;
//     @Resource
//     private ChatMessageGateway chatMessageGateway;
//     @Resource
//     private DashScopeFileSyncCmdExe dashScopeFileSyncCmdExe;
//     @Resource
//     private DeepSeekModelRateProperties deepSeekModelRateProperties;
//
//     public Object execute(ChatCmd cmd, HttpServletRequest request) {
//         // 更新上次聊天时间
//         SafeExecuteUtil.execute(() -> conversationGateway.updateLastChatTime(cmd.getConversationId()));
//         // 0.前置校验
//         ConversationEntity conversationEntity = conversationGateway.getByConversationId(cmd.getConversationId());
//         preCheck(cmd, conversationEntity);
//
//         // 1.拽出当前会话的历史聊天记录
//         LinkedList<ChatMessageEntity> history = new LinkedList<>(chatMessageGateway.query(new MessageListQuery().setConversationId(cmd.getConversationId())));
//         // 剔除包含错误响应的问答
//         history.removeIf(chatMessageEntity -> Objects.nonNull(chatMessageEntity.getExt()) && StringUtils.isNotBlank(chatMessageEntity.getExt().getFailMsg()));
//
//         // 2.创建SseEmitter
//         SseEmitter emitter = new SseEmitter(9 * 60 * 1000L);
//         String requestId = MDC.get(RequestConstant.REQUEST_ID);
//         emitter.onCompletion(() -> log.debug("emitter completed"));
//         emitter.onError(throwable -> {
//             MDC.put(RequestConstant.REQUEST_ID, requestId);
//             handleEmitterError(throwable, emitter);
//         });
//         emitter.onTimeout(() -> {
//             MDC.put(RequestConstant.REQUEST_ID, requestId);
//             log.warn("emitter timeout");
//         });
//
//         // 4.结合上下文判断意图识别（深度思考、生成图片、解析网页、文本对话）
//         String intent = getIntentCode(cmd);
//         log.info("策略判定结果：{}", intent);
//         Assert.notBlank(intent, "system error");
//
//         // 5.走具体意图分支
//         BizScenario bizScenario = BizScenario.valueOf(intent);
//         ChatContext chatCtx = new ChatContext(emitter, cmd, history, HttpUtil.getIp(request));
//
//         chatCtx.setSupportDownload(Objects.nonNull(conversationEntity) && conversationEntity.isWriteScene());
//         extensionExecutor.executeVoid(ModelChatSceneExtPt.class, bizScenario, i -> i.streamChat(chatCtx));
//         return emitter;
//     }
//
//     /**
//      * 前置校验
//      * @param cmd 入参
//      */
//     private void preCheck(ChatCmd cmd, ConversationEntity conversationEntity) {
//         // 0. 匿名会话检查
//         if (Objects.nonNull(conversationEntity) && conversationEntity.isAnonymous()) {
//             if (StringUtils.isBlank(cmd.getAnonymousKey()) || !Objects.equals(conversationEntity.getAnonymousKey(), cmd.getAnonymousKey())) {
//                 throw new NotPermissionException("会话不匹配");
//             }
//         }
//         // 1.场景校验
//         Assert.isTrue(BotCodeEnum.AI_SEARCH.eq(cmd.getBotCode()), I18nUtil.get(I18nValueEnum.CHAT_BOT_ERROR));
//
//         // 2.知识库场景需要校验登录
//         if (CollUtil.isNotEmpty(cmd.getFiles())
//             || CollUtil.isNotEmpty(cmd.getReference())
//             || (Objects.nonNull(cmd.getChatOption()) && cmd.getChatOption().isSearchKnowledge())) {
//             StpUtil.checkLogin();
//         }
//     }
//
//     private String getIntentCode(ChatCmd cmd) {
//         if ("qwen".equals(cmd.getModel())) {
//             if ("online".equals(cmd.getUserAction())) {
//                 return CommonConst.ModelIntent.QWEN_ONLINE;
//             }
//
//             return CommonConst.ModelIntent.QWEN;
//         }
//
//         if ("doubao".equals(cmd.getModel())) {
//             if ("online".equals(cmd.getUserAction())) {
//                 return CommonConst.ModelIntent.DOUBAO_ONLINE;
//             }
//
//             return CommonConst.ModelIntent.DOUBAO;
//         }
//
//         if ("deepseek".equals(cmd.getModel())) {
//             if ("online".equals(cmd.getUserAction())) {
//                 return deepSeekModelRateProperties.getModelIntent("online");
//             }
//             if ("deep".equals(cmd.getUserAction())) {
//                 return deepSeekModelRateProperties.getModelIntent("deep");
//             }
//             if (StrUtil.equalsAny(cmd.getUserAction(), "deep,online", "online,deep")) {
//                 return deepSeekModelRateProperties.getModelIntent("online_deep");
//             }
//             return deepSeekModelRateProperties.getModelIntent("common");
//         }
//
//         if ("deepseek-v3".equals(cmd.getModel())) {
//             if ("online".equals(cmd.getUserAction())) {
//                 return CommonConst.ModelIntent.DEEPSEEK_ONLINE_DOUBAO;
//             }
//
//             return CommonConst.ModelIntent.DEEPSEEK_DOUBAO;
//         }
//
//         if ("glm3".equals(cmd.getModel())) {
//             if ("online".equals(cmd.getUserAction())) {
//                 return CommonConst.ModelIntent.GLM3_ONLINE;
//             }
//
//             return CommonConst.ModelIntent.GLM3;
//         }
//
//         if ("moonshot_v1".equals(cmd.getModel())) {
//             if ("online".equals(cmd.getUserAction())) {
//                 return CommonConst.ModelIntent.MOONSHOT_V1_ONLINE;
//             }
//
//             return CommonConst.ModelIntent.MOONSHOT_V1;
//         }
//
//         return null;
//     }
//
//     /**
//      * 提取文件
//      * @param cmd 聊天参数命令
//      * @return 文件内容
//      */
//     private FileEntity extractFile(ChatCmd cmd) {
//         if (CollUtil.isEmpty(cmd.getFiles())) {
//             return null;
//         }
//         ChatCmd.FileItem fileItem = cmd.getFiles().stream().filter(i -> "file".equals(i.getType())).findFirst().orElse(null);
//         if (Objects.isNull(fileItem)) {
//             return null;
//         }
//
//         FileSyncCmd syncCmd = new FileSyncCmd();
//         syncCmd.setFileUrl(fileItem.getFileUrl());
//         syncCmd.setFileName(fileItem.getFileName());
//         FileEntity data = dashScopeFileSyncCmdExe.execute(syncCmd).getData();
//         if (Objects.nonNull(data)) {
//             fileItem.setFileId(data.getFileId());
//             // 存在同md5的情况，覆盖当前文件名
//             data.setFileName(fileItem.getFileName());
//         }
//         return data;
//     }
//
//     /**
//      * 错误处理方法，统一处理 emitter 错误并完成 emitter
//      * @param throwable 异常
//      * @param emitter   SseEmitter
//      */
//     private void handleEmitterError(Throwable throwable, SseEmitter emitter) {
//         log.warn(throwable.getMessage(), throwable);
//         emitter.completeWithError(throwable);
//     }
//
// }
