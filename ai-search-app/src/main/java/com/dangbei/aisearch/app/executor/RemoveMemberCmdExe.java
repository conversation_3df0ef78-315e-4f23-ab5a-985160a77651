package com.dangbei.aisearch.app.executor;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.exception.Assert;
import com.dangbei.aisearch.app.util.UserContextUtil;
import com.dangbei.aisearch.client.dto.NotificationJumpConfig;
import com.dangbei.aisearch.client.dto.cmd.RemoveMemberCmd;
import com.dangbei.aisearch.client.enums.NotificationBizTypeEnum;
import com.dangbei.aisearch.domain.entity.KnowledgeMemberEntity;
import com.dangbei.aisearch.domain.entity.SharedKnowledgeEntity;
import com.dangbei.aisearch.domain.entity.UserInfoEntity;
import com.dangbei.aisearch.domain.gateway.KnowledgeMemberGateway;
import com.dangbei.aisearch.domain.gateway.SharedKnowledgeGateway;
import com.dangbei.aisearch.domain.gateway.UserInfoGateway;
import com.dangbei.aisearch.domain.gateway.UserNotificationGateway;
import com.dangbei.aisearch.infrastructure.config.properties.NotificationProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.util.Optional;
import java.util.Set;

/**
 * 移除成员执行器
 * <AUTHOR>
 * @date 2025-05-28
 **/
@Slf4j
@Component
public class RemoveMemberCmdExe {

    @Resource
    private SharedKnowledgeGateway sharedKnowledgeGateway;
    @Resource
    private KnowledgeMemberGateway knowledgeMemberGateway;
    @Resource
    private UserInfoGateway userInfoGateway;
    @Resource
    private UserNotificationGateway userNotificationGateway;
    @Resource
    private NotificationProperties notificationProperties;

    /**
     * 执行移除成员
     * @param cmd 移除命令
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Response execute(RemoveMemberCmd cmd) {
        String currentUserId = UserContextUtil.getNonNullUserId();
        // 获取知识库信息
        SharedKnowledgeEntity knowledgeEntity = sharedKnowledgeGateway.getByKnowledgeId(cmd.getKnowledgeId());
        Assert.notNull(knowledgeEntity, "知识库不存在");
        // 获取目标成员信息
        String targetUserId = cmd.getUserId();
        KnowledgeMemberEntity memberEntity = knowledgeMemberGateway.getByKnowledgeIdAndUserId(
            cmd.getKnowledgeId(), targetUserId);
        Assert.notNull(memberEntity, "成员不存在");
        // 校验移除权限
        memberEntity.validateRemovePermission(currentUserId);
        // 执行移除操作
        memberEntity.delete();

        boolean transactionActive = TransactionSynchronizationManager.isActualTransactionActive();
        if (transactionActive) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    String knowledgeId = knowledgeEntity.getKnowledgeId();
                    // 发给被移出人
                    userNotificationGateway.sendText(
                        NotificationBizTypeEnum.KNOWLEDGE_REMOVE_MEMBER,
                        currentUserId,
                        targetUserId,
                        notificationProperties.getKnowledge().getRemoveMemberText(knowledgeEntity.getName()),
                        knowledgeId,
                        null
                    );

                    // 发给创建人和管理员
                    UserInfoEntity targetUser = userInfoGateway.getByUserId(targetUserId);
                    Set<String> userIds = knowledgeMemberGateway.listAdminMemberUserIdsExcludeCurrUserId(knowledgeId, currentUserId);
                    userNotificationGateway.sendTextBatch(
                        NotificationBizTypeEnum.KNOWLEDGE_REMOVE_OTHER_MEMBER,
                        currentUserId,
                        userIds,
                        notificationProperties.getKnowledge().getRemoveOtherMemberText(Optional.ofNullable(targetUser).map(UserInfoEntity::getNickname).orElse(""), knowledgeEntity.getName()),
                        knowledgeId,
                        new NotificationJumpConfig(notificationProperties.getKnowledge().getRemoveOtherMemberJumpConfig(), knowledgeId));
                }
            });
        }
        return Response.buildSuccess();
    }
}
