package com.dangbei.aisearch.app.executor.query;

import com.dangbei.aisearch.client.dto.clientobject.AvatarStyleCo;
import com.dangbei.aisearch.common.constant.DcParamConst;
import com.dangbei.aisearch.domain.gateway.DcParamGateway;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-26 17:04
 **/
@Component
@RequiredArgsConstructor
public class AgentAvatarStyleQueryExe {

    @Resource
    private DcParamGateway dcParamGateway;
    public List<AvatarStyleCo> execute() {
        return dcParamGateway.listParamVal(DcParamConst.Type.CONFIG, DcParamConst.ParamCode.AGENT_AVATAR_STYLE, AvatarStyleCo.class);
    }
}
