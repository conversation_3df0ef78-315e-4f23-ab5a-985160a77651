package com.dangbei.aisearch.common.constant;

import java.util.Map;

import static java.util.Map.entry;

/**
 * <a href="https://www.volcengine.com/docs/84313/1254615#failed-code%E6%8A%A5%E9%94%99%E7%A0%81%EF%BC%9A">...</a>
 * <AUTHOR>
 * @date 2025-03-25 19:56
 **/
public class VolcKnowledgeDocProcessFailCode {

    static Map<String, String> FAIL_CODE = Map.ofEntries(
        entry("10001", "10001:文档下载超时"),
        entry("10003", "10003:url 校验失败，请确认 url 链接"),
        entry("10005", "10005:飞书文档获取异常，请确认有效且授权"),
        entry("30001", "30001:超过知识库文件限制大小"),
        entry("35001", "35001:超过知识库切片数量限制"),
        entry("35002", "35002:FAQ 文档解析为空"),
        entry("35004", "35004:超过知识库 FAQ 文档 sheet 数量限制"),
        entry("36003", "36003:结构化文档表头不匹配"),
        entry("36004", "36004:结构化文档数据类型转换失败"),
        entry("36005", "36005:超过知识库结构化文档 sheet 数量限制"),
        entry("36006", "36006:超过知识库结构化文档有效行数限制"),
        entry("36007", "36007:结构化文档解析为空"),
        entry("44002", "44002:未知错误，请联系我们")
    );

    public static String getFailReason(String failCode) {
        return FAIL_CODE.getOrDefault(failCode, failCode + ":未知错误，请联系我们");
    }

    public static boolean isWarnIgnoreFailCode(String failCode) {
        return "44002".equals(failCode);
    }

}
