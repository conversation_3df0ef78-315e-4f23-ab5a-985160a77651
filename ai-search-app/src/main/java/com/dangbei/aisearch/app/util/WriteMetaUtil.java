package com.dangbei.aisearch.app.util;

import cn.hutool.core.util.ReUtil;
import com.dangbei.aisearch.client.dto.WritePlaceholder;
import com.dangbei.aisearch.client.dto.clientobject.WriteMetaCo;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;
import java.util.regex.Pattern;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-05-15
 */
public class WriteMetaUtil {

    private static final String REGEX = "\\$\\{([\\d}]+)}";

    private static final Pattern PATTERN = Pattern.compile(REGEX);

    public static List<Long> getMetaIdList(String content) {
        if (StringUtils.isBlank(content)) {
            return Lists.newArrayList();
        }
        List<String> idStrList = ReUtil.findAll(PATTERN, content, 1);
        if (CollectionUtils.isNotEmpty(idStrList)) {
            return idStrList.stream().map(Long::valueOf).toList();
        }
        return Lists.newArrayList();
    }

    public static String replaceIdToPlaceHolder(String showContent, WritePlaceholder placeholder, Long id) {
        String placeholderStr = Optional.ofNullable(placeholder).map(WritePlaceholder::getPlaceholder).orElse(null);
        if (StringUtils.isBlank(placeholderStr)) {
            return showContent;
        }
        if (!placeholderStr.startsWith("[")) {
            placeholderStr = "[" + placeholderStr;
        }
        if (!placeholderStr.endsWith("]")) {
            placeholderStr = placeholderStr + "]";
        }
        showContent = showContent.replace(String.format("${%s}", id), placeholderStr);
        return showContent;
    }

    public static String getShowContent(String content, List<WriteMetaCo> metaList) {
        if (StringUtils.isBlank(content) || CollectionUtils.isEmpty(metaList)) {
            return content;
        }
        String showContent = content;
        for (WriteMetaCo writeMetaCo : metaList) {
            showContent = replaceIdToPlaceHolder(showContent, writeMetaCo.getPlaceholder(), writeMetaCo.getId());
        }
        return showContent;
    }
}
