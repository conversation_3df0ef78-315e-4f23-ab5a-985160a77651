package com.dangbei.aisearch.infrastructure.search;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.WeightRandom;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.cola.dto.DTO;
import com.alibaba.fastjson2.JSON;
import com.aliyun.iqs20241111.Client;
import com.aliyun.iqs20241111.models.GenericSearchRequest;
import com.aliyun.iqs20241111.models.GenericSearchResponse;
import com.aliyun.iqs20241111.models.GenericSearchResult;
import com.aliyun.iqs20241111.models.ScorePageItem;
import com.aliyun.teaopenapi.models.Config;
import com.dangbei.aisearch.infrastructure.config.properties.WebSearchProperties;
import com.google.common.collect.Lists;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.tencentcloudapi.common.AbstractModel;
import com.tencentcloudapi.common.CommonClient;
import com.tencentcloudapi.common.Credential;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URI;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * Web搜索服务
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-22
 */
@Slf4j
@Service
public class WebSearchApi {

    @Resource
    private WebSearchProperties webSearchProperties;
    @Resource(name = "saveChatMessageAsyncExecutor")
    private Executor saveChatMessageAsyncExecutor;

    public SearchResponse webSearch(SearchRequest request) {
        try {
            if (CollectionUtils.isEmpty(request.getQuery())) {
                return new SearchResponse(request.getQuery());
            }

            // TODO 2025/2/18 临时改为只搜第一个
            List<String> query = Lists.newArrayList(request.getQuery().get(0));
            // 创建一个 CompletableFuture 列表来存储每个查询的异步任务
            List<CompletableFuture<List<SearchResponse.SearchResult>>> futures = query.stream()
                .map(item -> CompletableFuture.supplyAsync(() -> doRandomSearch(item), saveChatMessageAsyncExecutor))
                .toList();

            // 等待所有任务完成，并收集结果
            List<SearchResponse.SearchResult> results = futures.stream()
                .map(CompletableFuture::join)
                .flatMap(List::stream)
                .toList();

            // 根据 WebInfoCo.url 去重
            Map<String, SearchResponse.SearchResult> uniqueResultsMap = new LinkedHashMap<>();
            for (SearchResponse.SearchResult webInfo : results) {
                if (StrUtil.isNotBlank(webInfo.getUrl()) && StrUtil.isNotBlank(webInfo.getSummary())) {
                    uniqueResultsMap.put(webInfo.getUrl(), webInfo);
                }
            }

            // 将去重后的结果转换回列表，并重新设置 idIndex
            List<SearchResponse.SearchResult> uniqueResults = new ArrayList<>(uniqueResultsMap.values());
            for (int i = 0; i < uniqueResults.size(); i++) {
                uniqueResults.get(i).setIdIndex(Convert.toStr(i + 1));
            }

            // 将去重后的结果转换回列表
            return new SearchResponse(request.getQuery())
                .setSearchResults(uniqueResults);

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return new SearchResponse(request.getQuery());
        }
    }

    /**
     * 随机搜索
     * @param query 查询关键词
     * @return 搜索结果
     */
    private List<SearchResponse.SearchResult> doRandomSearch(String query) {
        // 使用WeightRandomLoadBalance随机选择一个搜索引擎
        String engine = webSearchProperties.random();
        // 根据引擎选择搜索
        if ("quark".equals(engine)) {
            return quarkSearch(query);
        } else if ("souGou".equals(engine)) {
            return souGouSearch(query);
        } else {
            // 默认走搜狗
            return souGouSearch(query);
        }
    }

    /**
     * 夸克搜索
     * @param query 查询关键词
     * @return 搜索结果
     */
    private List<SearchResponse.SearchResult> quarkSearch(String query) {
        Config config = new Config();
        config.setAccessKeyId(webSearchProperties.getQuarkSearchAk());
        config.setAccessKeySecret(webSearchProperties.getQuarkSearchSk());
        config.setEndpoint("iqs.cn-zhangjiakou.aliyuncs.com");

        try {
            GenericSearchRequest request = new GenericSearchRequest();
            request.setQuery(query);
            GenericSearchResponse response = new Client(config).genericSearch(request);
            GenericSearchResult result = response.getBody();
            log.info("阿里夸克搜索，结果：{}", JSON.toJSONString(result));
            List<ScorePageItem> pageItems = result.getPageItems();
            if (CollUtil.isEmpty(pageItems)) {
                return new ArrayList<>();
            }

            // score前三网页读mainText，其余网页读snippet
            int readIdx = 0;
            List<SearchResponse.SearchResult> resList = new ArrayList<>();
            for (int i = 0; i < pageItems.size(); i++) {
                ScorePageItem item = pageItems.get(i);
                resList.add(
                    new SearchResponse.SearchResult()
                        .setIdIndex(Convert.toStr(i + 1))
                        .setUrl(item.getLink())
                        .setTitle(item.getHtmlTitle())
                        .setSummary(readIdx++ < 3 ? item.getMainText() : item.getSnippet())
                        .setSiteName(item.getHostname())
                        .setIcon(StrUtil.isNotBlank(item.getHostLogo()) ? item.getHostLogo() : getIcon(item.getLink()))
                        .setDate(Objects.nonNull(item.getPublishTime()) ? DateUtil.format(new Date(item.getPublishTime()), DatePattern.NORM_DATETIME_PATTERN) : null)
                        .setWebContent(item.getMainText())
                );
            }
            return resList;

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
        return new ArrayList<>();
    }

    @SneakyThrows
    private List<SearchResponse.SearchResult> souGouSearch(String query) {
        Credential cred = new Credential(webSearchProperties.getTmsSecretId(), webSearchProperties.getTmsSecretKey());
        // 实例化要请求产品的CommonClient对象,依次传入产品名、产品版本、Credential、地域
        CommonClient client = new CommonClient("tms", "2020-12-29", cred, "ap-shanghai");
        SouGouSearchRequest req = new SouGouSearchRequest();
        req.setQuery(query);
        String resp = client.commonRequest(req, "SearchPro");
        log.info("搜狗搜索，结果：{}", resp);

        SouGouResp souGouResp = JSON.parseObject(resp, SouGouResp.class);
        List<String> pages = souGouResp.getResponse().getPages();
        if (CollUtil.isEmpty(pages)) {
            return new ArrayList<>();
        }


        List<SearchResponse.SearchResult> resList = new ArrayList<>();
        for (int i = 0; i < pages.size(); i++) {
            SouGouResp.Page page = JSON.parseObject(pages.get(i), SouGouResp.Page.class);
            resList.add(
                new SearchResponse.SearchResult()
                    .setIdIndex(Convert.toStr(i + 1))
                    .setUrl(page.getUrl())
                    .setTitle(page.getTitle())
                    .setSummary(page.getPassage())
                    .setSiteName(page.getSite())
                    .setIcon(StrUtil.isNotBlank(page.getFavicon()) ? page.getFavicon() : getIcon(page.getUrl()))
                    .setDate(page.getDate())
                    .setWebContent(page.getPassage())
            );
        }
        return resList;
    }

    private String getIcon(String url) {
        try {
            URI host = URLUtil.getHost(new URL(url));
            return host + "/favicon.ico";
        } catch (Exception ex) {
            log.warn(ex.getMessage(), ex);
            return "";
        }
    }

    @Data
    public static class SouGouSearchRequest extends AbstractModel {
        @Expose
        @SerializedName("Query")
        private String Query;
        @Expose
        @SerializedName("Mode")
        private Integer Mode = 2;

        @Override
        protected void toMap(HashMap<String, String> map, String prefix) {
            this.setParamSimple(map, prefix + "Content", this.Query);
            this.setParamSimple(map, prefix + "Mode", this.Mode);
        }
    }

    @Data
    public static class SouGouResp extends DTO {

        private Response Response;

        @Data
        public static class Response extends DTO {
            private String RequestId;
            private String Query;
            private String Uuid;
            private List<String> Pages;
        }

        @Data
        public static class Page extends DTO {
            private String date;
            private String favicon;
            private List<String> images;
            private String passage;
            private Double score;
            private String site;
            private String title;
            private String url;
        }
    }

    public static void main(String[] args) {
        List<WeightRandom.WeightObj<String>> weightList = new ArrayList<>();
        weightList.add(new WeightRandom.WeightObj<>("A", 0));
        weightList.add(new WeightRandom.WeightObj<>("B", 0));
        weightList.add(new WeightRandom.WeightObj<>("C", 40));
        weightList.add(new WeightRandom.WeightObj<>("D", 10));
        WeightRandom<String> wr = RandomUtil.weightRandom(weightList);
        String str = "";
        int num_a = 0, num_b = 0, num_c = 0, num_d = 0;
        int testCount = 10000;
        for (int i = 0; i < testCount; i++) {
            str = wr.next();
            switch (str) {
                case "A":
                    num_a = num_a + 1;
                    break;
                case "B":
                    num_b = num_b + 1;
                    break;
                case "C":
                    num_c = num_c + 1;
                    break;
                case "D":
                    num_d = num_d + 1;
                    break;
            }
        }
        System.out.println("A-" + num_a + "-------" + NumberUtil.div(num_a, testCount, 2) * 100 + "%");
        System.out.println("B-" + num_b + "-------" + NumberUtil.div(num_b, testCount, 2) * 100 + "%");
        System.out.println("C-" + num_c + "-------" + NumberUtil.div(num_c, testCount, 2) * 100 + "%");
        System.out.println("D-" + num_d + "-------" + NumberUtil.div(num_d, testCount, 2) * 100 + "%");
    }

}
