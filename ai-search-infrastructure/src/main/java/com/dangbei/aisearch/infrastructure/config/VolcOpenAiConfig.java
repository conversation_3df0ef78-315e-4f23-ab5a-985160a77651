package com.dangbei.aisearch.infrastructure.config;

import com.dangbei.aisearch.infrastructure.config.properties.AiGatewayProperties;
import com.dangbei.framework.insight.volcopenai.service.ArkService;
import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 火山-OpenAi配置
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-04-15
 */
@Configuration
public class VolcOpenAiConfig {

    @Resource
    private AiGatewayProperties aiGatewayProperties;

    @Bean(name = "openAiArkService")
    public ArkService openAiArkService() {
        AiGatewayProperties.InnerOkHttpConfig innerOkHttpConfig = aiGatewayProperties.getInnerOkHttpConfig();
        AiGatewayProperties.DispatcherConfig dispatcherConfig = innerOkHttpConfig.getDispatcher();
        AiGatewayProperties.ConnectionPoolConfig connectionPoolConfig = innerOkHttpConfig.getConnectionPool();

        // 自定义线程池
        Dispatcher dispatcher = getDispatcher(dispatcherConfig);

        return ArkService.builder()
            .baseUrl(aiGatewayProperties.getBaseUrl())
            .apiKey(aiGatewayProperties.getApiKey())
            .connectionPool(new ConnectionPool(connectionPoolConfig.getMaxIdleConnections(), connectionPoolConfig.getKeepAliveMinute(), TimeUnit.MINUTES))
            .dispatcher(dispatcher)
            .build();
    }

    @NotNull
    private static Dispatcher getDispatcher(AiGatewayProperties.DispatcherConfig dispatcherConfig) {
        ExecutorService executorService = new ThreadPoolExecutor(
            dispatcherConfig.getCorePoolSize(),  // 核心线程数
            dispatcherConfig.getMaximumPoolSize(),  // 最大线程数
            dispatcherConfig.getKeepAliveTime(),  // 空闲线程存活时间
            TimeUnit.SECONDS,  // 时间单位
            createQueue(dispatcherConfig.getWorkQueueSize())  // 任务队列
        );

        Dispatcher dispatcher = new Dispatcher(executorService);
        dispatcher.setMaxRequests(dispatcherConfig.getMaxRequests());  // 设置最大并发请求总数
        dispatcher.setMaxRequestsPerHost(dispatcherConfig.getMaximumPoolSize());  // 设置每个主机的最大并发请求数
        return dispatcher;
    }

    /**
     * Create the BlockingQueue to use for the ThreadPoolExecutor.
     * <p>A LinkedBlockingQueue instance will be created for a positive
     * capacity value; a SynchronousQueue else.
     * @param queueCapacity the specified queue capacity
     * @return the BlockingQueue instance
     * @see LinkedBlockingQueue
     * @see SynchronousQueue
     */
    private static BlockingQueue<Runnable> createQueue(int queueCapacity) {
        if (queueCapacity > 0) {
            return new LinkedBlockingQueue<>(queueCapacity);
        } else {
            return new SynchronousQueue<>();
        }
    }

}
