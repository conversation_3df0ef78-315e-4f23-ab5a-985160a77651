package com.dangbei.aisearch.client.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-01-13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RecommendQuestionListCo extends ClientObject {

    @Schema(description = "提问")
    private String question;

    @Schema(description = "详细提问")
    private String detailQuestion;

    @Schema(description = "emoji")
    private String emoji;
}
