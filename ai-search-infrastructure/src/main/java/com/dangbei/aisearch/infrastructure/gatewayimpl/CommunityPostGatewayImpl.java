package com.dangbei.aisearch.infrastructure.gatewayimpl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dangbei.aisearch.client.dto.clientobject.CommunityPostCo;
import com.dangbei.aisearch.common.util.MarkdownUtils;
import com.dangbei.aisearch.domain.entity.ChatMessageEntity;
import com.dangbei.aisearch.domain.entity.CommunityPostEntity;
import com.dangbei.aisearch.domain.entity.UserInfoEntity;
import com.dangbei.aisearch.domain.gateway.ChatMessageGateway;
import com.dangbei.aisearch.domain.gateway.CommunityPostGateway;
import com.dangbei.aisearch.domain.gateway.CommunityPostLikeGateway;
import com.dangbei.aisearch.domain.gateway.UserInfoGateway;
import com.dangbei.aisearch.infrastructure.common.base.BaseGatewayImpl;
import com.dangbei.aisearch.infrastructure.convertor.CommunityPostConvertor;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.CommunityPostDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.CommunityPostMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * CommunityPost 网关层实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-31
 */
@Component
public class CommunityPostGatewayImpl extends BaseGatewayImpl<Long, CommunityPostEntity, CommunityPostDO, CommunityPostMapper, CommunityPostConvertor> implements CommunityPostGateway {
    @Resource
    private CommunityPostConvertor communityPostConvertor;
    @Resource
    private ChatMessageGateway chatMessageGateway;
    @Resource
    private UserInfoGateway userInfoGateway;
    @Resource
    private CommunityPostLikeGateway communityPostLikeGateway;

    @NacosValue(value = "${ai-search.community.defaultNickname:该账号已注销}", autoRefreshed = true)
    private String defaultNickname;
    @NacosValue(value = "${ai-search.community.defaultAvatar:https://jt5.dangbei.net/wangbotai/images/AIAvartarDefault.png}", autoRefreshed = true)
    private String defaultAvatar;

    @Override
    public CommunityPostEntity getByPostId(String postId) {
        CommunityPostDO communityPostDO = this.lambdaQuery().eq(CommunityPostDO::getPostId, postId)
            .last(LIMIT_ONE)
            .one();
        return communityPostConvertor.toEntity(communityPostDO);
    }

    @Override
    public CommunityPostEntity getByAnswerMsgId(String answerMsgId) {
        CommunityPostDO communityPostDO = this.lambdaQuery().eq(CommunityPostDO::getAnswerMsgId, answerMsgId)
            .last(LIMIT_ONE)
            .one();
        return communityPostConvertor.toEntity(communityPostDO);
    }

    @Override
    public void deleteByAnswerMsgId(String msgId) {
        LambdaUpdateWrapper<CommunityPostDO> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(CommunityPostDO::getAnswerMsgId, msgId);
        this.baseMapper.delete(wrapper);
    }


    @Override
    public List<CommunityPostCo> getCommunityPost(List<CommunityPostEntity> entityList, String userId) {
        if (CollUtil.isEmpty(entityList)) {
            return new ArrayList<>();
        }

        List<String> postIds = new ArrayList<>();
        List<String> msgIds = new ArrayList<>();
        Set<String> userIds = new HashSet<>();

        for (CommunityPostEntity entity : entityList) {
            postIds.add(entity.getPostId());
            msgIds.add(entity.getAnswerMsgId());
            userIds.add(entity.getUserId());
        }

        List<ChatMessageEntity> chatMessageEntityList = chatMessageGateway.listByMsgIds(msgIds);
        Map<String, String> chatMessageMap = chatMessageEntityList.stream()
            .collect(Collectors.toMap(ChatMessageEntity::getMsgId, ChatMessageEntity::getContent));
        Map<String, UserInfoEntity> userInfoMap = userInfoGateway.getUserMap(new ArrayList<>(userIds));

        List<String> postIdList = new ArrayList<>();
        if (Objects.nonNull(userId)) {
            postIdList = communityPostLikeGateway.isLike(postIds, userId);
        }

        Map<String, Long> likeNumMap = communityPostLikeGateway.getLikeNumMap(postIds);

        List<CommunityPostCo> result = new ArrayList<>();
        for (CommunityPostEntity entity : entityList) {
            CommunityPostCo communityCo = new CommunityPostCo();
            communityCo.setPostId(entity.getPostId());
            communityCo.setTitle(entity.getTitle());
            String content = chatMessageMap.get(entity.getAnswerMsgId());
            communityCo.setContent(MarkdownUtils.removeDoc(MarkdownUtils.removeMarkdownTags(content)));
            communityCo.setIsLike(postIdList.contains(entity.getPostId()));
            communityCo.setLikeNum(ObjectUtil.defaultIfNull(likeNumMap.get(entity.getPostId()), 0L) + entity.getBaseLikeCount());
            communityCo.setUserId(entity.getUserId());
            communityCo.setCreateTime(entity.getCreateTime());
            UserInfoEntity userInfoEntity = userInfoMap.get(entity.getUserId());
            if (Objects.nonNull(userInfoEntity)) {
                communityCo.setNickname(userInfoEntity.getNickname());
                communityCo.setAvatar(userInfoEntity.getAvatar());
            } else {
                communityCo.setNickname(defaultNickname);
                communityCo.setAvatar(defaultAvatar);
            }
            result.add(communityCo);
        }
        return result;
    }

    @Override
    public CommunityPostEntity getByShareId(String shareId) {
        CommunityPostDO communityPostDO = this.lambdaQuery().eq(CommunityPostDO::getShareId, shareId)
            .last(LIMIT_ONE)
            .one();
        return communityPostConvertor.toEntity(communityPostDO);
    }

}
