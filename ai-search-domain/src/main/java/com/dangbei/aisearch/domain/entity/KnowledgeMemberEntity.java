package com.dangbei.aisearch.domain.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.cola.exception.Assert;
import com.alibaba.cola.exception.BizException;
import com.dangbei.aisearch.client.enums.SharedKnowledgeRoleEnum;
import com.dangbei.aisearch.domain.common.base.BaseEntity;
import com.dangbei.aisearch.domain.gateway.KnowledgeMemberGateway;
import com.dangbei.aisearch.domain.gateway.SharedKnowledgeGateway;
import com.dangbei.aisearch.domain.gateway.UserInfoGateway;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * KnowledgeMember 领域对象
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-05-27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(doNotUseGetters = true)
@Slf4j
public class KnowledgeMemberEntity extends BaseEntity<Long> {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "知识库ID")
    private String knowledgeId;

    @Schema(description = "用户ID")
    private String userId;

    /**
     * {@link SharedKnowledgeRoleEnum}
     */
    @Schema(description = "角色(1-创建人,2-管理员,3-成员)")
    private Integer role;

    @Schema(description = "加入时间")
    private LocalDateTime joinTime;

    @Schema(description = "邀请人ID")
    private String inviteUserId;

    private transient KnowledgeMemberGateway knowledgeMemberGateway = SpringUtil.getBean(KnowledgeMemberGateway.class);
    private transient SharedKnowledgeGateway sharedKnowledgeGateway = SpringUtil.getBean(SharedKnowledgeGateway.class);
    private transient UserInfoGateway userInfoGateway = SpringUtil.getBean(UserInfoGateway.class);

    @Override
    public Long getPrimaryId() {
        return this.id;
    }

    @Override
    public void setPrimaryId(Long id) {
        this.id = id;
    }

    @Override
    protected KnowledgeMemberGateway getGateWay() {
        return this.knowledgeMemberGateway;
    }

    // ==================== 业务方法 ====================

    /**
     * 更新成员角色
     * @param newRole 新角色
     */
    public void updateRole(SharedKnowledgeRoleEnum newRole) {
        Assert.notNull(newRole, "角色不能为空");
        // 校验角色变更合法性
        validateRoleChange(newRole);
        // 更新角色
        this.role = newRole.getCode();
        this.update();
    }

    /**
     * 验证角色变更合法性
     * @param newRole 新角色
     */
    private void validateRoleChange(SharedKnowledgeRoleEnum newRole) {
        // 1. 创建人角色不能被分配
        if (newRole == SharedKnowledgeRoleEnum.CREATOR) {
            throw new BizException("创建人角色不能被分配");
        }

        // 2. 检查当前角色
        if (isCreator()) {
            throw new BizException("创建人角色不能被修改");
        }
    }

    /**
     * 判断是否为创建人
     * @return 是否为创建人
     */
    public boolean isCreator() {
        return Objects.equals(this.role, SharedKnowledgeRoleEnum.CREATOR.getCode());
    }

    /**
     * 判断是否为管理员
     * @return 是否为管理员
     */
    public boolean isAdmin() {
        return Objects.equals(this.role, SharedKnowledgeRoleEnum.ADMIN.getCode());
    }

    /**
     * 判断是否为普通成员
     * @return 是否为普通成员
     */
    public boolean isMember() {
        return Objects.equals(this.role, SharedKnowledgeRoleEnum.MEMBER.getCode());
    }

    /**
     * 获取角色枚举
     * @return 角色枚举
     */
    public SharedKnowledgeRoleEnum getRoleEnum() {
        return SharedKnowledgeRoleEnum.getByCode(this.role);
    }

    /**
     * 判断是否可以被移除
     * @return 是否可以被移除
     */
    public boolean canBeRemoved() {
        // 创建人不能被移除
        return !isCreator();
    }

    /**
     * 校验移除权限
     * @param operatorId 操作者ID
     */
    public void validateRemovePermission(String operatorId) {
        Assert.notBlank(operatorId, "操作者ID不能为空");

        // 1. 创建人不能被移除
        if (isCreator()) {
            throw new BizException("创建人不能被移除");
        }

        // 2. 获取操作者信息
        SharedKnowledgeEntity knowledge = getKnowledgeEntity();
        SharedKnowledgeRoleEnum operatorRole = knowledge.getUserRole(operatorId);

        // 3. 校验操作者权限
        if (operatorRole != SharedKnowledgeRoleEnum.CREATOR && operatorRole != SharedKnowledgeRoleEnum.ADMIN) {
            throw new BizException("无权限移除成员");
        }

        // 4. 管理员不能移除创建人或其他管理员
        if (operatorRole == SharedKnowledgeRoleEnum.ADMIN && (isCreator() || isAdmin())) {
            throw new BizException("管理员不能移除创建人或其他管理员");
        }
    }

    /**
     * 校验角色变更权限
     * @param operatorId 操作者ID
     * @param newRole 新角色
     */
    public void validateRoleChangePermission(String operatorId, SharedKnowledgeRoleEnum newRole) {
        Assert.notBlank(operatorId, "操作者ID不能为空");
        Assert.notNull(newRole, "新角色不能为空");
        // 1. 创建人角色不能被分配
        if (newRole == SharedKnowledgeRoleEnum.CREATOR) {
            throw new BizException("创建人角色不能被分配");
        }
        // 2. 创建人不能被修改角色
        if (isCreator()) {
            throw new BizException("创建人角色不能被修改");
        }
        // 3. 获取操作者信息
        SharedKnowledgeEntity knowledge = getKnowledgeEntity();
        SharedKnowledgeRoleEnum operatorRole = knowledge.getUserRole(operatorId);
        // 4. 校验操作者权限
        if (operatorRole != SharedKnowledgeRoleEnum.CREATOR && operatorRole != SharedKnowledgeRoleEnum.ADMIN) {
            throw new BizException("无权限修改成员角色");
        }
        // 5. 管理员只能修改普通成员角色
        if (operatorRole == SharedKnowledgeRoleEnum.ADMIN && isAdmin()) {
            throw new BizException("管理员不能修改其他管理员角色");
        }
    }

    /**
     * 获取知识库实体
     * @return 知识库实体
     */
    public SharedKnowledgeEntity getKnowledgeEntity() {
        if (StringUtils.isBlank(this.knowledgeId)) {
            throw new BizException("知识库ID为空");
        }

        SharedKnowledgeEntity entity = sharedKnowledgeGateway.getByKnowledgeId(this.knowledgeId);
        if (Objects.isNull(entity)) {
            throw new BizException("知识库不存在");
        }

        return entity;
    }

    /**
     * 获取用户信息
     * @return 用户信息
     */
    public UserInfoEntity getUserInfo() {
        if (StringUtils.isBlank(this.userId)) {
            return null;
        }

        return userInfoGateway.getByUserId(this.userId);
    }
}
