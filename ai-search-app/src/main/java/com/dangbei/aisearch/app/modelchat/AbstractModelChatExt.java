package com.dangbei.aisearch.app.modelchat;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.common.constant.RequestConstant;
import com.alibaba.cola.common.util.NanoIdUtil;
import com.alibaba.dashscope.tokenizers.Tokenizer;
import com.alibaba.dashscope.tokenizers.TokenizerFactory;
import com.dangbei.aisearch.app.executor.MessageTtsCmdExe;
import com.dangbei.aisearch.app.executor.SplitSearchKeywordCmdExe;
import com.dangbei.aisearch.app.model.ChatContext;
import com.dangbei.aisearch.app.util.UserContextUtil;
import com.dangbei.aisearch.app.util.UserDeviceUtil;
import com.dangbei.aisearch.client.dto.clientobject.SplitSearchKeywordResult;
import com.dangbei.aisearch.client.dto.clientobject.TokenUsageCo;
import com.dangbei.aisearch.client.dto.cmd.ChatCmd;
import com.dangbei.aisearch.client.enums.TtsSourceEnum;
import com.dangbei.aisearch.common.constant.CacheKey;
import com.dangbei.aisearch.common.constant.CommonConst;
import com.dangbei.aisearch.common.enums.DocProcessStatusEnum;
import com.dangbei.aisearch.common.enums.RoleEnum;
import com.dangbei.aisearch.domain.entity.BaseDocEntity;
import com.dangbei.aisearch.domain.entity.ChatMessageEntity;
import com.dangbei.aisearch.domain.entity.UserKnowledgeEntity;
import com.dangbei.aisearch.domain.gateway.ExternalCommonGateway;
import com.dangbei.aisearch.domain.gateway.UserKnowledgeDocGateway;
import com.dangbei.aisearch.domain.gateway.UserKnowledgeGateway;
import com.dangbei.aisearch.infrastructure.attachment.AttachmentResponse;
import com.dangbei.aisearch.infrastructure.config.properties.AppConfigProperties;
import com.dangbei.aisearch.infrastructure.config.properties.KnowledgeDocProperties;
import com.dangbei.aisearch.infrastructure.config.properties.MonitorProperties;
import com.dangbei.aisearch.infrastructure.i18n.I18nUtil;
import com.dangbei.aisearch.infrastructure.knowledge.KnowledgeRequest;
import com.dangbei.aisearch.infrastructure.knowledge.KnowledgeResponse;
import com.dangbei.aisearch.infrastructure.knowledge.KnowledgeSearchApi;
import com.dangbei.aisearch.infrastructure.prompt.PromptParam;
import com.dangbei.aisearch.infrastructure.prompt.PromptUtil;
import com.dangbei.aisearch.infrastructure.prompt.RagResponse;
import com.dangbei.aisearch.infrastructure.search.SearchRequest;
import com.dangbei.aisearch.infrastructure.search.SearchResponse;
import com.dangbei.aisearch.infrastructure.search.WebSearchApi;
import com.dangbei.aisearch.infrastructure.tts.TTSClient;
import com.dangbei.aisearch.infrastructure.tts.TTSSynthesizer;
import com.dangbei.aisearch.infrastructure.tts.protocol.OutputFormatEnum;
import com.dangbei.aisearch.infrastructure.tts.protocol.Protocol;
import com.dangbei.aisearch.infrastructure.tts.protocol.SampleRateEnum;
import com.dangbei.framework.insight.redis.util.RedisUtil;
import com.google.common.collect.Maps;
import com.theokanning.openai.completion.chat.AssistantMessage;
import com.theokanning.openai.completion.chat.ChatCompletionRequest;
import com.theokanning.openai.completion.chat.ChatCompletionResult;
import com.theokanning.openai.completion.chat.ChatMessage;
import com.theokanning.openai.completion.chat.ChatMessageRole;
import com.theokanning.openai.completion.chat.UserMessage;
import io.reactivex.Flowable;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 根据模型对话抽象类
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-21
 */
@Slf4j
public abstract class AbstractModelChatExt implements ModelChatSceneExtPt {

    private static final Long MAX_TOKENS = 30000L;
    @Resource
    protected PromptUtil promptUtil;
    @Resource
    private TTSClient ttsClient;
    @Resource(name = "chatAsyncExecutor")
    protected Executor asyncExecutor;
    @Resource(name = "ttftAsyncExecutor")
    protected ScheduledThreadPoolExecutor ttftAsyncExecutor;
    @Resource
    private KnowledgeDocProperties knowledgeDocProperties;
    @Resource
    protected WebSearchApi webSearchApi;
    @Resource
    private MonitorProperties monitorProperties;
    @Resource
    private KnowledgeSearchApi knowledgeSearchApi;
    @Resource
    private UserKnowledgeGateway userKnowledgeGateway;
    @Resource
    protected ExternalCommonGateway externalCommonGateway;
    @Resource
    private UserKnowledgeDocGateway userKnowledgeDocGateway;
    @Resource
    protected SplitSearchKeywordCmdExe splitSearchKeywordCmdExe;
    @Resource
    private AppConfigProperties appConfigProperties;
    @Resource
    private MessageTtsCmdExe messageTtsCmdExe;

    /**
     * 流式对话抽象方法，子类实现
     * @param ctx 聊天上下文
     */
    public abstract Flowable<ChatCompletionResult> chatCompletion(ChatContext ctx) throws Exception;

    @Override
    public void streamChat(ChatContext context) {
        // // 异步流式调用
        // CompletableFuture.runAsync(() -> {
        //     try {
        //         context.setQuestionMsgId(genDistributedId());
        //         context.setAnswerMsgId(genDistributedId());
        //
        //         // ①一些前置操作，若有
        //         preHandle(context);
        //
        //         // ②调用具体大模型流式对话
        //         Flowable<ChatCompletionResult> flowable = chatCompletion(context);
        //
        //         // ③定义延迟任务
        //         AtomicReference<ScheduledFuture<?>> firstTokenCostTaskRef = new AtomicReference<>();
        //
        //         long startAt = System.currentTimeMillis();
        //         log.info("模型调用开始开始，时间戳：{}", startAt);
        //
        //         flowable.doOnError(Throwable::printStackTrace)
        //             .doOnSubscribe(subscription -> flowableDoOnSubscribe(context, firstTokenCostTaskRef))
        //             .doFinally(() -> flowableDoFinally(firstTokenCostTaskRef))
        //             .blockingForEach(resp -> {
        //
        //                 RagResponse ragInfo = resp.getRagSearchInfo();
        //                 AssistantMessage message = CollUtil.isNotEmpty(resp.getChoices()) ? resp.getChoices().get(0).getMessage() : null;
        //
        //                 // 记录首token耗时
        //                 if (isFirstToken(message, context)) {
        //                     context.recordFirstTokenCost(startAt);
        //                 }
        //
        //                 // 联网+知识库结果
        //                 if (isOutputRag(ragInfo)) {
        //                     context.sendSearchCardSSE(ragInfo);
        //                 }
        //
        //                 // 思考过程
        //                 if (isOutputThinking(message)) {
        //                     assert message != null;
        //                     context.appendThink(message.getReasoningContent());
        //                     context.sendDeltaThinkSSE(message.getReasoningContent());
        //                 }
        //
        //                 // 模型回答
        //                 if (isOutputAnswer(message)) {
        //                     assert message != null;
        //                     context.appendAnswer(message.getContent());
        //                     context.sendDeltaAnswerSSE(message.getContent());
        //                     // 语音合成
        //                     sendTTSText(context.getTtsSynthesizer(), message.getContent());
        //                 }
        //
        //                 // Token消耗记录
        //                 context.collectUsage(resp, startAt);
        //
        //                 // 记录请求链路ID
        //                 context.setRequestIdExt(resp.getId());
        //
        //             });
        //
        //         // 推荐问题
        //         if (context.isEnableSuggest()) {
        //             context.sendFollowUpLoadingSSE();
        //             List<String> suggests = context.generateFollowUp();
        //             context.sendFollowUpSSE(suggests);
        //             context.addFollowUp(suggests);
        //         }
        //
        //         // 发送结束标志
        //         context.sendChatCompletedSSE();
        //         context.getEmitter().complete();
        //     } catch (Exception e) {
        //         handleException(e, context);
        //     } finally {
        //         // 保存聊天记录
        //         SafeExecuteUtil.execute(context::asyncStoreChatMsg);
        //         // 关闭TTS session
        //         stopAndDestroy(context.getTtsSynthesizer());
        //     }
        // }, asyncExecutor);
    }

    /**
     * 初始化TTS合成器
     * @param ttsClient tts客户端
     * @param listener  tts合成监听器
     * @param voice     音色
     * @return TTSSynthesizer
     */
    public static TTSSynthesizer initTTSSynthesizer(TTSClient ttsClient,
                                                    LlmVoiceSynthesizerListener listener,
                                                    String voice) {
        try {
            // 初始化合成器
            TTSSynthesizer synthesizer = new TTSSynthesizer(ttsClient, listener);
            synthesizer.setUserId(UserDeviceUtil.getUserIdDefaultDeviceId());
            synthesizer.setSessionId(listener.getSessionId());
            synthesizer.setFormat(OutputFormatEnum.PCM);
            synthesizer.setVoice(voice);
            synthesizer.setSampleRate(SampleRateEnum.SAMPLE_RATE_24K);

            // redis中存储当前wsId关联的最新session
            RedisUtil.set(String.format(CacheKey.WS_SESSION_ID, listener.getWsId()), listener.getSessionId(), 60 * 10);

            return synthesizer;
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return null;
        }
    }

    /**
     * 发送TTS文本给火山
     * @param synthesizer TTS合成器
     * @param content     内容
     */
    public static void sendTTSText(TTSSynthesizer synthesizer, String content) {
        if (StrUtil.isBlank(content) || Objects.isNull(synthesizer)) {
            return;
        }

        try {
            // 如果是第一次发送文本，先启动session
            if (Protocol.State.STATE_CONNECTED.equals(synthesizer.getState())) {
                synthesizer.startSession();
            }

            if (Protocol.State.STATE_REQUEST_CONFIRMED.equals(synthesizer.getState())) {
                synthesizer.send(content);
            }
        } catch (Exception ex) {
            log.error("sendTTS.error={}", ex.getMessage(), ex);
            // 若发送文本出现异常的话，切断连接，剩余的音频都不再接收
            try {
                synthesizer.close();
            } catch (Exception ignore) {
            }
        }
    }

    /**
     * 关闭TTS会话
     * @param synthesizer TTS合成器
     */
    public static void stopAndDestroy(TTSSynthesizer synthesizer) {
        if (Objects.isNull(synthesizer)) {
            return;
        }

        synthesizer.stopAndDestroy();
    }

    /**
     * 流结束时执行的操作
     * @param taskRef 定时任务引用
     */
    private void flowableDoFinally(AtomicReference<ScheduledFuture<?>> taskRef) {
        try {
            // ①在流结束时取消定时任务
            log.debug("TTFT超时检测任务取消关闭");
            ScheduledFuture<?> task = taskRef.get();
            if (task != null && !task.isCancelled()) {
                task.cancel(true);
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    /**
     * 订阅时执行的操作
     * @param context 聊天上下文
     * @param taskRef 定时任务引用
     */
    private void flowableDoOnSubscribe(ChatContext context, AtomicReference<ScheduledFuture<?>> taskRef) {
        try {
            //  ①开关打开 # 在订阅时启动首token检查任务
            if (monitorProperties.isTtftWarnEnabled()) {
                ScheduledFuture<?> task = ttftAsyncExecutor.schedule(() -> {
                    log.info("首token检查时间：" + System.currentTimeMillis());

                    TokenUsageCo usageInfo = context.getUsageInfo();
                    if (Objects.isNull(usageInfo) || Objects.isNull(usageInfo.getFirstTokenCostMs())) {
                        log.error("""
                                首Token返回超时，当前阈值{}ms，请关注！
                                供应商：{}
                                模型：{}
                                """,
                            monitorProperties.getTtftMaxMs(),
                            Objects.nonNull(usageInfo) ? usageInfo.getProvider() : null,
                            Objects.nonNull(usageInfo) ? usageInfo.getModel() : null
                        );
                    }
                }, monitorProperties.getTtftMaxMs(), TimeUnit.MILLISECONDS);
                // 存储定时任务
                taskRef.set(task);
            }

            // ④初始化语音TTS websocket连接
            ChatCmd.ChatOption option = context.getChatCmd().getChatOption();
            if (Objects.nonNull(option) && StrUtil.isNotBlank(option.getWsId()) && option.isAutoTts()) {
                log.info("初始化TTS合成器，wsId：{}", option.getWsId());
                long start = System.currentTimeMillis();
                // 音色
                String voiceType = messageTtsCmdExe.findVoiceType(StrUtil.isNotBlank(context.getAgentId()) ? context.getConversationId() : null);
                if (StrUtil.isBlank(voiceType)) {
                    return;
                }
                // appKey
                String wsAppKey = appConfigProperties.extractWsAppKey();
                // sessionId
                String sessionId = String.format("%s_%s", context.getAnswerMsgId(), NanoIdUtil.simpleNanoId(6));

                LlmVoiceSynthesizerListener listener = new LlmVoiceSynthesizerListener(MDC.get(RequestConstant.REQUEST_ID));
                listener.setWsId(option.getWsId());
                listener.setSessionId(sessionId);
                listener.setMsgId(context.getAnswerMsgId());
                listener.setWsAppKey(wsAppKey);
                listener.addExt("source", TtsSourceEnum.CONVERSATION.getValue());
                listener.addExt("sessionId", sessionId);

                TTSSynthesizer ttsSynthesizer = initTTSSynthesizer(ttsClient, listener, voiceType);
                if (Objects.isNull(ttsSynthesizer)) {
                    log.warn("初始化TTS合成器失败，耗时：{}ms", System.currentTimeMillis() - start);
                    return;
                }

                // 设置到上下文
                context.setTtsSynthesizer(ttsSynthesizer);

                log.info("""
                    初始化TTS合成器成功，耗时：{}ms
                    sessionId：{}
                    """, System.currentTimeMillis() - start, sessionId);
            }

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    protected void preHandle(ChatContext ctx) {
        // empty
    }

    protected String genDistributedId() {
        return externalCommonGateway.getDistributedId();
    }

    private boolean isOutputRag(RagResponse ragResponse) {
        return Objects.nonNull(ragResponse) && ragResponse.hasResult();
    }

    private boolean isFirstToken(AssistantMessage message, ChatContext context) {
        if (Objects.isNull(context.getUsageInfo()) || Objects.isNull(context.getUsageInfo().getFirstTokenCostMs())) {
            return isOutputAnswer(message) || isOutputThinking(message);
        }
        return false;
    }

    private boolean isOutputThinking(AssistantMessage message) {
        return Objects.nonNull(message) && StrUtil.isNotEmpty(message.getReasoningContent());
    }

    private boolean isOutputAnswer(AssistantMessage message) {
        return Objects.nonNull(message) && StrUtil.isNotEmpty(message.getContent());
    }

    // private boolean isGreenIntercept(Exception ex) {
    //     return ex instanceof ApiException && StringUtils.contains(ex.getMessage(), "inappropriate content");
    // }
    //
    // private boolean isTencentConcurrentRequest(Exception ex) {
    //     return ex instanceof TencentCloudSDKException && StringUtils.contains(ex.getMessage(), "20034");
    // }
    //
    // private boolean isClientInterrupt(Exception ex) {
    //     return ex instanceof RuntimeException && ex.getCause() instanceof ClientAbortException;
    // }
    //
    // private boolean isBrokenPipe(Exception ex) {
    //     return ex instanceof ClientAbortException && ex.getCause() instanceof IOException
    //         && StrUtil.contains(ex.getMessage(), "Broken pipe");
    // }
    //
    // private static boolean isAsyncTimeout(Exception ex) {
    //     return ex instanceof IllegalStateException && StringUtils.contains(ex.getMessage(), "already completed");
    // }

    // private void handleException(Exception ex, ChatContext context) {
    //     if (isGreenIntercept(ex)) { // 绿网
    //         context.sendGreenInterceptSSE();
    //     } else if (isTencentConcurrentRequest(ex)) { // 腾讯并发请求
    //         context.sendConcurrentRequestSSE();
    //     } else if (isAsyncTimeout(ex)) { // 请求超时，超过Emitter的超时时间
    //         log.warn("请求超时", ex);
    //     } else if (isClientInterrupt(ex)) {
    //         log.debug("流式对话被打断：{}", ex.getMessage(), ex);
    //     } else if (isBrokenPipe(ex)) {
    //         log.debug("流式对话异常Broken pipe：{}", ex.getMessage(), ex);
    //     } else {
    //         log.error(ex.getMessage(), ex);
    //     }
    //     context.getEmitter().completeWithError(ex);
    // }

    protected RagResponse ragSearch(ChatCompletionRequest request, ChatContext ctx) {
        RagResponse ragResp = new RagResponse();
        // 1.先知识库搜索
        KnowledgeResponse knowledgeResponse = handleKnowledgeSearch(ctx);
        ragResp.setKnowledgeResponse(knowledgeResponse);

        // 2.再联网搜索
        SearchResponse searchResponse = handleCustomSearch(request, ctx, knowledgeResponse);
        ragResp.setSearchResponse(searchResponse);

        // 阿里enable_search=true，联网结果会在流式中返回，所以不能提前发送卡片
        if (!request.isEnableSearch() && ragResp.hasResult()) {

            // 改写提示词
            ChatMessage message = request.getMessages().get(request.getMessages().size() - 1);
            if (ChatMessageRole.USER.value().equals(message.getRole())) {
                UserMessage userMsg = (UserMessage) message;
                PromptParam param = PromptParam.builder()
                    // TODO 国际化
                    .promptTmpl(I18nUtil.isChinese() ? promptUtil.getRagUserPrompt() : promptUtil.getEnOnlineUserPrompt())
                    .ip(ctx.getIp())
                    .question(ctx.getChatCmd().getQuestion())
                    .ragResponse(ragResp)
                    .build();
                String newQuestion = promptUtil.format(param);
                userMsg.setContent(newQuestion);
            }

            // 2025-03-18 提前发送RAG搜索结果，缓解用户焦虑
            ctx.sendSearchCardSSE(ragResp);
        }
        return ragResp;
    }

    /**
     * 处理自定义搜索提示词
     * @param request 请求参数
     * @param ctx     聊天上下文
     */
    protected SearchResponse handleCustomSearch(ChatCompletionRequest request, ChatContext ctx, KnowledgeResponse knowledgeResponse) {
        // 用户要求搜索，但是api不支持搜索
        SearchResponse customSearchResp = null;
        if (request.isEnableCustomSearch()) {
            SplitSearchKeywordResult result = splitSearchKeywordCmdExe.execute(ctx, splicingAdditionInfo(knowledgeResponse, null));

            if (CollUtil.isNotEmpty(result.getSearchKeywords())) {
                customSearchResp = webSearchApi.webSearch(SearchRequest.builder()
                    .query(result.getSearchKeywords())
                    .build());
            }
        }
        return customSearchResp;
    }


    /**
     * 处理知识库搜索
     * @param ctx 聊天上下文
     */
    protected KnowledgeResponse handleKnowledgeSearch(ChatContext ctx) {
        if (!ctx.needSearchKnowledge()) {
            // 不需要检索场景，直接return
            return null;
        }

        String userId = UserContextUtil.getUserId();
        UserKnowledgeEntity userKnowledge = userKnowledgeGateway.getUserKnowledge(userId);
        if (Objects.isNull(userKnowledge)) {
            return null;
        }

        // 提取用户docList
        ChatCmd chatCmd = ctx.getChatCmd();
        List<BaseDocEntity> docInfos = extractDocInfos(chatCmd, userKnowledge.getKnowledgeId());
        if (CollUtil.isEmpty(docInfos)) {
            return null;
        }

        Map<String, BaseDocEntity> docExtMap = Maps.newHashMap();
        for (BaseDocEntity each : docInfos) {
            docExtMap.put(each.getDocIdExt(), each);
        }

        // 调用火山API检索
        LinkedList<ChatMessage> chatMessages = filterMemoryTurns(ctx.getHistory(), 2);
        chatMessages.add(new UserMessage(chatCmd.getQuestion()));

        KnowledgeRequest searchRequest = new KnowledgeRequest();
        searchRequest.setQuery(chatCmd.getQuestion());
        searchRequest.setKnowledgeIdExt(userKnowledge.getKnowledgeIdExt());
        searchRequest.setDocInfos(docInfos);
        searchRequest.setMessages(cnv2OpenAiMessages(chatMessages));
        KnowledgeResponse searchResponse = knowledgeSearchApi.search(searchRequest);
        if (Objects.isNull(searchResponse) || CollUtil.isEmpty(searchResponse.getChunkList())) {
            return null;
        }

        // 遍历chunkList，将chunk聚合到对应的doc中
        LinkedHashMap<String, KnowledgeResponse.DocResult> docResultMap = Maps.newLinkedHashMap();
        for (KnowledgeResponse.ChunkResult chunk : searchResponse.getChunkList()) {
            String docIdExt = chunk.getDocIdExt();
            // 如果docResultMap中不存在该docIdExt，则创建一个新的DocResult
            if (!docResultMap.containsKey(docIdExt)) {
                BaseDocEntity docEntity = docExtMap.get(docIdExt);
                KnowledgeResponse.DocResult docResult = new KnowledgeResponse.DocResult();
                docResult.setDocIdExt(docIdExt);
                docResult.setChunkList(new ArrayList<>());
                if (Objects.nonNull(docEntity)) {
                    docResult.setDocId(docEntity.getDocId());
                    docResult.setDocType(docEntity.getDocType());
                    docResult.setDocName(docEntity.getDocName());
                    docResult.setDocSize(docEntity.getDocSize());
                    docResult.setWordNum(docEntity.getWordNum());
                }
                docResultMap.put(docIdExt, docResult);
            }

            // 获取对应的DocResult，并添加当前chunk
            KnowledgeResponse.DocResult docResult = docResultMap.get(docIdExt);
            docResult.getChunkList().add(chunk);
        }

        // 将map转换为list
        List<KnowledgeResponse.DocResult> aggDocList = new ArrayList<>(docResultMap.values());

        // 更新response的docList
        searchResponse.setDocList(aggDocList);
        return searchResponse;
    }

    private LinkedList<com.dangbei.framework.insight.volcopenai.model.completion.chat.ChatMessage> cnv2OpenAiMessages(LinkedList<ChatMessage> chatMessages) {
        if (CollUtil.isEmpty(chatMessages)) {
            return new LinkedList<>();
        }

        LinkedList<com.dangbei.framework.insight.volcopenai.model.completion.chat.ChatMessage> resList = new LinkedList<>();
        for (ChatMessage source : chatMessages) {
            com.dangbei.framework.insight.volcopenai.model.completion.chat.ChatMessage chatMessage = new com.dangbei.framework.insight.volcopenai.model.completion.chat.ChatMessage();
            chatMessage.setRole(valueOfRoleEnum(source.getRole()));
            chatMessage.setContent(source.getTextContent());
            resList.add(chatMessage);
        }
        return resList;
    }

    private com.dangbei.framework.insight.volcopenai.model.completion.chat.ChatMessageRole valueOfRoleEnum(String role) {
        com.dangbei.framework.insight.volcopenai.model.completion.chat.ChatMessageRole[] values = com.dangbei.framework.insight.volcopenai.model.completion.chat.ChatMessageRole.values();
        for (com.dangbei.framework.insight.volcopenai.model.completion.chat.ChatMessageRole each : values) {
            if (each.value().equals(role)) {
                return each;
            }
        }
        return null;
    }

    private List<BaseDocEntity> extractDocInfos(ChatCmd chatCmd, String knowledgeId) {
        if (Objects.nonNull(chatCmd.getChatOption())
            && chatCmd.getChatOption().isSearchKnowledge()
            && CollUtil.isEmpty(chatCmd.getFiles())
            && CollUtil.isEmpty(chatCmd.getReference())) {
            // 找出用户所有docInfo
            String userId = UserContextUtil.getUserId();
            return userKnowledgeDocGateway.getUserAllDocIds(userId, knowledgeId, knowledgeDocProperties.getSearchMaxDocSize()).stream()
                .filter(i -> DocProcessStatusEnum.COMPLETED.eq(i.getProcessStatus()) && StrUtil.isNotBlank(i.getDocIdExt()))
                .collect(Collectors.toList());
        }

        // 筛选出用户指定的文档
        Set<String> docIds = new HashSet<>();
        if (CollUtil.isNotEmpty(chatCmd.getFiles())) {
            docIds.addAll(chatCmd.getFiles().stream()
                .map(ChatCmd.FileItem::getFileId)
                .filter(fileId -> StrUtil.startWith(fileId, CommonConst.Knowledge.DOC_PREFIX))
                .collect(Collectors.toSet())
            );
        }
        // 筛选出引用的文档
        if (CollUtil.isNotEmpty(chatCmd.getReference())) {
            docIds.addAll(chatCmd.getReference().stream()
                .map(ChatCmd.ReferenceItem::getFileId)
                .filter(fileId -> StrUtil.startWith(fileId, CommonConst.Knowledge.DOC_PREFIX))
                .collect(Collectors.toSet())
            );
        }

        return userKnowledgeDocGateway.listByDocIds(docIds).stream()
            .filter(i -> DocProcessStatusEnum.COMPLETED.eq(i.getProcessStatus()) && StrUtil.isNotBlank(i.getDocIdExt()))
            .collect(Collectors.toList());
    }


    private String splicingAdditionInfo(KnowledgeResponse knowledgeResponse, AttachmentResponse attachmentResponse) {
        StringBuilder additionInfoBuilder = new StringBuilder();

        if (Objects.nonNull(knowledgeResponse) && CollUtil.isNotEmpty(knowledgeResponse.getChunkList())) {
            List<KnowledgeResponse.ChunkResult> sortList = knowledgeResponse.getChunkList().stream().limit(2).toList();
            additionInfoBuilder.append("知识库参考资料:");
            sortList.forEach(knowledge -> additionInfoBuilder.append("<doc_start>").append("\n").append(knowledge.getContent()).append("\n").append("<doc_end>"));
        }

        //  用户当前提问，结合附件
        if (Objects.nonNull(attachmentResponse)) {
            if (CollUtil.isNotEmpty(attachmentResponse.getFileAttachmentResults())) {
                additionInfoBuilder.append("文件附件总结:");
                attachmentResponse.getFileAttachmentResults().forEach(fileAttachment -> additionInfoBuilder.append("<file_start>").append("\n").append(fileAttachment.getSummary()).append("\n").append("<file_end>"));
            }
            if (Objects.nonNull(attachmentResponse.getImageAttachmentResult())) {
                additionInfoBuilder.append("图片附件总结:");
                additionInfoBuilder.append("\n").append(attachmentResponse.getImageAttachmentResult().getComprehension());
            }
        }
        return additionInfoBuilder.toString();
    }

    public static LinkedList<ChatMessage> filterMemoryTurns(List<ChatMessageEntity> history, Integer turns) {
        if (CollUtil.isEmpty(history)) {
            return new LinkedList<>();
        }

        LinkedList<ChatMessage> recentMessages = new LinkedList<>();
        // 倒序遍历消息列表，找到最近的 12 条 user/assistant 消息（6轮）
        for (ChatMessageEntity entity : history) {
            if (StringUtils.isBlank(entity.getContent())) {
                continue;
            }
            if (RoleEnum.USER.eq(entity.getRole())) {
                recentMessages.addFirst(new UserMessage(entity.getContent()));
            }
            if (RoleEnum.ASSISTANT.eq(entity.getRole())) {
                recentMessages.addFirst(new AssistantMessage(entity.getContent()));
            }
            if (recentMessages.size() >= turns * 2) {
                break; // 找到N轮对话，退出
            }
        }
        return recentMessages;
    }

    private long countToken(String text) {
        try {
            Tokenizer tokenizer = TokenizerFactory.qwen();
            return tokenizer.encode(text, "all").size();
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return StrUtil.length(text);
        }
    }

}
