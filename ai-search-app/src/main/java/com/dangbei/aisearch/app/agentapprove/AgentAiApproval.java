package com.dangbei.aisearch.app.agentapprove;

import cn.hutool.json.JSONUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.dashscope.aigc.generation.GenerationParam;
import com.alibaba.dashscope.common.Message;
import com.alibaba.dashscope.common.ResponseFormat;
import com.alibaba.dashscope.common.Role;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.dangbei.aisearch.app.agentapprove.dto.AgentApproveCo;
import com.dangbei.aisearch.app.agentapprove.dto.AgentVersionApproveCo;
import com.dangbei.aisearch.app.agentapprove.dto.AiContentApproveTemplate;
import com.dangbei.aisearch.app.agentapprove.dto.ApproveResult;
import com.dangbei.aisearch.app.executor.AgentEnrichCmdExe;
import com.dangbei.aisearch.app.executor.AgentSystemPromptPolishCmdExe;
import com.dangbei.aisearch.client.dto.AgentFollowUp;
import com.dangbei.aisearch.client.dto.clientobject.AgentEnrichCo;
import com.dangbei.aisearch.client.dto.cmd.AgentEnrichGenCmd;
import com.dangbei.aisearch.client.dto.cmd.AgentSystemPromptGenCmd;
import com.dangbei.aisearch.client.enums.AgentApproveStatusEnum;
import com.dangbei.aisearch.client.enums.AgentGenderEnum;
import com.dangbei.aisearch.client.enums.AgentOnlineStatusEnum;
import com.dangbei.aisearch.common.constant.MsgTag;
import com.dangbei.aisearch.common.util.SafeUtil;
import com.dangbei.aisearch.domain.entity.AgentEntity;
import com.dangbei.aisearch.domain.entity.AgentVersionEntity;
import com.dangbei.aisearch.domain.gateway.AgentGateway;
import com.dangbei.aisearch.domain.gateway.AgentVersionGateway;
import com.dangbei.aisearch.infrastructure.config.properties.DashScopeProperties;
import com.dangbei.aisearch.infrastructure.config.properties.MqProperties;
import com.dangbei.aisearch.infrastructure.factory.AutoCloseGeneration;
import com.dangbei.aisearch.infrastructure.factory.PoolDashScopeObjectFactory;
import com.dangbei.aisearch.infrastructure.i18n.I18nUtil;
import com.dangbei.aisearch.infrastructure.prompt.PromptParam;
import com.dangbei.aisearch.infrastructure.prompt.PromptUtil;
import com.dangbei.framework.insight.ons.util.ProducerUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 智能体 AI 审核
 * <AUTHOR>
 * @date 2025-03-28 15:24
 **/
@Component
@Slf4j
public class AgentAiApproval {

    @Resource
    private AgentVersionGateway agentVersionGateway;
    @Resource
    private AgentGateway agentGateway;
    @Resource
    private DashScopeProperties dashScopeProperties;
    @Resource
    private PromptUtil promptUtil;
    @Resource
    private AgentSystemPromptPolishCmdExe agentSystemPromptPolishCmdExe;
    @Resource
    private AgentEnrichCmdExe agentEnrichCmdExe;
    @Resource
    private ProducerUtil producerUtil;
    @Resource
    private MqProperties mqProperties;

    public boolean consume(AgentVersionApproveCo agentVersionApproveCo) {
        log.debug("智能体版本审核开始:{}", JSON.toJSONString(agentVersionApproveCo));
        if (Objects.isNull(agentVersionApproveCo) || Objects.isNull(agentVersionApproveCo.getAgentInfo())) {
            return true;
        }
        var targetVersion = agentVersionGateway.loadById(agentVersionApproveCo.getId());
        if (Objects.isNull(targetVersion)) {
            log.warn("智能体[{}]版本审核失败，智能体版本不存在:{}", agentVersionApproveCo.getAgentId(), agentVersionApproveCo.getId());
            return true;
        }
        // 存在最新版本，则跳过审核
        if (existLatestVersion(targetVersion.getAgentId(), targetVersion.getId())) {
            log.warn("智能体[{}]版本[{}] AI 审核跳过，存在最新版本", targetVersion.getAgentId(), targetVersion.getId());
            ((AgentAiApproval) AopContext.currentProxy()).abortVersion(targetVersion);
            return true;
        }
        var agentInfo = agentVersionApproveCo.getAgentInfo();
        var approveResult = new ApproveResult();
        // 内容安全AI检查
        if (!contentAiCheck(agentInfo, approveResult)) {
            ((AgentAiApproval) AopContext.currentProxy()).rejectVersion(targetVersion, approveResult);
            return true;
        }
        // 通过
        acceptVersion(targetVersion);
        return true;
    }

    private boolean existLatestVersion(String agentId, Long currentVersionId) {
        AgentVersionEntity latest = agentVersionGateway.getLatestVersionByAgentId(agentId);
        return Objects.nonNull(latest) && latest.getId() > currentVersionId;
    }

    private boolean contentAiCheck(AgentApproveCo agentInfo, ApproveResult approveResult) {
        var aiApproveConfig = dashScopeProperties.getAiApproveConfig();
        String promptTmpl = I18nUtil.isChinese() ? aiApproveConfig.getSystem() : aiApproveConfig.getEnSystem();

        AiContentApproveTemplate template = convert2ApproveTemplate(agentInfo);

        Map<String, Object> extParam = Maps.newHashMap();
        extParam.put("input", JSONObject.toJSONString(template));

        List<Message> messages = new ArrayList<>();
        String prompt = promptUtil.format(PromptParam.builder()
            .promptTmpl(promptTmpl)
            .extParam(extParam)
            .build());

        messages.add(Message.builder().role(Role.SYSTEM.getValue()).content(prompt).build());

        GenerationParam param = GenerationParam.builder()
            .apiKey(dashScopeProperties.getApiKey())
            .model(aiApproveConfig.getModel())
            .messages(messages)
            .resultFormat(GenerationParam.ResultFormat.MESSAGE)
            .responseFormat(ResponseFormat.from(ResponseFormat.JSON_OBJECT))
            .build();
        String approveJson = "";
        try (AutoCloseGeneration gen = PoolDashScopeObjectFactory.getGeneration()) {
            approveJson = gen.call(param).getOutput().getChoices().get(0).getMessage().getContent();
        } catch (Exception ex) {
            log.warn("ai 审核异常", ex);
            approveResult.setPass(false);
            approveResult.setRejectReason("ai 审核异常" + ex.getMessage());
            return false;
        }
        boolean isJson = JSONUtil.isTypeJSONObject(approveJson);
        if (!isJson) {
            log.warn("ai 审核异常,返回格式异常：{}", approveJson);
            approveResult.setPass(false);
            approveResult.setRejectReason("ai 审核异常,返回格式异常: " + approveJson);
            return false;
        }
        var modelApproveResult = JSON.parseObject(approveJson, ApproveResult.class);
        if (modelApproveResult.isPass()) {
            return true;
        } else {
            approveResult.setPass(false);
            approveResult.setRejectReason(modelApproveResult.getRejectReason());
            return false;
        }
    }
    /**
     * 拒绝
     * @param targetVersion 目标版本
     * @param approveResult 审核结果
     */
    @Transactional(rollbackFor = Exception.class)
    public void rejectVersion(AgentVersionEntity targetVersion, ApproveResult approveResult) {
        var agentVersionUpdateEntity = new AgentVersionEntity();
        agentVersionUpdateEntity.setId(targetVersion.getId());
        agentVersionUpdateEntity.setApproveStatus(AgentApproveStatusEnum.MACHINE_REJECTED.getValue());
        agentVersionUpdateEntity.setRejectReason(approveResult.getRejectReason());
        agentVersionUpdateEntity.update();
        refreshAgent(targetVersion);
    }

    /**
     * 拒绝
     * @param targetVersion 目标版本
     */
    @Transactional(rollbackFor = Exception.class)
    public void abortVersion(AgentVersionEntity targetVersion) {
        var agentVersionUpdateEntity = new AgentVersionEntity();
        agentVersionUpdateEntity.setId(targetVersion.getId());
        agentVersionUpdateEntity.setApproveStatus(AgentApproveStatusEnum.ABORT.getValue());
        agentVersionUpdateEntity.update();
        refreshAgent(targetVersion);
    }

    /**
     * 通过
     * @param targetVersion 目标版本
     */
    public void acceptVersion(AgentVersionEntity targetVersion) {
        AgentEntity agentEntity = agentGateway.getByAgentId(targetVersion.getAgentId());
        log.debug("当前智能体详情:{}", JSON.toJSONString(agentEntity));
        var agentVersionInfo = targetVersion.getAgentInfo();
        log.debug("智能体版本详情:{}", JSON.toJSONString(agentVersionInfo));

        Integer onlineStatus = agentEntity.getOnlineStatus();
        if (agentEntity.isAgentDeleted()) {
            log.info("agent {} is deleted, skip acceptVersion", agentEntity.getAgentId());
            return;
        }
        // 如果是冻结，则设置为正常
        if (AgentOnlineStatusEnum.FROZEN.getStatus().equals(onlineStatus)) {
            agentVersionInfo.setOnlineStatus(AgentOnlineStatusEnum.NORMAL.getStatus());
        }
        // 异步润色一下 systemPrompt
        asyncPolishSystemPrompt(agentVersionInfo);
        // 如果简介、开场白、推荐问题为空，补充一下
        fillExtraInfo(agentVersionInfo);
        // 开启事务，执行更新
        ((AgentAiApproval) AopContext.currentProxy()).doUpdate(targetVersion, agentVersionInfo, onlineStatus);
    }

    private void asyncPolishSystemPrompt(AgentEntity agentVersionInfo) {
        SafeUtil.runSilently(() -> producerUtil.send(mqProperties.getTopic(), MsgTag.AGENT_POLISH, agentVersionInfo, agentVersionInfo.getAgentId()));
    }

    @Transactional(rollbackFor = Exception.class)
    public void doUpdate(AgentVersionEntity targetVersion, AgentEntity agentVersionInfo, Integer expectedOnlineStatus) {
        // 更新版本状态
        var agentVersionUpdateEntity = new AgentVersionEntity();
        agentVersionUpdateEntity.setId(targetVersion.getId());
        agentVersionUpdateEntity.setApproveStatus(AgentApproveStatusEnum.APPROVED.getValue());
        agentVersionUpdateEntity.update();

        // 将 agent 刷成最新版本，仅当 online_status 与预期值一致时才更新
        boolean updateSuccess = agentGateway.updateWithOnlineStatusCheck(agentVersionInfo, expectedOnlineStatus);
        if (!updateSuccess) {
            log.warn("智能体[{}]更新失败，online_status 已发生变化", agentVersionInfo.getAgentId());
        }
    }

    public void polishSystemPrompt(AgentEntity agentVersionInfo) {
        try {
            var targetAgent = agentGateway.getByAgentId(agentVersionInfo.getAgentId());
            if (Objects.isNull(targetAgent) || targetAgent.isAgentDeleted()) {
                return;
            }
            var agentSystemPromptGenCmd = buildPolishSystemPromptParam(agentVersionInfo);
            SingleResponse<String> polishResponse = agentSystemPromptPolishCmdExe.execute(agentSystemPromptGenCmd);
            if (Objects.nonNull(polishResponse) && polishResponse.isSuccess()) {
                // 更新系统提示词
                AgentEntity agentEntity = new AgentEntity();
                agentEntity.setId(agentVersionInfo.getId());
                agentEntity.setSystemPrompt(polishResponse.getData());
                agentGateway.update(agentEntity);
                log.info("Agent [{}]润色 systemPrompt 成功", agentVersionInfo.getAgentId());
            }
        } catch (Exception e) {
            log.warn("Agent [{}]润色 systemPrompt 失败:{}", agentVersionInfo.getAgentId(), e.getMessage(), e);
        }
    }

    private void refreshAgent(AgentVersionEntity targetVersion) {
        var agentInfo = targetVersion.getAgentInfo();
        var targetAgent = agentGateway.getByAgentId(agentInfo.getAgentId());
        // 如果当前还是冻结的版本，则更新
        if (AgentOnlineStatusEnum.isFrozen(targetAgent.getOnlineStatus())) {
            agentGateway.update(agentInfo);
        }
    }

    private void fillExtraInfo(AgentEntity agentVersionInfo) {
        // 如果简介和开场白都有，不补充
        if (StringUtils.isNotBlank(agentVersionInfo.getIntro()) &&
            CollectionUtils.isNotEmpty(agentVersionInfo.getFollowUp())) {
            return;
        }
        AgentEnrichGenCmd cmd = new AgentEnrichGenCmd();
        cmd.setName(agentVersionInfo.getName());
        cmd.setUserPrompt(agentVersionInfo.getUserPrompt());
        cmd.setIntro(agentVersionInfo.getIntro());
        cmd.setGreeting(agentVersionInfo.getGreeting());
        cmd.setFollowUp(agentVersionInfo.getFollowUp());
        AgentEnrichCo co = new AgentEnrichCo();
        SafeUtil.runSilently(
            () -> agentEnrichCmdExe.doEnrich(cmd, co),
            e -> log.warn("Agent [{}] 自动补充高级设定 失败:{}", agentVersionInfo.getAgentId(), e.getMessage(), e)
        );
        if (StringUtils.isBlank(agentVersionInfo.getIntro()) && StringUtils.isNotBlank(co.getIntro())) {
            agentVersionInfo.setIntro(co.getIntro());
            // 兼容历史前端使用 description 的逻辑
            agentVersionInfo.setDescription(co.getIntro());
        }
        if (CollectionUtils.isEmpty(agentVersionInfo.getFollowUp()) && CollectionUtils.isNotEmpty(co.getFollowUp())) {
            agentVersionInfo.setFollowUp(co.getFollowUp());
        }
        if (StringUtils.isBlank(agentVersionInfo.getGreeting()) && StringUtils.isNotBlank(co.getGreeting())) {
            agentVersionInfo.setGreeting(co.getGreeting());
        }
    }


    private AiContentApproveTemplate convert2ApproveTemplate(AgentApproveCo agentInfo) {
        AiContentApproveTemplate template = new AiContentApproveTemplate();
        template.setName(agentInfo.getName());
        template.setIntro(agentInfo.getIntro());
        template.setChatExample(agentInfo.getChatExample());
        template.setPersonality(agentInfo.getPersonality());
        template.setUserPrompt(agentInfo.getUserPrompt());
        if (CollectionUtils.isNotEmpty(agentInfo.getFollowUp())) {
            List<String> followUpStrList = new ArrayList<>();
            for (AgentFollowUp agentFollowUp : agentInfo.getFollowUp()) {
                followUpStrList.add(agentFollowUp.getValue());
            }
            template.setFollowUp(followUpStrList);
        }
        return template;
    }


    private static @NotNull AgentSystemPromptGenCmd buildPolishSystemPromptParam(AgentEntity agentVersionInfo) {
        AgentSystemPromptGenCmd agentSystemPromptGenCmd = new AgentSystemPromptGenCmd();
        agentSystemPromptGenCmd.setAgentRole(agentVersionInfo.getRole());
        agentSystemPromptGenCmd.setName(agentVersionInfo.getName());
        agentSystemPromptGenCmd.setUserPrompt(agentVersionInfo.getUserPrompt());
        // 带上示例和性格
        agentSystemPromptGenCmd.setGender(AgentGenderEnum.getByValue(agentVersionInfo.getGender()).getDesc());
        agentSystemPromptGenCmd.setChatExample(agentVersionInfo.getChatExample());
        agentSystemPromptGenCmd.setPersonality(agentVersionInfo.getPersonality());
        return agentSystemPromptGenCmd;
    }
}
