package com.dangbei.aisearch.app.service;

import com.alibaba.cola.dto.PageSingleResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.dangbei.aisearch.app.executor.CreateOrUpdateSharedKnowledgeExe;
import com.dangbei.aisearch.app.executor.DeleteSharedKnowledgeExe;
import com.dangbei.aisearch.app.executor.KnowledgeShareCmdExe;
import com.dangbei.aisearch.app.executor.query.SharedKnowledgeDetailQueryExe;
import com.dangbei.aisearch.app.executor.query.SharedKnowledgeListQueryExe;
import com.dangbei.aisearch.client.dto.clientobject.SharedKnowledgeCo;
import com.dangbei.aisearch.client.dto.clientobject.SharedKnowledgeListItemCo;
import com.dangbei.aisearch.client.dto.cmd.DeleteSharedKnowledgeCmd;
import com.dangbei.aisearch.client.dto.cmd.KnowledgeShareCmd;
import com.dangbei.aisearch.client.dto.cmd.SaveSharedKnowledgeCmd;
import com.dangbei.aisearch.client.dto.cmd.query.SharedKnowledgeListQuery;
import com.dangbei.aisearch.client.dto.cmd.ExitSharedKnowledgeCmd;
import com.dangbei.aisearch.app.executor.ExitSharedKnowledgeExe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 共享知识库服务
 * <AUTHOR>
 * @date 2025-05-27
 **/
@Slf4j
@Service
public class SharedKnowledgeService {

    @Resource
    private CreateOrUpdateSharedKnowledgeExe createOrUpdateSharedKnowledgeExe;
    @Resource
    private DeleteSharedKnowledgeExe deleteSharedKnowledgeExe;
    @Resource
    private SharedKnowledgeDetailQueryExe sharedKnowledgeDetailQueryExe;
    @Resource
    private SharedKnowledgeListQueryExe sharedKnowledgeListQueryExe;
    @Resource
    private KnowledgeShareCmdExe knowledgeShareCmdExe;
    @Resource
    private ExitSharedKnowledgeExe exitSharedKnowledgeExe;

    public SingleResponse<String> saveSharedKnowledge(SaveSharedKnowledgeCmd cmd) {
        return createOrUpdateSharedKnowledgeExe.execute(cmd);
    }

    public Response deleteSharedKnowledge(DeleteSharedKnowledgeCmd cmd) {
        return deleteSharedKnowledgeExe.execute(cmd);
    }

    public SingleResponse<SharedKnowledgeCo> getSharedKnowledgeDetail(String knowledgeId) {
        return sharedKnowledgeDetailQueryExe.execute(knowledgeId);
    }

    public PageSingleResponse<SharedKnowledgeListItemCo> getAllKnowledgeList(SharedKnowledgeListQuery query) {
        return sharedKnowledgeListQueryExe.executeAllList(query);
    }

    public PageSingleResponse<SharedKnowledgeListItemCo> getCreatedKnowledgeList(SharedKnowledgeListQuery query) {
        return sharedKnowledgeListQueryExe.executeCreatedList(query);
    }

    public PageSingleResponse<SharedKnowledgeListItemCo> getJoinedKnowledgeList(SharedKnowledgeListQuery query) {
        return sharedKnowledgeListQueryExe.executeJoinedList(query);
    }

    public SingleResponse<String> share(KnowledgeShareCmd cmd) {
        return knowledgeShareCmdExe.execute(cmd);
    }

    /**
     * 退出知识库
     * @param cmd 退出命令
     * @return 操作结果
     */
    public Response exitSharedKnowledge(ExitSharedKnowledgeCmd cmd) {
        return exitSharedKnowledgeExe.execute(cmd);
    }
}
