package com.dangbei.aisearch.infrastructure.gatewayimpl;

import com.dangbei.aisearch.domain.entity.WriteCategoryEntity;
import com.dangbei.aisearch.domain.gateway.WriteCategoryGateway;
import com.dangbei.aisearch.infrastructure.common.base.BaseGatewayImpl;
import com.dangbei.aisearch.infrastructure.convertor.WriteCategoryConvertor;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.WriteCategoryDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.WriteCategoryMapper;
import org.springframework.stereotype.Component;

/**
 * WriteCategory 网关层实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-26
 */
@Component
public class WriteCategoryGatewayImpl extends BaseGatewayImpl<Long, WriteCategoryEntity, WriteCategoryDO, WriteCategoryMapper, WriteCategoryConvertor> implements WriteCategoryGateway {

}
