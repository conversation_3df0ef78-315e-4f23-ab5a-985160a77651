package com.dangbei.aisearch.app.executor;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.dashscope.aigc.generation.GenerationParam;
import com.alibaba.dashscope.common.Message;
import com.alibaba.dashscope.common.ResponseFormat;
import com.dangbei.aisearch.infrastructure.config.properties.DashScopeProperties;
import com.dangbei.aisearch.infrastructure.factory.AutoCloseGeneration;
import com.dangbei.aisearch.infrastructure.factory.PoolDashScopeObjectFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.function.Function;

/**
 * 文本生成执行器
 *
 * <AUTHOR>
 * @date 2025-03-27 11:00
 **/
@Component
@Slf4j
public class TextGenerationCmdExe {

    @Resource
    private DashScopeProperties dashScopeProperties;

    protected String execute(List<Message> messages, String model, Function<Exception, SingleResponse<String>> exceptionFunction) {
        GenerationParam param = GenerationParam.builder()
            .apiKey(dashScopeProperties.getApiKey())
            .model(model)
            .messages(messages)
            .resultFormat(GenerationParam.ResultFormat.MESSAGE)
            .responseFormat(ResponseFormat.from(ResponseFormat.TEXT))
            .build();
        try (AutoCloseGeneration gen = PoolDashScopeObjectFactory.getGeneration()) {
            return gen.call(param).getOutput().getChoices().get(0).getMessage().getContent();
        } catch (Exception ex) {
            exceptionFunction.apply(ex);
        }
        return null;
    }
}
