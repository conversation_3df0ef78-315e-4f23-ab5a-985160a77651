package com.dangbei.aisearch.app.executor;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.exception.Assert;
import com.alibaba.cola.exception.BizException;
import com.dangbei.aisearch.app.util.UserContextUtil;
import com.dangbei.aisearch.client.dto.NotificationJumpConfig;
import com.dangbei.aisearch.client.dto.cmd.ProcessKnowledgeApplyCmd;
import com.dangbei.aisearch.client.enums.ApplyProcessActionEnum;
import com.dangbei.aisearch.client.enums.NotificationBizTypeEnum;
import com.dangbei.aisearch.client.enums.SharedKnowledgePermissionEnum;
import com.dangbei.aisearch.domain.entity.KnowledgeApplyEntity;
import com.dangbei.aisearch.domain.entity.SharedKnowledgeEntity;
import com.dangbei.aisearch.domain.entity.UserInfoEntity;
import com.dangbei.aisearch.domain.gateway.KnowledgeApplyGateway;
import com.dangbei.aisearch.domain.gateway.UserInfoGateway;
import com.dangbei.aisearch.domain.gateway.UserNotificationGateway;
import com.dangbei.aisearch.infrastructure.config.properties.NotificationProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;

/**
 * 处理知识库申请执行器
 * <AUTHOR>
 * @date 2025-05-27
 **/
@Slf4j
@Component
public class ProcessKnowledgeApplyCmdExe {

    @Resource
    private KnowledgeApplyGateway knowledgeApplyGateway;
    @Resource
    private UserInfoGateway userInfoGateway;
    @Resource
    private UserNotificationGateway userNotificationGateway;
    @Resource
    private NotificationProperties notificationProperties;

    /**
     * 处理知识库申请
     * @param cmd 处理命令
     * @return 处理结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Response execute(ProcessKnowledgeApplyCmd cmd) {
        // 获取当前用户
        String currUserId = UserContextUtil.getNonNullUserId();
        UserInfoEntity userInfo = userInfoGateway.getByUserId(currUserId);
        Assert.notNull(userInfo, "用户不存在");
        // 获取申请
        KnowledgeApplyEntity applyEntity = knowledgeApplyGateway.getByApplyId(cmd.getApplyId());
        Assert.notNull(applyEntity, "申请不存在");
        // 检查申请状态
        if (!applyEntity.isPending()) {
            throw new BizException("该申请已处理，当前状态：" + applyEntity.getStatusDesc());
        }
        // 获取知识库信息
        SharedKnowledgeEntity knowledge = applyEntity.getKnowledgeEntity();
        // 验证权限
        knowledge.validatePermission(currUserId, SharedKnowledgePermissionEnum.APPROVE_APPLY);
        // 判断处理动作
        ApplyProcessActionEnum action = ApplyProcessActionEnum.getByCode(cmd.getAction());
        Assert.notNull(action, "无效的处理动作");
        // 处理申请
        boolean approved = ApplyProcessActionEnum.isApprove(cmd.getAction());
        applyEntity.processApply(currUserId, approved);

        boolean transactionActive = TransactionSynchronizationManager.isActualTransactionActive();
        if (transactionActive) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    String applicantId = applyEntity.getApplicantId();
                    String knowledgeName = knowledge.getName();
                    // 发送通知给申请人
                    userNotificationGateway.sendText(
                        NotificationBizTypeEnum.APPLY_APPROVED,
                        currUserId,
                        applicantId,
                        notificationProperties.getKnowledge().getApplyResultText(approved, knowledgeName),
                        cmd.getApplyId(),
                        new NotificationJumpConfig(notificationProperties.getKnowledge().getApplyResultJumpConfig(approved), approved ? applyEntity.getKnowledgeId() : null));
                }
            });
        }
        return Response.buildSuccess();
    }
}
