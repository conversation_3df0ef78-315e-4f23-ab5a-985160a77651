package com.dangbei.aisearch.app.executor.query;

import com.alibaba.cola.dto.MultiResponse;
import com.dangbei.aisearch.client.dto.clientobject.FunctionListCo;
import com.dangbei.aisearch.common.constant.DcParamConst;
import com.dangbei.aisearch.domain.gateway.DcParamGateway;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-01-13
 */
@Component
public class FunctionListQueryExe {

    @Resource
    private DcParamGateway dcParamGateway;

    public MultiResponse<FunctionListCo> execute(String paramCode) {
        List<FunctionListCo> list = dcParamGateway.listParamVal(DcParamConst.Type.FUNCTION_LIST, paramCode, FunctionListCo.class);
        return MultiResponse.of(list);
    }
}
