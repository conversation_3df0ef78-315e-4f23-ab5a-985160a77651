package com.dangbei.aisearch.infrastructure.i18n;

import cn.hutool.extra.spring.SpringUtil;
import com.dangbei.aisearch.common.enums.I18nValueEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Locale;
import java.util.Objects;

/**
 * 国际化工具
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-14
 */
@Slf4j
public class I18nUtil {

    /**
     * @param valueEnum 值枚举
     * @return String
     */
    public static String get(I18nValueEnum valueEnum) {
        if (Objects.isNull(valueEnum)) {
            return null;
        }
        return get(valueEnum.getValue());
    }

    /**
     * @param code 对应messages配置的key
     * @return String
     */
    public static String get(String code) {
        return get(code, null, "");
    }

    /**
     * @param code           对应messages配置的key
     * @param defaultMessage 没有设置key的时候的默认值
     * @return String
     */
    public static String get(String code, String defaultMessage) {
        return get(code, null, defaultMessage);
    }

    /**
     * @param code 对应messages配置的key
     * @param args 数组参数
     * @return String
     */
    public static String get(String code, Object[] args) {
        return get(code, args, "");
    }

    /**
     * @param valueEnum 值枚举
     * @return String
     */
    public static String get(I18nValueEnum valueEnum, Object[] args) {
        return get(valueEnum.getValue(), args, "");
    }


    /**
     * @param code           对应messages配置的key
     * @param args           数组参数
     * @param defaultMessage 没有设置key的时候的默认值
     * @return String
     */
    public static String get(String code, Object[] args, String defaultMessage) {
        Locale locale = LocaleContextHolder.getLocale();
        String message = getInstance().getMessage(code, args, defaultMessage, locale);
        log.debug("语言为：{}，取出的消息为：{}", locale.getLanguage(), message);
        return message;
    }

    /**
     * 当前语言是否为中文
     * @return String
     */
    public static boolean isChinese() {
        return LangEnum.isChinese(LocaleContextHolder.getLocale().getLanguage());
    }

    /**
     * 获取当前环境语言
     * @return String
     */
    public static String getLang() {
        Locale locale = LocaleContextHolder.getLocale();
        return locale.getLanguage();
    }

    private static MessageSource getInstance() {
        return Lazy.messageSource;
    }

    /**
     * 使用懒加载方式实例化messageSource国际化工具
     */
    private static class Lazy {
        private static final MessageSource messageSource = SpringUtil.getBean(MessageSource.class);
    }

}
