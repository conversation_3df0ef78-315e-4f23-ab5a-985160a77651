
alter table ais_community_post add column base_like_count int default 0 comment '基础点赞数';
alter table ais_community_post add column max_like_count int default 0 comment '最大机器点赞数';

CREATE TABLE `ais_community_post_report` (
     `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
     `post_id` varchar(32) NOT NULL COMMENT '帖子ID',
     `user_id` varchar(64) DEFAULT NULL COMMENT '用户ID',
     `device_id` varchar(64) DEFAULT NULL COMMENT '设备ID',
     `type` int(11) DEFAULT '0' COMMENT '举报类型1-侵权、2-不良、3-违法、4-其他',
     `audit_status` int(11) NOT NULL DEFAULT '0' COMMENT '0-未审核 1-通过 2-未通过',
     `create_person` varchar(16) DEFAULT '' COMMENT '创建人',
     `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '业务创建时间',
     `update_person` varchar(16) DEFAULT '' COMMENT '修改人',
     `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '业务更新时间',
     `is_deleted` bigint(20) NOT NULL DEFAULT '0' COMMENT '逻辑删除标识，0-未删除,其他-已删除',
     `db_modify_timestamp` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库变更时间',
     PRIMARY KEY (`id`),
     KEY `idx_post_user_id` (`user_id`, `post_id`)
) AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 ROW_FORMAT = DYNAMIC COMMENT = '社区帖子举报表';


