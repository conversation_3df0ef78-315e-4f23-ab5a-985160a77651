package com.dangbei.aisearch.app.service;

import com.alibaba.cola.dto.SingleResponse;
import com.dangbei.aisearch.app.executor.query.ScreenShotDownloadInfoQueryExe;
import com.dangbei.aisearch.client.dto.clientobject.ScreenShotDownloadInfoCo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-25
 */
@Service
public class ExternalService {

    @Resource
    private ScreenShotDownloadInfoQueryExe screenShotDownloadInfoQueryExe;

    public SingleResponse<ScreenShotDownloadInfoCo> screenshotDownloadInfo() {
        return screenShotDownloadInfoQueryExe.execute();
    }
}
