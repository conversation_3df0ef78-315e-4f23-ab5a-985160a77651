package com.dangbei.aisearch.infrastructure.gatewayimpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dangbei.aisearch.domain.entity.WriteEntity;
import com.dangbei.aisearch.domain.gateway.WriteGateway;
import com.dangbei.aisearch.infrastructure.common.base.BaseGatewayImpl;
import com.dangbei.aisearch.infrastructure.convertor.WriteConvertor;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.WriteDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.WriteMapper;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Write 网关层实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-26
 */
@Component
public class WriteGatewayImpl extends BaseGatewayImpl<Long, WriteEntity, WriteDO, WriteMapper, WriteConvertor> implements WriteGateway {

    @Override
    public List<String> listAllCode() {
        LambdaQueryWrapper<WriteDO> queryWrapper = Wrappers.lambdaQuery(WriteDO.class)
            .select(WriteDO::getCode);
        return baseMapper.selectList(queryWrapper).stream().map(WriteDO::getCode).collect(Collectors.toList());
    }
}
