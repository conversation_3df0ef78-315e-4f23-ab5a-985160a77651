package com.dangbei.aisearch.domain.gateway;

import com.alibaba.cola.dto.SingleResponse;
import com.dangbei.aisearch.client.enums.DocStorageTypeEnum;
import com.dangbei.aisearch.common.enums.DocProcessStatusEnum;
import com.dangbei.aisearch.domain.common.base.BaseGateway;
import com.dangbei.aisearch.domain.entity.UserKnowledgeDocEntity;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * UserKnowledgeDoc 网关层
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-10
 */
public interface UserKnowledgeDocGateway extends BaseGateway<Long, UserKnowledgeDocEntity> {

    /**
     * 查询总上传字数
     * @param userId 用户ID
     * @return 字数
     */
    long getWordCount(String userId);

    /**
     * 查询总上传文档数
     * @param userId 用户ID
     * @return 文档总数
     */
    long getDocCount(String userId, DocProcessStatusEnum docProcessStatus);

    /**
     * 根据路径查询doc信息
     * @param path 路径
     * @return doc信息
     */
    UserKnowledgeDocEntity getByPath(String path);

    /**
     * 根据docId查询doc信息
     * @param docId 文档Id
     * @return doc信息
     */
    UserKnowledgeDocEntity getByDocId(String docId);

    /**
     * 文档上传火山知识库
     * @param entity         知识库文档
     * @param knowledgeIdExt 火山知识库ID
     * @return 上传结果
     */
    SingleResponse<String> addDocToVolc(UserKnowledgeDocEntity entity, DocStorageTypeEnum storageTypeEnum, String knowledgeIdExt);

    /**
     * 根据文档ID批量查询
     * @param docIds 文档ID
     * @return 文档列表
     */
    List<UserKnowledgeDocEntity> listByDocIds(Collection<String> docIds);


    /**
     * 分批获取创建时间在指定时间范围内的文档
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param offset    偏移量
     * @param batchSize 批次大小
     * @return 文档列表
     */
    List<UserKnowledgeDocEntity> batchLoadDocsBetweenTimeRange(Date startTime,
                                                               Date endTime,
                                                               DocProcessStatusEnum status,
                                                               List<Long> ids,
                                                               int offset, int batchSize);

    /**
     * 获取用户所有外部文档ID
     * @param userId      用户ID
     * @param knowledgeId 知识库ID
     * @param limit       获取条数
     * @return 用户所有文档ID
     */
    List<UserKnowledgeDocEntity> getUserAllDocIds(String userId, String knowledgeId, Integer limit);

    /**
     * 根据知识库ID和状态获取用户所有文档
     * @param knowledgeId          知识库ID
     * @param docProcessStatusEnum 状态
     * @return 文档列表
     */
    List<UserKnowledgeDocEntity> getByKnowledgeIdAndStatus(String knowledgeId, DocProcessStatusEnum docProcessStatusEnum);
}
