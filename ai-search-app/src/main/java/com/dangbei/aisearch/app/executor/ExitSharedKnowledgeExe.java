package com.dangbei.aisearch.app.executor;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.exception.Assert;
import com.dangbei.aisearch.app.util.UserContextUtil;
import com.dangbei.aisearch.client.dto.NotificationJumpConfig;
import com.dangbei.aisearch.client.dto.cmd.ExitSharedKnowledgeCmd;
import com.dangbei.aisearch.client.enums.NotificationBizTypeEnum;
import com.dangbei.aisearch.domain.entity.SharedKnowledgeEntity;
import com.dangbei.aisearch.domain.gateway.KnowledgeMemberGateway;
import com.dangbei.aisearch.domain.gateway.SharedKnowledgeGateway;
import com.dangbei.aisearch.domain.gateway.UserNotificationGateway;
import com.dangbei.aisearch.infrastructure.config.properties.NotificationProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.util.Set;

/**
 * 退出知识库执行器
 * <AUTHOR>
 * @date 2025-05-29
 **/
@Slf4j
@Component
public class ExitSharedKnowledgeExe {

    @Resource
    private SharedKnowledgeGateway sharedKnowledgeGateway;
    @Resource
    private KnowledgeMemberGateway knowledgeMemberGateway;
    @Resource
    private UserNotificationGateway userNotificationGateway;
    @Resource
    private NotificationProperties notificationProperties;

    /**
     * 执行退出知识库
     * @param cmd 退出命令
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Response execute(ExitSharedKnowledgeCmd cmd) {
        String currentUserId = UserContextUtil.getNonNullUserId();
        // 获取知识库信息
        SharedKnowledgeEntity knowledgeEntity = sharedKnowledgeGateway.getByKnowledgeId(cmd.getKnowledgeId());
        Assert.notNull(knowledgeEntity, "知识库不存在");
        // 执行退出操作
        knowledgeEntity.exitKnowledge(currentUserId);
        boolean transactionActive = TransactionSynchronizationManager.isActualTransactionActive();
        if (transactionActive) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    Set<String> userIds = knowledgeMemberGateway.listCreatorAdminUserIdsExcludeCurrUserId(cmd.getKnowledgeId(), currentUserId);
                    String knowledgeId = knowledgeEntity.getKnowledgeId();
                    userNotificationGateway.sendTextBatch(
                        NotificationBizTypeEnum.KNOWLEDGE_EXIT,
                        currentUserId,
                        userIds,
                        notificationProperties.getKnowledge().getExitText(knowledgeEntity.getName()),
                        knowledgeId,
                        new NotificationJumpConfig(notificationProperties.getKnowledge().getExitJumpConfig(), knowledgeId)
                    );
                }
            });
        }
        return Response.buildSuccess();
    }
}
