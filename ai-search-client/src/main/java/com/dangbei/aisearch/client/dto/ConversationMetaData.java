package com.dangbei.aisearch.client.dto;

import com.alibaba.cola.dto.DTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConversationMetaData extends DTO {

    @Schema(description = "帮我写场景编码")
    private String writeCode;

    @Schema(description = "聊天模型配置")
    private ChatModelConfig chatModelConfig;
}
