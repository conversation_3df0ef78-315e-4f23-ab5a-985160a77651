<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.ShareMapper">
    <update id="incrLikeNum">
        UPDATE ais_share
        SET like_num = like_num + 1
        WHERE share_id = #{shareId}
          AND is_deleted = 0
    </update>

    <update id="decrLikeNum">
        UPDATE ais_share
        SET like_num = like_num - 1
        WHERE share_id = #{shareId}
          AND like_num > 0
          AND is_deleted = 0
    </update>
</mapper>
