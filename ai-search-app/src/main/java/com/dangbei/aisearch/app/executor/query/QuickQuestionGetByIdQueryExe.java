package com.dangbei.aisearch.app.executor.query;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.Assert;
import com.dangbei.aisearch.app.assembler.QuickQuestionAssembler;
import com.dangbei.aisearch.app.assembler.WriteAssembler;
import com.dangbei.aisearch.app.util.UserContextUtil;
import com.dangbei.aisearch.app.util.WriteMetaUtil;
import com.dangbei.aisearch.client.dto.clientobject.QuickQuestionCo;
import com.dangbei.aisearch.domain.entity.QuickQuestionEntity;
import com.dangbei.aisearch.domain.entity.WriteMetaEntity;
import com.dangbei.aisearch.domain.gateway.QuickQuestionGateway;
import com.dangbei.aisearch.domain.gateway.WriteMetaGateway;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 根据ID查询快捷提问命令执行器
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-05-01
 */
@Slf4j
@Component
public class QuickQuestionGetByIdQueryExe {

    @Resource
    private QuickQuestionGateway quickQuestionGateway;
    @Resource
    private QuickQuestionAssembler quickQuestionAssembler;
    @Resource
    private WriteMetaGateway writeMetaGateway;
    @Resource
    private WriteAssembler writeAssembler;

    public SingleResponse<QuickQuestionCo> execute(Long id) {
        QuickQuestionEntity entity = quickQuestionGateway.loadById(id);
        Assert.notNull(entity, "快捷提问不存在");
        Assert.equals(entity.getUserId(), UserContextUtil.getNonNullUserId(), "非法数据访问");

        QuickQuestionCo quickQuestionCo = quickQuestionAssembler.toCo(entity);
        List<WriteMetaEntity> entityList = writeMetaGateway.loadByIds(WriteMetaUtil.getMetaIdList(entity.getContent()));
        quickQuestionCo.setMetaList(entityList.stream().map(writeAssembler::toWriteMetaCo).collect(Collectors.toList()));
        quickQuestionCo.setShowContent(WriteMetaUtil.getShowContent(quickQuestionCo.getContent(), quickQuestionCo.getMetaList()));
        return SingleResponse.of(quickQuestionCo);
    }

}
