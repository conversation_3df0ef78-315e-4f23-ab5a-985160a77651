package com.dangbei.aisearch.infrastructure.gatewayimpl;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson2.JSON;
import com.dangbei.aidoggyscreenshot.client.dto.ChatMessageDTO;
import com.dangbei.aidoggyscreenshot.client.dto.clientobject.MessageListCo;
import com.dangbei.aidoggyscreenshot.client.dto.cmd.query.MessagePageQuery;
import com.dangbei.aidoggyscreenshot.client.facade.ScreenshotChatMessageFacade;
import com.dangbei.aisearch.common.constant.CommonConst;
import com.dangbei.aisearch.domain.gateway.ExternalScreenshotGateway;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-11
 */
@Slf4j
@Component
public class ExternalScreenshotGatewayImpl implements ExternalScreenshotGateway {

    @DubboReference(group = CommonConst.Dubbo.GROUP)
    private ScreenshotChatMessageFacade screenshotChatMessageFacade;

    @Override
    public MessageListCo messagePageQuery(MessagePageQuery query) {
        try {
            log.debug("[分页查询小旺截图消息列表]query={}", JSON.toJSONString(query));
            SingleResponse<MessageListCo> response = screenshotChatMessageFacade.pageQuery(query);
            log.debug("[分页查询小旺截图消息列表]response={}", JSON.toJSONString(response));
            if (Objects.nonNull(response) && response.isSuccess()) {
                return response.getData();
            }
        } catch (Exception e) {
            log.error("[分页查询小旺截图消息列表失败]", e);
        }
        return null;
    }

    @Override
    public List<ChatMessageDTO> listAllMessageByConversationId(String conversationId) {
        try {
            log.debug("[查询小旺截图消息列表]conversationId={}", conversationId);
            MultiResponse<ChatMessageDTO> response = screenshotChatMessageFacade.listAllByConversationId(conversationId);
            log.debug("[查询小旺截图消息列表]response={}", JSON.toJSONString(response));
            if (Objects.nonNull(response) && response.isSuccess()) {
                return response.getData();
            }
        } catch (Exception e) {
            log.error("[查询小旺截图消息列表失败]", e);
        }
        return Lists.newArrayList();
    }
}
