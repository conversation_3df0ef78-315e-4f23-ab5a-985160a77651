package com.dangbei.aisearch.app.assembler;

import com.dangbei.aisearch.client.dto.clientobject.SitemapAgentCo;
import com.dangbei.aisearch.client.dto.clientobject.SitemapCommunityPostCo;
import com.dangbei.aisearch.client.dto.clientobject.SitemapShareCo;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.AgentDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.CommunityPostDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.ShareDO;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

import java.util.List;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-04-08
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SitemapAssembler {

    List<SitemapAgentCo> toAgentCoList(List<AgentDO> agentList);

    List<SitemapCommunityPostCo> toPostCoList(List<CommunityPostDO> postList);

    List<SitemapShareCo> toShareCoList(List<ShareDO> shareList);
}
