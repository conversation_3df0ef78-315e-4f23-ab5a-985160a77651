package com.dangbei.aisearch.client.dto.cmd;

import com.alibaba.cola.dto.DTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 共享知识库文档解析命令
 * <AUTHOR>
 * @date 2025-05-27
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class SharedKnowledgeParseDocCmd extends DTO {

    @NotBlank(message = "知识库ID不能为空")
    @Schema(description = "知识库ID")
    private String knowledgeId;

    @NotBlank(message = "文件路径不能为空")
    @Schema(description = "文件路径")
    private String filePath;

    @NotBlank(message = "文件名不能为空")
    @Size(max = 100, message = "文件名长度不能超过100")
    @Schema(description = "文件名")
    private String fileName;

    @Schema(description = "文件大小（字节）")
    private String fileSize;

    @Schema(description = "文档存储方式，1: oss, 2: tos")
    @Min(value = 1, message = "storageType不合法")
    @Max(value = 2, message = "storageType不合法")
    private Integer storageType = 1;
}
