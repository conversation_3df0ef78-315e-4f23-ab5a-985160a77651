package com.dangbei.aisearch.client.dto.clientobject;

import com.alibaba.cola.dto.DTO;
import com.dangbei.aisearch.client.dto.NotificationJumpConfig;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 通知详情客户端对象
 * <AUTHOR>
 * @date 2025-05-27
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class NotificationListCo extends DTO {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "类型(1-普通文本,2-审批)")
    private Integer type;

    @Schema(description = "发送人昵称")
    private String senderNickname;

    @Schema(description = "发送人头像")
    private String senderAvatar;

    @Schema(description = "内容")
    private String content;

    @Schema(description = "跳转配置JSON")
    private NotificationJumpConfig jumpConfig;

    @Schema(description = "是否已读(0-未读,1-已读)")
    private Integer isRead;

    @Schema(description = "创建时间/发送时间")
    private LocalDateTime createTime;

    @Schema(description = "type=2时的个性化字段")
    private ApplyNotificationCo applyInfo;

}
