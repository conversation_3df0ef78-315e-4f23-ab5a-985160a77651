package com.dangbei.aisearch.app.assembler;

import com.dangbei.aisearch.client.dto.clientobject.KnowledgeDocInfoCo;
import com.dangbei.aisearch.domain.entity.UserKnowledgeDocEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-17 21:27
 **/
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface KnowledgeDocAssembler {

    @Mapping(source = "docPath", target = "filePath")
    KnowledgeDocInfoCo toDocInfoCo(UserKnowledgeDocEntity entity);

    List<KnowledgeDocInfoCo> toDocInfoCoList(List<UserKnowledgeDocEntity> entities);
}
