package com.dangbei.aisearch.adapter.web;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.alibaba.cola.dto.PageSingleResponse;
import com.alibaba.cola.dto.Response;
import com.dangbei.aisearch.app.executor.ProcessKnowledgeApplyCmdExe;
import com.dangbei.aisearch.app.executor.SubmitKnowledgeApplyCmdExe;
import com.dangbei.aisearch.app.executor.query.KnowledgeApplyListQueryExe;
import com.dangbei.aisearch.client.dto.clientobject.KnowledgeApplyCo;
import com.dangbei.aisearch.client.dto.cmd.ProcessKnowledgeApplyCmd;
import com.dangbei.aisearch.client.dto.cmd.SubmitKnowledgeApplyCmd;
import com.dangbei.aisearch.client.dto.cmd.query.KnowledgeApplyListQuery;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 共享知识库申请管理服务接口
 * <AUTHOR>
 * @date 2025-05-27
 **/
@RestController
@RequestMapping("/sharedKnowledgeApi/v1/apply")
@Tag(name = "SharedKnowledgeApplyController", description = "共享知识库申请管理服务")
public class SharedKnowledgeApplyController {

    @Resource
    private SubmitKnowledgeApplyCmdExe submitKnowledgeApplyCmdExe;
    @Resource
    private ProcessKnowledgeApplyCmdExe processKnowledgeApplyCmdExe;
    @Resource
    private KnowledgeApplyListQueryExe knowledgeApplyListQueryExe;

    @PostMapping("/submit")
    @Operation(summary = "提交加入申请", description = "提交加入知识库的申请")
    @SaCheckLogin
    public Response submitKnowledgeApply(@Valid @RequestBody SubmitKnowledgeApplyCmd cmd) {
        return submitKnowledgeApplyCmdExe.execute(cmd);
    }

    @PostMapping("/process")
    @Operation(summary = "处理申请", description = "审批知识库加入申请")
    @SaCheckLogin
    public Response processKnowledgeApply(@Valid @RequestBody ProcessKnowledgeApplyCmd cmd) {
        return processKnowledgeApplyCmdExe.execute(cmd);
    }

    @PostMapping("/list")
    @Operation(summary = "申请列表查询", description = "查询知识库的申请列表")
    @SaCheckLogin
    public PageSingleResponse<KnowledgeApplyCo> getApplyList(@Valid @RequestBody KnowledgeApplyListQuery query) {
        return knowledgeApplyListQueryExe.execute(query);
    }
}
