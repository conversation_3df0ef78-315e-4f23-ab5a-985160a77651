package com.dangbei.aisearch.infrastructure.gatewayimpl;

import com.dangbei.aisearch.domain.entity.QuickQuestionEntity;
import com.dangbei.aisearch.domain.gateway.QuickQuestionGateway;
import com.dangbei.aisearch.infrastructure.common.base.BaseGatewayImpl;
import com.dangbei.aisearch.infrastructure.convertor.QuickQuestionConvertor;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.QuickQuestionDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.QuickQuestionMapper;
import org.springframework.stereotype.Component;

/**
 * QuickQuestion 网关层实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-04-25
 */
@Component
public class QuickQuestionGatewayImpl extends BaseGatewayImpl<Long, QuickQuestionEntity, QuickQuestionDO, QuickQuestionMapper, QuickQuestionConvertor> implements QuickQuestionGateway {

}
