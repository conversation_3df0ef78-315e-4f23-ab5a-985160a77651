package com.dangbei.aisearch.infrastructure.config.properties;

import cn.hutool.core.lang.WeightRandom;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 模型路由映射逻辑
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-04-18
 */
@Data
@Component
@NacosConfigurationProperties(dataId = "${nacos.config.data-id:com.dangbei.ai-search.application.yaml}",
    groupId = "${nacos.config.group:ai-search}",
    prefix = "model-router", autoRefreshed = true)
public class ModelRouterProperties {

    /**
     * 路由规则
     */
    private Map<String, ModelMapping> models;

    /**
     * 自建模型路由开关
     */
    private Boolean selfHostedEnable = false;

    /**
     * 自建模型最大并发数，默认15
     */
    private Integer selfHostedMaxConcurrent = 15;

    /**
     * 自建模型并发占用超时时间，5分钟
     */
    private Integer selfHostedTimeout = 300;

    /**
     * 模型路由映射
     * 优先级顺序建议为：
     * •	target（最高优先级）
     * •	conditions
     * •	weighted
     * •	defaultModel（最低优先级）
     * @param modelKey   前端传的模型名称
     * @param userAction 功能选项
     * @return 转换后的模型名称
     */
    public String transferModel(String modelKey, String userAction) {
        ModelMapping mapping = models.get(modelKey);
        if (mapping == null) {
            return modelKey;
        }

        // 简单映射（如：qwen: qwen-plus）
        if (mapping.getTarget() != null) {
            return mapping.getTarget();
        }

        // 条件匹配映射
        if (mapping.getConditions() != null) {
            for (Condition condition : mapping.getConditions()) {
                if (userAction != null && userAction.contains(condition.getIfActionContains())) {
                    if (StrUtil.isNotBlank(condition.getFixedResult())) {
                        return condition.getFixedResult();
                    }

                    String picked = weightedPick(condition.getWeighted());

                    Fallback fallback = condition.getFallback();
                    if (fallback != null) {
                        if (picked.equals(fallback.getIfResultIs())) {
                            return weightedPick(fallback.getThenWeighted());
                        } else {
                            return fallback.getElseResult();
                        }
                    }

                    return picked;
                }
            }
        }

        // 新增逻辑：全局级别的 weighted 映射
        if (mapping.getWeighted() != null && !mapping.getWeighted().isEmpty()) {
            return weightedPick(mapping.getWeighted());
        }

        // 默认
        return mapping.getDefaultModel();
    }

    private String weightedPick(Map<String, Integer> weightedMap) {
        if (weightedMap == null || weightedMap.isEmpty()) return null;

        List<WeightRandom.WeightObj<String>> weightList = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : weightedMap.entrySet()) {
            weightList.add(new WeightRandom.WeightObj<>(entry.getKey(), entry.getValue()));
        }

        WeightRandom<String> wr = RandomUtil.weightRandom(weightList);
        return wr.next();
    }

    @Data
    public static class ModelMapping {
        /**
         * 如果是简单映射（例如 qwen: qwen-plus）
         */
        private String target;

        /**
         * 带条件的复杂映射（例如 deepseek）
         */
        private List<Condition> conditions;

        /**
         * 权重映射
         */
        private Map<String, Integer> weighted;

        /**
         * 默认fallback
         */
        private String defaultModel;
    }

    @Data
    public static class Condition {
        private String ifActionContains;
        private String fixedResult;
        private Map<String, Integer> weighted;
        private Fallback fallback;
    }

    @Data
    public static class Fallback {
        private String ifResultIs;
        private Map<String, Integer> thenWeighted;
        private String elseResult;
    }

    public static void main(String[] args) throws JsonProcessingException {
        String modelStr = """
            {
                 "deepseek": {
                     "conditions": [
                         {
                             "fallback": {
                                 "elseResult": "ep-20250207141251-hsqnn",
                                 "ifResultIs": "FULL_BLOOD",
                                 "thenWeighted": {
                                     "ep-20250205094640-6pv5d": 90,
                                     "deepseek-r1": 10
                                 }
                             },
                             "ifActionContains": "deep",
                             "weighted": {
                                 "DISTILL": 30,
                                 "FULL_BLOOD": 70
                             }
                         }
                     ],
                     "defaultModel": "ep-20250207150114-lzd9k"
                 },
                 "deepseek-v3": {
                     "target": "ep-20250207150114-lzd9k"
                 },
                 "doubao": {
                     "target": "ep-20250210021055-zmvbw"
                 },
                 "qwen": {
                     "target": "qwen-plus"
                 },
                 "qwen-plus": {
                     "target": "qwen-plus"
                 },
                 "moonshot_v1": {
                     "target": "ep-20250314130331-vw77w"
                 },
                 "moonshot-v1-32k": {
                     "target": "moonshot-v1-32k"
                 },
                 "qwq-plus": {
                     "conditions": [
                         {
                             "ifActionContains": "deep",
                             "fixedResult": "qwq-plus"
                         }
                     ],
                     "defaultModel": "qwen-plus"
                 },
                 "doubao-thinking": {
                     "conditions": [
                         {
                             "ifActionContains": "deep",
                             "fixedResult": "ep-20250428095106-8zb5h"
                         }
                     ],
                     "defaultModel": "ep-20250210021055-zmvbw"
                 },
                 "ernie-x1": {
                     "conditions": [
                         {
                             "ifActionContains": "deep",
                             "fixedResult": "ernie-x1-turbo-32k"
                         }
                     ],
                     "defaultModel": "ernie-4.5-turbo-32k"
                 }
             }
            """;

        ModelRouterProperties properties = new ModelRouterProperties();
        ObjectMapper mapper = new ObjectMapper();
        Map<String, ModelMapping> models = mapper.readValue(modelStr, mapper.getTypeFactory().constructMapType(Map.class, String.class, ModelMapping.class));
        properties.setModels(models);

        Map<String, Integer> resultCount = Maps.newHashMap();
        for (int i = 0; i < 1000; i++) {
            String model = properties.transferModel("deepseek", "deep");
            resultCount.merge(model, 1, Integer::sum);
        }

        // 打印统计结果
        resultCount.forEach((modelName, count) -> {
            System.out.println(modelName + ": " + count);
        });
    }

}
