package com.dangbei.aisearch.client.dto.cmd.query;

import com.alibaba.cola.dto.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-04-08
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SitemapQuery extends Query {

    @NotNull(message = "创建开始时间不能为空")
    @Schema(description = "创建开始时间")
    private LocalDateTime startTime;

    @Schema(description = "创建结束时间")
    private LocalDateTime endTime;
}
