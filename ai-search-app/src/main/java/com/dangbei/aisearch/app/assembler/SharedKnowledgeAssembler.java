package com.dangbei.aisearch.app.assembler;

import com.dangbei.aisearch.client.dto.clientobject.KnowledgeShareCo;
import com.dangbei.aisearch.client.dto.clientobject.SharedKnowledgeCo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-05-29
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SharedKnowledgeAssembler {

    KnowledgeShareCo toShareCo(SharedKnowledgeCo sharedKnowledgeCo);
}
