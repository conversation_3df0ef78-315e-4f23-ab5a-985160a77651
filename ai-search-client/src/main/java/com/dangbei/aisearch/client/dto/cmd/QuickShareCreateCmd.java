package com.dangbei.aisearch.client.dto.cmd;

import com.alibaba.cola.dto.Command;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-04-01
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QuickShareCreateCmd extends Command {

    @NotBlank(message = "会话ID不能为空")
    @Schema(description = "会话ID")
    private String conversationId;
}
