package com.dangbei.aisearch.app.executor;

import com.alibaba.cola.common.constant.StrPool;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.BizException;
import com.alibaba.dashscope.aigc.imagesynthesis.ImageSynthesis;
import com.alibaba.dashscope.aigc.imagesynthesis.ImageSynthesisParam;
import com.alibaba.dashscope.aigc.imagesynthesis.ImageSynthesisResult;
import com.baomidou.lock.annotation.Lock4j;
import com.dangbei.aisearch.app.util.RequestThreadLocalUtil;
import com.dangbei.aisearch.client.dto.HeaderDTO;
import com.dangbei.aisearch.client.dto.clientobject.GenAvatarTaskCo;
import com.dangbei.aisearch.client.dto.cmd.AgentAvatarGenCmd;
import com.dangbei.aisearch.client.enums.AppTypeEnum;
import com.dangbei.aisearch.common.constant.LockKey;
import com.dangbei.aisearch.infrastructure.config.properties.DashScopeProperties;
import com.dangbei.aisearch.infrastructure.config.properties.GenAvatarProperties;
import com.dangbei.framework.insight.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-03-26 17:02
 **/
@Slf4j
@Component
public class GenAvatarCmdExe {

    @Resource
    private DashScopeProperties dashScopeProperties;
    @Resource
    private GenAvatarProperties genAvatarProperties;
    @Resource
    private GenAvatarLimitJudgeCmdExe genAvatarLimitJudgeCmdExe;

    public static final String DAILY_OPERATE_FREQUENT_MSG = "每日生成头像已达次数限制";

    @Lock4j(keys = "#userId", expire = 10000L)
    public SingleResponse<GenAvatarTaskCo> execute(String userId, AgentAvatarGenCmd cmd, boolean isEnrich) {
        // 次数限制判断
        limitJudge(userId);
        var genImageConfig = dashScopeProperties.getGenImageConfig();
        // 生成 prompt
        String prompt = generatePrompt(cmd);
        ImageSynthesisParam param =
            ImageSynthesisParam.builder()
                .apiKey(dashScopeProperties.getApiKey())
                .model(genImageConfig.getModel())
                .prompt(prompt)
                .n(getGenNum(isEnrich))
                .size(getGenSize(isEnrich))
                .build();

        ImageSynthesis imageSynthesis = new ImageSynthesis();
        ImageSynthesisResult result;
        try {
            result = imageSynthesis.asyncCall(param);
        } catch (Exception e) {
            log.error("生成头像异常, :{}", e.getMessage(), e);
            throw new BizException("-1", "生成头像异常");
        }
        if (Objects.isNull(result.getOutput()) || StringUtils.isBlank(result.getOutput().getTaskId())) {
            log.warn("生成头像异常,返回任务数据为空：{}", result);
            throw new BizException("-1", "生成头像异常");
        }
        return SingleResponse.of(GenAvatarTaskCo.builder().taskId(result.getOutput().getTaskId()).build());
    }

    public boolean isHitLimit(String userId) {
        return !genAvatarLimitJudgeCmdExe.execute(userId);
    }

    private void limitJudge(String userId) {
        String dailyLimitKey = String.format(LockKey.GEN_AGENT_AVATAR_LOCK, userId, LocalDate.now());
        if (RedisUtil.isFrequent(dailyLimitKey, genAvatarProperties.getGenLimitPreDay(), genAvatarProperties.getLockTimeSeconds())) {
            log.warn("每日生成头像已达每日限制={}", userId);
            throw new BizException(DAILY_OPERATE_FREQUENT_MSG);
        }
    }

    private String getGenSize(boolean isEnrich) {
        if (isEnrich) {
            return genAvatarProperties.getSquareAvatarSize();
        }
        return genAvatarProperties.getAppAvatarSize();
    }

    private String generatePrompt(AgentAvatarGenCmd cmd) {
        return  cmd.getDescription() + StrPool.COMMA + cmd.getStyle();
    }

    private int getGenNum(boolean isEnrich) {
        if (isEnrich) {
            return 1;
        }
        var headerDTO = RequestThreadLocalUtil.getHeaderDTO();
        var appTypeEnum = Optional.ofNullable(headerDTO).map(HeaderDTO::getAppType).orElse(AppTypeEnum.WEB);
        return appTypeEnum.isSmallScreen() ? 2 : 1;
    }
}
