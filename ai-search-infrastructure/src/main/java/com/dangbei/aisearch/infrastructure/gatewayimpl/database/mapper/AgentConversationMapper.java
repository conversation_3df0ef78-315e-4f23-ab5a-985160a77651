package com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dangbei.aisearch.client.dto.AgentIdUsageCountDTO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.AgentConversationDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AgentConversation Mapper接口
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-28
 */
public interface AgentConversationMapper extends BaseMapper<AgentConversationDO> {

    /**
     * 智能体使用次数
     * @param agentIds 智能体ID集合
     * @return {@link List }<{@link AgentIdUsageCountDTO }>
     */
    List<AgentIdUsageCountDTO> listUsageCount(@Param("agentIds") List<String> agentIds);

    /**
     * 获取该用户id下的所有智能体id(去重，不包括已删除)
     * @param userId 用户id
     * @return {@link List }<{@link String }>
     */
    List<String> getAllAgentIdByUserId(String userId);
}
