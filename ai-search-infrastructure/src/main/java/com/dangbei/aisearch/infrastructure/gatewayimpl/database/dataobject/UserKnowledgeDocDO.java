package com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dangbei.aisearch.infrastructure.common.base.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * UserKnowledgeDoc DO对象
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-10
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("ais_user_knowledge_doc")
public class UserKnowledgeDocDO extends BaseDO<Long> {

    @Schema(description = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "当贝知识库ID")
    @TableField(value = "knowledge_id")
    private String knowledgeId;

    @Schema(description = "当贝文档ID")
    @TableField(value = "doc_id")
    private String docId;

    @Schema(description = "用户文档名称")
    @TableField(value = "doc_name")
    private String docName;

    @Schema(description = "OSS文件路径")
    @TableField(value = "doc_path")
    private String docPath;

    @Schema(description = "文档的类型，如docx")
    @TableField(value = "doc_type")
    private String docType;

    @Schema(description = "文件字节数")
    @TableField(value = "doc_size")
    private Long docSize;

    @Schema(description = "文件MD5")
    @TableField(value = "md5")
    private String md5;

    @Schema(description = "文档的处理状态 0-处理中 1-处理完成 2-处理失败")
    @TableField(value = "process_status")
    private Integer processStatus;

    @Schema(description = "文档提取出的point数量")
    @TableField(value = "point_num")
    private Integer pointNum;

    @Schema(description = "文档字数")
    @TableField(value = "word_num")
    private Long wordNum;

    @Schema(description = "外部文档ID")
    @TableField(value = "doc_id_ext")
    private String docIdExt;

    @Schema(description = "失败原因")
    @TableField(value = "fail_reason")
    private String failReason;

    @Schema(description = "火山知识库文档删除标记 0-未删除 1-已删除 2-删除失败")
    @TableField(value = "volc_delete_flag")
    private Integer volcDeleteFlag;

    @Schema(description = "火山知识库文档删除失败原因")
    @TableField(value = "volc_delete_fail_reason")
    private String volcDeleteFailReason;

    @Schema(description = "文档摘要")
    private String summary;

    @Schema(description = "文档存储类型")
    @TableField(value = "storage_type")
    private String storageType;

    @Override
    public Long getPrimaryId() {
        return this.id;
    }

    @Override
    public void setPrimaryId(Long id) {
        this.id = id;
    }
}
