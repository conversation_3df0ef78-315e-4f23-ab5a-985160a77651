package com.dangbei.aisearch.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-01-10
 */
@Getter
@AllArgsConstructor
public enum ShareStatusEnum {

    NORMAL(1, "正常"),
    STOP(2, "已停止分享");

    private final Integer status;

    private final String desc;

    public static boolean isNormal(Integer status) {
        return Objects.equals(status, NORMAL.getStatus());
    }

    public static boolean isStop(Integer status) {
        return Objects.equals(status, STOP.getStatus());
    }
}
