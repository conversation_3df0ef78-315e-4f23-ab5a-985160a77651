package com.dangbei.aisearch.client.dto.clientobject;

import com.alibaba.cola.dto.DTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025-03-17 21:13
 **/
@Data
@Builder
@EqualsAndHashCode(callSuper = true)
public class KnowledgeDocInfoCo extends DTO {

    @Schema(description = "文档ID")
    private String docId;

    @Schema(description = "OSS文件路径")
    private String filePath;

    @Schema(description = "文档的处理状态 0-处理中 1-处理完成 2-处理失败, 3-内容不合法")
    private Integer processStatus;

    @Schema(description = "文档名称")
    private String docName;

    @Schema(description = "文档的类型，如docx")
    private String docType;

    @Schema(description = "文档字数")
    private Long wordNum;

    @Schema(description = "文档大小")
    private Long docSize;

    @Schema(description = "上传时间戳")
    private String createTime;

    @Schema(description = "文档摘要")
    private String summary;
}
