package com.dangbei.aisearch.common.util;

import cn.hutool.core.util.StrUtil;

/**
 * 模型工具
 * <AUTHOR> href="mailto:yinyanta<PERSON>@dangbei.com"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-06-07
 */
public class ModelUtil {

    public static final String ERNIE_X1_PREFIX = "ernie-x1-";
    public static final String QWQ_PLUS = "qwq-plus";
    public static final String PRO_DEEPSEEK_R1 = "pro-deepseek-r1";
    public static final String DEEPSEEK_R1 = "deepseek-r1";
    public static final String DEEPSEEK_R1_DISTILL_QWEN_32B = "deepseek-r1-distill-qwen-32b";
    public static final String EP_DEEPSEEK_R1 = "ep-20250205094640-6pv5d";
    public static final String EP_DEEPSEEK_R1_DISTILL_QWEN_32B = "ep-20250207141251-hsqnn";
    public static final String EP_DOUBAO_THINKING_PRO = "ep-20250428095106-8zb5h";
    // DeepSeek-r1-0528 版本
    public static final String EP_DEEPSEEK_R1_0528 = "ep-20250530093927-h99xh";
    public static final String TENCENT_DEEPSEEK_R1_0528 = "deepseek-r1-0528";
    // 自建R1
    public static final String DANGBEI_DEEPSEEK_R1 = "dangbei-deepseek-r1";
    // 自建V3
    public static final String DANGBEI_DEEPSEEK_V3 = "dangbei-deepseek-v3";

    /**
     * 是否推理模型
     * @param model 当前模型
     * @return true=是
     */
    public static boolean isReasoningModel(String model) {
        return EP_DEEPSEEK_R1.equals(model)
            || EP_DEEPSEEK_R1_DISTILL_QWEN_32B.equals(model)
            || DEEPSEEK_R1.equals(model)
            || DEEPSEEK_R1_DISTILL_QWEN_32B.equals(model)
            || QWQ_PLUS.equals(model)
            || EP_DOUBAO_THINKING_PRO.equals(model)
            || StrUtil.startWith(model, ERNIE_X1_PREFIX)
            || PRO_DEEPSEEK_R1.equals(model)
            || EP_DEEPSEEK_R1_0528.equals(model)
            || TENCENT_DEEPSEEK_R1_0528.equals(model)
            || DANGBEI_DEEPSEEK_R1.equals(model);
    }

    /**
     * 是否自建模型
     * @param model 当前模型
     * @return true=是
     */
    public static boolean isSelfHostedModel(String model) {
        // dangbei-开头的都算
        return DANGBEI_DEEPSEEK_R1.equals(model)
            || DANGBEI_DEEPSEEK_V3.equals(model)
            || StrUtil.startWith(model, "dangbei-");
    }

}
