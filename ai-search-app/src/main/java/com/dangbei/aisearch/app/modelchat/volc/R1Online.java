package com.dangbei.aisearch.app.modelchat.volc;

import com.alibaba.cola.extension.Extension;
import com.dangbei.aisearch.app.model.ChatContext;
import com.dangbei.aisearch.app.model.MessageModel;
import com.dangbei.aisearch.common.constant.CommonConst;
import com.dangbei.aisearch.common.enums.I18nValueEnum;
import com.dangbei.aisearch.common.enums.MsgContentTypeEnum;
import com.dangbei.aisearch.infrastructure.config.properties.VolcEngineArkProperties;
import com.dangbei.aisearch.infrastructure.i18n.I18nUtil;
import com.theokanning.openai.completion.chat.ChatCompletionRequest;
import com.theokanning.openai.completion.chat.ChatMessage;
import com.theokanning.openai.completion.chat.UserMessage;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.LinkedList;

/**
 * R1 + 联网
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-24
 */
@Slf4j
@Component("r1_online_volc_model_chat")
@Extension(bizId = CommonConst.ModelIntent.DEEPSEEK_ONLINE_THINK_DOUBAO)
public class R1Online extends AbstractVolcModelChatExt {

    @Resource
    private VolcEngineArkProperties volcEngineArkProperties;

    @Override
    @SneakyThrows
    protected void preHandle(ChatContext ctx) {
        MessageModel.Msg notice = ctx.newSseMsg(MsgContentTypeEnum.PROGRESS, I18nUtil.get(I18nValueEnum.CHAT_WEB_SEARCHING));
        ctx.sendDeltaAnswerSSE(notice);
    }

    @Override
    protected ChatCompletionRequest buildParam(ChatContext ctx) {
        // 上下文记忆
        LinkedList<ChatMessage> messages = filterMemoryTurns(ctx.getHistory(), 6);
        messages.add(new UserMessage(ctx.getChatCmd().getQuestion()));

        ChatCompletionRequest build = ChatCompletionRequest.builder()
            .model(volcEngineArkProperties.getDeepSeekR1Model())
            .messages(messages)
            .build();
        build.setEnableCustomSearch(Boolean.TRUE);
        return build;
    }

}
