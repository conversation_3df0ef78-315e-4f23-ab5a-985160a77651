package com.dangbei.aisearch.infrastructure.gatewayimpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dangbei.aisearch.client.dto.UserDeviceDTO;
import com.dangbei.aisearch.domain.entity.AgentConversationEntity;
import com.dangbei.aisearch.domain.gateway.AgentConversationGateway;
import com.dangbei.aisearch.infrastructure.common.base.BaseGatewayImpl;
import com.dangbei.aisearch.infrastructure.convertor.AgentConversationConvertor;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.AgentConversationDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.AgentConversationMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * AgentConversation 网关层实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-28
 */
@Component
public class AgentConversationGatewayImpl extends BaseGatewayImpl<Long, AgentConversationEntity, AgentConversationDO, AgentConversationMapper, AgentConversationConvertor> implements AgentConversationGateway {

    @Override
    public AgentConversationEntity getByUserDeviceInfoAndAgentId(UserDeviceDTO userDeviceDTO, String agentId) {
        if (StringUtils.isBlank(agentId) || Objects.isNull(userDeviceDTO) || StringUtils.isAllBlank(userDeviceDTO.getUserId(), userDeviceDTO.getDeviceId())) {
            return null;
        }
        LambdaQueryWrapper<AgentConversationDO> queryWrapper = Wrappers.lambdaQuery(AgentConversationDO.class);
        if (StringUtils.isNotBlank(userDeviceDTO.getUserId())) {
            queryWrapper.eq(AgentConversationDO::getUserId, userDeviceDTO.getUserId());
        } else if (StringUtils.isNotBlank(userDeviceDTO.getDeviceId())) {
            queryWrapper.eq(AgentConversationDO::getDeviceId, userDeviceDTO.getDeviceId());
        }
        queryWrapper.eq(AgentConversationDO::getAgentId, agentId);
        queryWrapper.last(LIMIT_ONE);
        return convertor.toEntity(baseMapper.selectOne(queryWrapper));
    }

    @Override
    public AgentConversationEntity getByConversationId(String conversationId) {
        LambdaQueryWrapper<AgentConversationDO> queryWrapper = Wrappers.lambdaQuery(AgentConversationDO.class)
            .eq(AgentConversationDO::getConversationId, conversationId);
        return convertor.toEntity(baseMapper.selectOne(queryWrapper));
    }

    @Override
    public List<AgentConversationEntity> listByUserIdOrDeviceId(UserDeviceDTO userDeviceDTO, Integer limit) {
        if (Objects.isNull(userDeviceDTO) || StringUtils.isAllBlank(userDeviceDTO.getUserId(), userDeviceDTO.getDeviceId())) {
            return null;
        }
        LambdaQueryWrapper<AgentConversationDO> wrapper = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(userDeviceDTO.getUserId())) {
            wrapper.eq(AgentConversationDO::getUserId, userDeviceDTO.getUserId());
        } else if (StringUtils.isNotBlank(userDeviceDTO.getDeviceId())) {
            wrapper.eq(AgentConversationDO::getDeviceId, userDeviceDTO.getDeviceId());
        }
        wrapper.orderByDesc(AgentConversationDO::getLastChatTime);
        if (Objects.nonNull(limit)) {
            wrapper.last("limit " + limit);
        }
        return convertor.toEntityList(baseMapper.selectList(wrapper));
    }

    @Override
    public void updateLastChatTime(String conversationId) {
        if (StringUtils.isBlank(conversationId)) {
            return;
        }
        LambdaUpdateWrapper<AgentConversationDO> updateWrapper = Wrappers.lambdaUpdate(AgentConversationDO.class)
            .set(AgentConversationDO::getLastChatTime, LocalDateTime.now())
            .eq(AgentConversationDO::getConversationId, conversationId);
        baseMapper.update(updateWrapper);
    }

    @Override
    public void mergeConversation(String deviceId, String userId) {
        if (StringUtils.isAnyBlank(deviceId, userId)) {
            return;
        }
        List<String> agentIds = baseMapper.getAllAgentIdByUserId(userId);
        LambdaUpdateWrapper<AgentConversationDO> updateWrapper = Wrappers.lambdaUpdate(AgentConversationDO.class)
            .set(AgentConversationDO::getUserId, userId)
            .eq(AgentConversationDO::getDeviceId, deviceId)
            .isNull(AgentConversationDO::getUserId)
            .notIn(CollectionUtils.isNotEmpty(agentIds), AgentConversationDO::getAgentId, agentIds);
        baseMapper.update(updateWrapper);
    }
}
