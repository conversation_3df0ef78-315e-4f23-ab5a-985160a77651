package com.dangbei.aisearch.app.service;

import com.dangbei.aisearch.app.executor.query.AgentCategoryQueryExe;
import com.dangbei.aisearch.client.dto.clientobject.AgentPageCo;
import com.dangbei.aisearch.client.dto.cmd.query.AgentCategoryQuery;
import com.dangbei.aisearch.common.constant.DcParamConst;
import com.dangbei.aisearch.domain.gateway.DcParamGateway;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025-02-28 16:08
 **/
@Component
@Resource
@RequiredArgsConstructor
@Slf4j
public class AgentCategoryService {

    private final AgentCategoryQueryExe agentCategoryQueryExe;

    private final DcParamGateway dcParamGateway;

    public AgentPageCo getPageInfo(AgentCategoryQuery query) {
        var agentPageCo = AgentPageCo.builder()
            .categories(agentCategoryQueryExe.execute(query))
            .build();
        try {
            // 副标题
            agentPageCo.setSubtitle(dcParamGateway.getParamVal(DcParamConst.Type.AGENT, DcParamConst.ParamCode.SUBTITLE, String.class));
        } catch (Exception e) {
            log.error("获取智能体副标题失败", e);
        }
        return agentPageCo;
    }
}
