package com.dangbei.aisearch.app.executor.query;

import com.alibaba.cola.common.util.BeanCopyUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.dangbei.aisearch.app.util.UserDeviceUtil;
import com.dangbei.aisearch.client.dto.UserDeviceDTO;
import com.dangbei.aisearch.client.dto.clientobject.QuickNoteCo;
import com.dangbei.aisearch.domain.entity.QuickNoteEntity;
import com.dangbei.aisearch.domain.gateway.QuickNoteGateway;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-01-14
 */
@Component
public class QuickNoteGetByNoteNoQueryExe {

    @Resource
    private QuickNoteGateway quickNoteGateway;

    public SingleResponse<QuickNoteCo> execute(String noteNo) {
        UserDeviceDTO userDeviceInfo = UserDeviceUtil.getUserDeviceInfo();
        QuickNoteEntity quickNoteEntity = quickNoteGateway.getByUserDeviceInfoAndNoteNo(userDeviceInfo, noteNo);
        return SingleResponse.of(BeanCopyUtil.copyOf(quickNoteEntity, QuickNoteCo::new));
    }
}
