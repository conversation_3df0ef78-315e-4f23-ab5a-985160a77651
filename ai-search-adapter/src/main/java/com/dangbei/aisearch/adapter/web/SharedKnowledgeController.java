package com.dangbei.aisearch.adapter.web;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageSingleResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.dangbei.aisearch.app.executor.JoinSharedKnowledgeCmdExe;
import com.dangbei.aisearch.app.service.SharedKnowledgeDocService;
import com.dangbei.aisearch.app.service.SharedKnowledgeService;
import com.dangbei.aisearch.client.dto.SharedKnowledgePreCheckCo;
import com.dangbei.aisearch.client.dto.clientobject.SharedKnowledgeCo;
import com.dangbei.aisearch.client.dto.clientobject.SharedKnowledgeDocCo;
import com.dangbei.aisearch.client.dto.clientobject.SharedKnowledgeDocInfoCo;
import com.dangbei.aisearch.client.dto.clientobject.SharedKnowledgeDocParseCo;
import com.dangbei.aisearch.client.dto.clientobject.SharedKnowledgeListItemCo;
import com.dangbei.aisearch.client.dto.clientobject.TransferDocumentResultCo;
import com.dangbei.aisearch.client.dto.cmd.DeleteSharedKnowledgeCmd;
import com.dangbei.aisearch.client.dto.cmd.ExitSharedKnowledgeCmd;
import com.dangbei.aisearch.client.dto.cmd.JoinSharedKnowledgeCmd;
import com.dangbei.aisearch.client.dto.cmd.KnowledgeShareCmd;
import com.dangbei.aisearch.client.dto.cmd.SaveSharedKnowledgeCmd;
import com.dangbei.aisearch.client.dto.cmd.SharedKnowledgeDocDeleteCmd;
import com.dangbei.aisearch.client.dto.cmd.SharedKnowledgeDocRenameCmd;
import com.dangbei.aisearch.client.dto.cmd.SharedKnowledgeParseDocCmd;
import com.dangbei.aisearch.client.dto.cmd.TransferDocumentCmd;
import com.dangbei.aisearch.client.dto.cmd.query.SharedKnowledgeDocDownloadUrlQuery;
import com.dangbei.aisearch.client.dto.cmd.query.SharedKnowledgeDocInfosQuery;
import com.dangbei.aisearch.client.dto.cmd.query.SharedKnowledgeDocListQuery;
import com.dangbei.aisearch.client.dto.cmd.query.SharedKnowledgeListQuery;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 共享知识库服务接口
 * <AUTHOR>
 * @date 2025-05-27
 **/
@RestController
@RequestMapping("/sharedKnowledgeApi/v1")
@Tag(name = "SharedKnowledgeController", description = "共享知识库服务")
public class SharedKnowledgeController {

    @Resource
    private SharedKnowledgeService sharedKnowledgeService;
    @Resource
    private SharedKnowledgeDocService sharedKnowledgeDocService;
    @Resource
    private JoinSharedKnowledgeCmdExe joinSharedKnowledgeCmdExe;

    // ==================== 共享知识库管理接口 ====================

    @PostMapping("/save")
    @Operation(summary = "保存共享知识库", description = "保存共享知识库（新增/更新）")
    @SaCheckLogin
    public SingleResponse<String> saveSharedKnowledge(@Valid @RequestBody SaveSharedKnowledgeCmd cmd) {
        return sharedKnowledgeService.saveSharedKnowledge(cmd);
    }

    @GetMapping("/detail")
    @Operation(summary = "查询共享知识库详情", description = "查询共享知识库详情")
    @SaCheckLogin
    public SingleResponse<SharedKnowledgeCo> getSharedKnowledgeDetail(@RequestParam("knowledgeId") String knowledgeId) {
        return sharedKnowledgeService.getSharedKnowledgeDetail(knowledgeId);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除共享知识库", description = "删除共享知识库")
    @SaCheckLogin
    public Response deleteSharedKnowledge(@Valid @RequestBody DeleteSharedKnowledgeCmd cmd) {
        return sharedKnowledgeService.deleteSharedKnowledge(cmd);
    }

    @SaCheckLogin
    @PostMapping("/share")
    @Operation(summary = "分享", description = "分享")
    public SingleResponse<String> share(@Valid @RequestBody KnowledgeShareCmd cmd) {
        return sharedKnowledgeService.share(cmd);
    }

    @PostMapping("/join")
    @Operation(summary = "直接加入知识库", description = "直接加入不需要审批的知识库")
    @SaCheckLogin
    public Response joinSharedKnowledge(@Valid @RequestBody JoinSharedKnowledgeCmd cmd) {
        return joinSharedKnowledgeCmdExe.execute(cmd);
    }

    @PostMapping("/exit")
    @Operation(summary = "退出知识库", description = "用户退出指定知识库，创建者不能退出")
    @SaCheckLogin
    public Response exitSharedKnowledge(@Valid @RequestBody ExitSharedKnowledgeCmd cmd) {
        return sharedKnowledgeService.exitSharedKnowledge(cmd);
    }

    // ==================== 知识库列表接口 ====================

    @PostMapping("/list/all")
    @Operation(summary = "全部知识库列表", description = "获取用户的全部知识库列表（个人+共享）")
    @SaCheckLogin
    public PageSingleResponse<SharedKnowledgeListItemCo> getAllKnowledgeList(@Valid @RequestBody SharedKnowledgeListQuery query) {
        return sharedKnowledgeService.getAllKnowledgeList(query);
    }

    @PostMapping("/list/created")
    @Operation(summary = "我创建的知识库", description = "获取用户创建的知识库列表")
    @SaCheckLogin
    public PageSingleResponse<SharedKnowledgeListItemCo> getCreatedKnowledgeList(@Valid @RequestBody SharedKnowledgeListQuery query) {
        return sharedKnowledgeService.getCreatedKnowledgeList(query);
    }

    @PostMapping("/list/joined")
    @Operation(summary = "我加入的知识库", description = "获取用户加入的知识库列表")
    @SaCheckLogin
    public PageSingleResponse<SharedKnowledgeListItemCo> getJoinedKnowledgeList(@Valid @RequestBody SharedKnowledgeListQuery query) {
        return sharedKnowledgeService.getJoinedKnowledgeList(query);
    }

    // ==================== 共享知识库文档相关接口 ====================

    @PostMapping("/doc/preCheck")
    @Operation(summary = "上传文件前置校验", description = "上传文件前置校验")
    @SaCheckLogin
    public SingleResponse<SharedKnowledgePreCheckCo> preCheck(@RequestParam("knowledgeId") String knowledgeId) {
        return sharedKnowledgeDocService.preCheck(knowledgeId);
    }

    @PostMapping("/doc/docParse")
    @Operation(summary = "提交文档解析请求", description = "提交文档解析请求")
    @SaCheckLogin
    public SingleResponse<SharedKnowledgeDocParseCo> parseDoc(@Valid @RequestBody SharedKnowledgeParseDocCmd cmd) {
        return sharedKnowledgeDocService.parseDoc(cmd);
    }

    @PostMapping("/doc/pageQuery")
    @Operation(summary = "知识库文档列表", description = "分页查询知识库中的文档列表")
//    @SaCheckLogin
    public PageSingleResponse<SharedKnowledgeDocCo> pageQuery(@Valid @RequestBody SharedKnowledgeDocListQuery query) {
        return sharedKnowledgeDocService.pageQuery(query);
    }

    @PostMapping("/doc/infos")
    @Operation(summary = "批量获取文档信息", description = "批量获取文档信息")
    @SaCheckLogin
    public MultiResponse<SharedKnowledgeDocInfoCo> getDocInfos(@Valid @RequestBody SharedKnowledgeDocInfosQuery query) {
        return sharedKnowledgeDocService.getDocInfos(query);
    }

    @PostMapping("/doc/delete")
    @Operation(summary = "删除文档", description = "删除知识库中的文档")
    @SaCheckLogin
    public Response deleteDoc(@Valid @RequestBody SharedKnowledgeDocDeleteCmd cmd) {
        return sharedKnowledgeDocService.deleteDoc(cmd);
    }

    @PostMapping("/doc/rename")
    @Operation(summary = "文档重命名", description = "重命名知识库中的文档")
    @SaCheckLogin
    public Response renameDoc(@Valid @RequestBody SharedKnowledgeDocRenameCmd cmd) {
        return sharedKnowledgeDocService.renameDoc(cmd);
    }

    @PostMapping("/doc/getDownloadUrl")
    @Operation(summary = "获取文档下载地址", description = "获取知识库文档的下载地址")
    @SaCheckLogin
    public SingleResponse<String> getDocDownloadUrl(@Valid @RequestBody SharedKnowledgeDocDownloadUrlQuery query) {
        return sharedKnowledgeDocService.getDocDownloadUrl(query);
    }

    @PostMapping("/doc/transfer")
    @Operation(summary = "文档转移", description = "在知识库间复制或移动文档")
    @SaCheckLogin
    public SingleResponse<TransferDocumentResultCo> transferDocument(@Valid @RequestBody TransferDocumentCmd cmd) {
        return sharedKnowledgeDocService.transferDocument(cmd);
    }
}
