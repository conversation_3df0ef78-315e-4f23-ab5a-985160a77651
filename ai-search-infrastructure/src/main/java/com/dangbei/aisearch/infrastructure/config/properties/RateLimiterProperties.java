package com.dangbei.aisearch.infrastructure.config.properties;

import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-10
 */
@Data
@Component
@NacosConfigurationProperties(dataId = "${nacos.config.data-id:com.dangbei.ai-search.application.yaml}",
    groupId = "${nacos.config.group:ai-search}",
    prefix = "ratelimiter",
    autoRefreshed = true)
public class RateLimiterProperties {

    private boolean enabled = false;

    /**
     * 单个ip每天聊天限制访问次数
     */
    private Integer ipLimitTimes;

    /**
     * 单个deviceId每天聊天限制访问次数
     */
    private Integer deviceLimitTimes;

    /**
     * 调用deepSeek模型锁秒数
     */
    private Integer deepSeekLockSeconds;

    /**
     * 限流提示词语
     */
    private String rateLimitMsg = "抱歉，你今天的使用次数已超过限额！";

    /**
     * 需要封禁的问题
     */
    private List<String> banQuestionList;

}
