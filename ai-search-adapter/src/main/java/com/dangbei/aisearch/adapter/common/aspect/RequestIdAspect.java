package com.dangbei.aisearch.adapter.common.aspect;

import com.alibaba.cola.common.constant.RequestConstant;
import com.alibaba.cola.dto.Response;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.MDC;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * 请求 id 切面
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/05/29
 */
@Order(0)
@Aspect
@Component
public class RequestIdAspect {

    /**
     * 切点
     */
    @Pointcut("@within(org.springframework.web.bind.annotation.RestController)||@within(org.springframework.stereotype.Controller)")
    public void pointcut() {
    }

    /**
     * 请求返回后执行该方法
     * @param result 返回值
     */
    @AfterReturning(returning = "result", pointcut = "pointcut()")
    public void doAfterReturning(Object result) {
        String requestId = MDC.get(RequestConstant.REQUEST_ID);
        // 响应对象封装链路 ID
        if (result instanceof Response response) {
            response.setRequestId(requestId);
        }
        // response header 增加 requestId
        setHeaderRequestId(requestId);
    }

    /**
     * 请求抛出异常后执行该方法
     */
    @AfterThrowing(pointcut = "pointcut()")
    public void doAfterThrowing() {
        String requestId = MDC.get(RequestConstant.REQUEST_ID);
        // response header 增加 requestId
        setHeaderRequestId(requestId);
    }

    /**
     * response header 增加 requestId
     * @param requestId 请求 id
     */
    private void setHeaderRequestId(String requestId) {
        // response header 增加 requestId
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (Objects.nonNull(servletRequestAttributes)) {
            HttpServletResponse response = servletRequestAttributes.getResponse();
            if (Objects.nonNull(response)) {
                response.setHeader(RequestConstant.REQUEST_ID, requestId);
            }
        }
    }
}
