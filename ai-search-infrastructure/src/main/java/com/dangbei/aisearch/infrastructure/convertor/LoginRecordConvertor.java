package com.dangbei.aisearch.infrastructure.convertor;

import com.dangbei.aisearch.domain.entity.LoginRecordEntity;
import com.dangbei.aisearch.infrastructure.common.base.BaseConvertor;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.LoginRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 转换器 Entity <---> DO
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-05-19
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface LoginRecordConvertor extends BaseConvertor<LoginRecordDO, LoginRecordEntity> {

}
