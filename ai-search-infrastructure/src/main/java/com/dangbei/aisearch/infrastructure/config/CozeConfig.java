package com.dangbei.aisearch.infrastructure.config;

import com.coze.openapi.service.auth.JWTOAuth;
import com.coze.openapi.service.auth.JWTOAuthClient;
import com.coze.openapi.service.config.Consts;
import com.coze.openapi.service.service.CozeAPI;
import com.dangbei.aisearch.infrastructure.config.properties.CozeProperties;
import lombok.SneakyThrows;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * 扣子配置
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-25
 */
@Configuration
public class CozeConfig {

    @Resource
    private CozeProperties cozeProperties;

    @Bean(name = "cozeApi")
    public CozeAPI cozeApi() {
        // TokenAuth authCli = new TokenAuth(cozeProperties.getAccessToken());
        return new CozeAPI.Builder()
            .baseURL(Consts.COZE_CN_BASE_URL)
            .auth(new JWTOAuth(cozeJwtOauthClient()))
            .readTimeout(300000)
            .connectTimeout(5000)
            .build();
    }

    @SneakyThrows
    @Bean(name = "cozeJwtOauthClient")
    public JWTOAuthClient cozeJwtOauthClient() {
        return new JWTOAuthClient.JWTOAuthBuilder()
            .clientID(cozeProperties.getOauthClientId())
            .privateKey(cozeProperties.getOauthPrivateKey())
            .publicKey(cozeProperties.getOauthPublicKey())
            .baseURL(Consts.COZE_CN_BASE_URL)
            .build();
    }

}
