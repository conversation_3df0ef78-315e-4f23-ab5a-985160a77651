package com.dangbei.aisearch.common.util;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Callable;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-04-02
 */
@Slf4j
@UtilityClass
public class SafeExecuteUtil {

    public static void execute(Runnable runnable) {
        try {
            runnable.run();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    public static <T> T execute(Callable<T> callable) {
        try {
            return callable.call();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }
}
