package com.dangbei.aisearch.app.executor.query;

import com.alibaba.cola.dto.MultiResponse;
import com.dangbei.aisearch.common.constant.DcParamConst;
import com.dangbei.aisearch.domain.gateway.DcParamGateway;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-05-21
 */
@Component
public class UserDefaultAvatarListQueryExe {

    @Resource
    private DcParamGateway dcParamGateway;

    public MultiResponse<String> execute() {
        List<String> defaultAvatarList = dcParamGateway.listParamVal(DcParamConst.Type.DEFAULT_AVATAR, DcParamConst.ParamCode.LOGIN, String.class);
        return MultiResponse.of(defaultAvatarList);
    }
}
