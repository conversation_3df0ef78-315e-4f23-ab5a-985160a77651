package com.dangbei.aisearch.client.dto.clientobject;

import com.alibaba.cola.dto.DTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-17 20:58
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DocConfigCo<T> extends DTO {

    @Schema(description = "知识库配置")
    private List<LimitConfig<T>> knowledge;

    @Schema(description = "附件配置")
    private List<LimitConfig<T>> attachment;

    @Schema(description = "知识库存储类型,1:oss, 2:tos")
    private Integer knowledgeStorageType;

    @Schema(description = "附件存储类型,1:oss, 2:tos")
    private Integer attachmentStorageType;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LimitConfig<T> {
        private String type;
        private T size;
        private boolean img = false;
    }
}
