package com.dangbei.aisearch.app.service;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson2.JSONObject;
import com.dangbei.aisearch.app.executor.ProxyAIGCFetchCmdExe;
import com.dangbei.aisearch.app.executor.RtcAccessTokenCmdExe;
import com.dangbei.aisearch.app.executor.RtcCreateRoomCmdExe;
import com.dangbei.aisearch.client.dto.clientobject.RtcAccessTokenCo;
import com.dangbei.aisearch.client.dto.clientobject.RtcRoomCo;
import com.dangbei.aisearch.client.dto.cmd.ProxyAIGCFetchCmd;
import com.dangbei.aisearch.client.dto.cmd.RtcAccessTokenCmd;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * RTC代理服务
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-23
 */
@Service
public class RtcProxyService {

    @Resource
    private ProxyAIGCFetchCmdExe proxyAIGCFetchCmdExe;
    @Resource
    private RtcAccessTokenCmdExe rtcAccessTokenCmdExe;
    @Resource
    private RtcCreateRoomCmdExe rtcCreateRoomCmdExe;

    public SingleResponse<JSONObject> proxyAIGCFetch(ProxyAIGCFetchCmd cmd, String action, String version) {
        return proxyAIGCFetchCmdExe.execute(cmd, action, version);
    }

    public SingleResponse<RtcAccessTokenCo> accessToken(RtcAccessTokenCmd cmd) {
        return rtcAccessTokenCmdExe.execute(cmd);
    }

    public SingleResponse<RtcRoomCo> createRoom() {
        return rtcCreateRoomCmdExe.execute();
    }

}
