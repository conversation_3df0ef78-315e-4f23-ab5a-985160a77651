package com.dangbei.aisearch.domain.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.dangbei.aisearch.domain.common.base.BaseEntity;
import com.dangbei.aisearch.domain.common.base.BaseGateway;
import com.dangbei.aisearch.domain.gateway.QuickNoteGateway;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * QuickNote 领域对象
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-01-20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(doNotUseGetters = true)
public class QuickNoteEntity extends BaseEntity<Long> {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "速记编号")
    private String noteNo;

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "设备ID")
    private String deviceId;

    @Schema(description = "来源(self_write:自己写作;conversation:对话)")
    private String source;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "内容")
    private String content;

    @Schema(description = "会话ID")
    private String conversationId;

    @Schema(description = "消息ID列表")
    private List<String> msgIds;

    @Schema(description = "扩展字段")
    private String ext;

    private transient QuickNoteGateway quickNoteGateway = SpringUtil.getBean(QuickNoteGateway.class);

    @Override
    public Long getPrimaryId() {
        return this.id;
    }

    @Override
    public void setPrimaryId(Long id) {
        this.id = id;
    }

    @Override
    protected BaseGateway getGateWay() {
        return this.quickNoteGateway;
    }
}
