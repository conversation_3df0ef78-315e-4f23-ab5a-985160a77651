package com.dangbei.aisearch.infrastructure.gatewayimpl;

import com.dangbei.aisearch.domain.entity.I18nConfigEntity;
import com.dangbei.aisearch.domain.gateway.I18nConfigGateway;
import com.dangbei.aisearch.infrastructure.common.base.BaseGatewayImpl;
import com.dangbei.aisearch.infrastructure.convertor.I18nConfigConvertor;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.I18nConfigDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.I18nConfigMapper;
import org.springframework.stereotype.Component;

/**
 * I18nConfig 网关层实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-14
 */
@Component
public class I18nConfigGatewayImpl extends BaseGatewayImpl<Long, I18nConfigEntity, I18nConfigDO, I18nConfigMapper, I18nConfigConvertor> implements I18nConfigGateway {

}
