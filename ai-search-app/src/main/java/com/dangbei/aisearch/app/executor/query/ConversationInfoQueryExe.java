package com.dangbei.aisearch.app.executor.query;

import cn.dev33.satoken.exception.NotPermissionException;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.common.constant.StrPool;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.Assert;
import com.dangbei.aisearch.app.assembler.ConversationAssembler;
import com.dangbei.aisearch.app.util.UserDeviceUtil;
import com.dangbei.aisearch.client.dto.clientobject.ConversationCo;
import com.dangbei.aisearch.client.dto.cmd.ConversationStopAutoTtsCmd;
import com.dangbei.aisearch.client.dto.cmd.query.ConversationInfoQuery;
import com.dangbei.aisearch.common.constant.CacheKey;
import com.dangbei.aisearch.domain.entity.ConversationEntity;
import com.dangbei.aisearch.domain.gateway.ConversationGateway;
import com.dangbei.framework.insight.redis.util.RedisUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 会话查询命令执行器
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-26
 */
@Component
public class ConversationInfoQueryExe {

    @Resource
    private ConversationGateway conversationGateway;
    @Resource
    private ConversationAssembler conversationAssembler;

    public SingleResponse<ConversationCo> execute(ConversationInfoQuery query) {
        ConversationEntity conversationEntity = conversationGateway.getByConversationId(query.getConversationId());
        Assert.notNull(conversationEntity, "会话不存在");
        if (!UserDeviceUtil.isOwn(conversationEntity.getUserId(), conversationEntity.getDeviceId())) {
            throw new NotPermissionException("会话不匹配");
        }
        ConversationCo co = conversationAssembler.toCo(conversationEntity);
        return SingleResponse.of(co);
    }

    public Response stopAutoTts(ConversationStopAutoTtsCmd cmd) {
        if (StrUtil.isNotBlank(cmd.getWsId())) {
            RedisUtil.set(String.format(CacheKey.WS_SESSION_ID, cmd.getWsId()), StrPool.ZERO, 60 * 10);
        }
        return Response.buildSuccess();
    }

}
