package com.dangbei.aisearch.common.util;

import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @date 2025-03-19 14:28
 **/
public class SafeUtil {
    public static void runSilently(Runnable task) {
        try {
            task.run();
        } catch (Exception ignored) {}
    }

    public static void runSilently(Runnable task, Consumer<Exception> consumer) {
        try {
            task.run();
        } catch (Exception e) {
            consumer.accept(e);
        }
    }
}
