package com.dangbei.aisearch.app.executor;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.BizException;
import com.alibaba.dashscope.common.Message;
import com.alibaba.dashscope.common.Role;
import com.dangbei.aisearch.client.dto.cmd.AvatarDescriptionGenCmd;
import com.dangbei.aisearch.client.enums.AgentGenderEnum;
import com.dangbei.aisearch.client.enums.AgentRoleEnum;
import com.dangbei.aisearch.infrastructure.config.properties.DashScopeProperties;
import com.dangbei.aisearch.infrastructure.i18n.I18nUtil;
import com.dangbei.aisearch.infrastructure.prompt.PromptParam;
import com.dangbei.aisearch.infrastructure.prompt.PromptUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 生成头像描述
 * <AUTHOR>
 * @date 2025-03-26 17:07
 **/
@Slf4j
@Component
public class GenAvatarDescriptionCmdExe extends TextGenerationCmdExe {

    @Resource
    private DashScopeProperties dashScopeProperties;
    @Resource
    private PromptUtil promptUtil;


    public SingleResponse<String> execute(AvatarDescriptionGenCmd cmd) {
        List<Message> messages = new ArrayList<>();
        DashScopeProperties.ModelConfig modelConfig = dashScopeProperties.getGenAvatarDescriptionConfig();
        String promptTmpl = I18nUtil.isChinese() ? modelConfig.getSystem() : modelConfig.getEnSystem();

        Map<String, Object> extParam = Maps.newHashMap();
        extParam.put("role", AgentRoleEnum.getDesc(cmd.getAgentRole()));
        extParam.put("name", cmd.getAgentName());
        extParam.put("intro", cmd.getIntro());
        extParam.put("gender", AgentGenderEnum.getByValue(cmd.getGender()));

        String prompt = promptUtil.format(PromptParam.builder()
            .promptTmpl(promptTmpl)
            .extParam(extParam)
            .build());

        messages.add(Message.builder().role(Role.SYSTEM.getValue()).content(prompt).build());

        var agentAvatarDescription = execute(messages, modelConfig.getModel(), ex -> {
            log.error("生成头像描述异常：{}", ex.getMessage(), ex);
            throw new BizException("-1", "生成头像描述异常");
        });
        return SingleResponse.of(agentAvatarDescription);
    }
}
