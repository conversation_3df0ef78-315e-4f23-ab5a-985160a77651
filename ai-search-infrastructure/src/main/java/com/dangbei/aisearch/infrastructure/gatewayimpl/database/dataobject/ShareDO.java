package com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.dangbei.aisearch.infrastructure.common.base.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Share DO对象
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-06
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "ais_share", autoResultMap = true)
public class ShareDO extends BaseDO<Long> {

    @Schema(description = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "分享ID")
    @TableField(value = "share_id")
    private String shareId;

    @Schema(description = "用户ID")
    @TableField(value = "user_id")
    private String userId;

    @Schema(description = "设备ID")
    @TableField(value = "device_id")
    private String deviceId;

    @Schema(description = "会话ID")
    @TableField(value = "conversation_id")
    private String conversationId;

    @Schema(description = "消息ID列表")
    @TableField(value = "msg_ids", typeHandler = Fastjson2TypeHandler.class)
    private List<String> msgIds;

    @Schema(description = "智能体唯一ID")
    @TableField(value = "agent_id")
    private String agentId;

    @Schema(description = "当贝知识库ID")
    @TableField(value = "knowledge_id")
    private String knowledgeId;

    @Schema(description = "分享状态(1:正常;2:已停止分享)")
    @TableField(value = "share_status")
    private Integer shareStatus;

    @Schema(description = "点赞数")
    @TableField(value = "like_num")
    private Integer likeNum;

    @Schema(description = "过期时间")
    @TableField(value = "expire_time")
    private LocalDateTime expireTime;

    @Override
    public Long getPrimaryId() {
        return this.id;
    }

    @Override
    public void setPrimaryId(Long id) {
        this.id = id;
    }
}
