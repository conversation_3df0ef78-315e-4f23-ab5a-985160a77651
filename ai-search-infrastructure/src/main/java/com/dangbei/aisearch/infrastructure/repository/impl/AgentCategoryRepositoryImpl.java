package com.dangbei.aisearch.infrastructure.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.AgentCategoryDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.AgentCategoryMapper;
import com.dangbei.aisearch.infrastructure.repository.AgentCategoryRepository;
import org.springframework.stereotype.Repository;

/**
 * AgentCategory 仓库实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-28
 */
@Repository
public class AgentCategoryRepositoryImpl extends ServiceImpl<AgentCategoryMapper, AgentCategoryDO> implements AgentCategoryRepository {

}
