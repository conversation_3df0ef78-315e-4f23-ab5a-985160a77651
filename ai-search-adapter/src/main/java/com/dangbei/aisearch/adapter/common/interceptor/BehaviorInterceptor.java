package com.dangbei.aisearch.adapter.common.interceptor;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.cola.common.util.HttpUtil;
import com.dangbei.aisearch.app.util.UserDeviceUtil;
import com.dangbei.aisearch.client.dto.UserDeviceDTO;
import com.dangbei.aisearch.domain.entity.BehaviorEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.DispatcherType;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.Objects;
import java.util.concurrent.Executor;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-22
 */
@Slf4j
@Component
public class BehaviorInterceptor implements HandlerInterceptor {

    @Resource(name = "behaviorAsyncExecutor")
    private Executor behaviorAsyncExecutor;

    @Override
    public boolean preHandle(HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) {
        if (!Objects.equals(request.getDispatcherType(), DispatcherType.REQUEST)) {
            return true;
        }
        behaviorAsyncExecutor.execute(() -> {
            try {
                UserDeviceDTO userDeviceInfo = UserDeviceUtil.getUserDeviceInfo();
                BehaviorEntity entity = new BehaviorEntity();
                entity.setUserId(userDeviceInfo.getUserId());
                entity.setDeviceId(userDeviceInfo.getDeviceId());
                entity.setIp(HttpUtil.getIp(request));
                entity.setUri(request.getServletPath());
                entity.setPartitionKey(LocalDateTimeUtil.format(LocalDate.now(), "yyyyMMdd"));
                entity.save();
            } catch (Exception e) {
                log.warn("记录行为异常", e);
            }
        });
        return true;
    }
}
