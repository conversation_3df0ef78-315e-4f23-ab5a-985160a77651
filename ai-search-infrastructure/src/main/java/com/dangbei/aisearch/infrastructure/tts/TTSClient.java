package com.dangbei.aisearch.infrastructure.tts;

import com.dangbei.aisearch.infrastructure.tts.netty4.Connection;
import com.dangbei.aisearch.infrastructure.tts.netty4.ConnectionListener;
import com.dangbei.aisearch.infrastructure.tts.netty4.NettyWebSocketClient;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.net.URI;
import java.util.concurrent.TimeUnit;

/**
 * 语音处理client,全局维护一个实例
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-28
 */
@Slf4j
public class TTSClient {

    private static final String DEFAULT_SERVER_ADDR = "wss://openspeech.bytedance.com/api/v3/tts/bidirection";
    NettyWebSocketClient client;

    /**
     * -- SETTER --
     * 更新token.token有有效期,过了有效期需要设置新的token
     */
    @Setter
    String token;

    /**
     * -- SETTER --
     * 火山应用ID
     */
    @Setter
    String appId;

    /**
     * 连接建立默认重试次数和超时时间,单位毫秒
     */
    public static int connectMaxTryTimes = 2;
    public static int connectTimeout = 1500;
    public static boolean connectRetryEnable = false;

    //static {
    //    connectMaxTryTimes = Integer.parseInt(System.getProperty("tts.ws.connect.max_try_times", "3"));
    //    connectTimeout = Integer.parseInt(System.getProperty("tts.ws.connect.timeout", "3000"));
    //}

    public TTSClient() {
    }

    /**
     * 传入accessToken,访问阿里云线上服务
     * @param token accessToken
     */
    public TTSClient(String token, String appId) {
        try {
            this.token = token;
            this.appId = appId;
            client = new NettyWebSocketClient(DEFAULT_SERVER_ADDR);
        } catch (Exception e) {
            log.error("fail to create TTSClient", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 传入accessToken,根据传入url访问指定环境的服务
     * @param url   服务地址
     * @param token accessToken
     */
    public TTSClient(String url, String token, String appId) {
        try {
            this.token = token;
            this.appId = appId;
            client = new NettyWebSocketClient(url);
        } catch (Exception e) {
            log.error("fail to create TTSClient", e);
            throw new RuntimeException(e);
        }
    }

    public Connection connect(ConnectionListener listener) throws Exception {
        return this.connect(this.token, this.appId, listener);
    }

    public Connection connect(String accessToken, String appId, ConnectionListener listener) throws Exception {
        return this.connect(this.token, this.appId, listener, null);
    }

    public Connection connect(String accessToken, String appId, ConnectionListener listener, URI newUri) throws Exception {
        if (connectRetryEnable) {
            for (int i = 0; i < connectMaxTryTimes; i++) {
                try {
                    return client.connect(accessToken, appId, listener, connectTimeout, newUri);
                } catch (Exception e) {
                    if (i == 1) {
                        log.error("failed to connect to server after 2 tries,error msg is :{}", e.getMessage());
                        throw e;
                    }
                    Thread.sleep(100);
                    log.warn("failed to connect to server the {} time:{} ,try again ", i, e.getMessage());
                }
            }
        } else {
            return client.connect(accessToken, appId, listener, connectTimeout, newUri);
        }
        return null;
    }

    /**
     * 在应用的最后调用此方法,释放资源
     */
    public void shutdown() {
        client.shutdown();
    }

    /**
     * 在关闭前等待一段静默时间，确保处理中的请求能够正常完成
     */
    public void shutdown(long quietTime, TimeUnit unit) {
        client.shutdown(quietTime, unit);
    }

}
