package com.dangbei.aisearch.app.executor;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.dangbei.aisearch.client.dto.cmd.ProxyAIGCFetchCmd;
import com.dangbei.aisearch.common.constant.CacheKey;
import com.dangbei.aisearch.infrastructure.config.properties.VolcEngineProperties;
import com.dangbei.framework.insight.http.OkHttpUtil;
import com.dangbei.framework.insight.redis.util.RedisUtil;
import com.google.common.collect.Maps;
import com.volcengine.ApiClient;
import com.volcengine.ApiException;
import com.volcengine.auth.ISignerV4;
import com.volcengine.auth.impl.SignerV4Impl;
import com.volcengine.helper.Const;
import com.volcengine.model.RequestParam;
import com.volcengine.model.SignRequest;
import com.volcengine.sign.Credentials;
import com.volcengine.sts.StsApi;
import com.volcengine.sts.model.AssumeRoleRequest;
import com.volcengine.sts.model.AssumeRoleResponse;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicHeader;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * RTC Proxy代理服务
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-23
 */
@Slf4j
@Component
public class ProxyAIGCFetchCmdExe {

    @Resource
    private VolcEngineProperties volcEngineProperties;

    @SneakyThrows
    public SingleResponse<JSONObject> execute(ProxyAIGCFetchCmd cmd, String action, String version) {
        AssumeRoleResponse assumeResponse = assumeRole(volcEngineProperties.getRoleTrn());
        log.info("凭证信息={}", JSON.toJSONString(assumeResponse));

        String bodyJson = JSON.toJSONString(cmd);
        SignRequest signRequest = sign(assumeResponse, bodyJson, action, version);
        log.info("签名信息={}", JSON.toJSONString(signRequest));

        String callUrl = StrUtil.format("https://rtc.volcengineapi.com?Action={}&Version={}", action, version);
        Map<String, String> headerMap = Maps.newHashMap();
        headerMap.put(Const.Host, signRequest.getHost());
        headerMap.put(Const.XDate, signRequest.getXDate());
        headerMap.put(Const.XContentSha256, signRequest.getXContentSha256());
        headerMap.put(Const.XSecurityToken, signRequest.getXSecurityToken());
        headerMap.put(Const.Authorization, signRequest.getAuthorization());
        String respBody = OkHttpUtil.post(callUrl, headerMap, bodyJson);
        log.info("action接口响应={}", respBody);
        return SingleResponse.of(JSON.parseObject(respBody));
    }

    @SneakyThrows
    private SignRequest sign(AssumeRoleResponse assumeResponse, String body, String action, String version) {
        ISignerV4 signerV4 = new SignerV4Impl();
        List<NameValuePair> queryList = new ArrayList<>();
        queryList.add(new BasicHeader("Action", action));
        queryList.add(new BasicHeader("Version", version));

        RequestParam requestParam = RequestParam.builder()
            .isSignUrl(false)
            .body(body.getBytes(StandardCharsets.UTF_8))
            .method("POST")
            .date(new Date())
            .path("/")
            .host("rtc.volcengineapi.com")
            .queryList(queryList)
            .headers(new Header[]{})
            .build();

        com.volcengine.model.Credentials credentials = new com.volcengine.model.Credentials();
        credentials.setAccessKeyID(assumeResponse.getCredentials().getAccessKeyId());
        credentials.setSecretAccessKey(assumeResponse.getCredentials().getSecretAccessKey());
        credentials.setSessionToken(assumeResponse.getCredentials().getSessionToken());
        credentials.setRegion(assumeResponse.getResponseMetadata().getRegion());
        credentials.setService("rtc");
        return signerV4.getSignRequest(requestParam, credentials);
    }

    private AssumeRoleResponse assumeRole(String roleTrn) {
        String key = StrUtil.format(CacheKey.CredentialsCache, StrUtil.subAfter(roleTrn, StringPool.SLASH, true));
        Object credentialsVal = RedisUtil.get(key);
        if (Objects.nonNull(credentialsVal)) {
            return JSON.parseObject(Convert.toStr(credentialsVal), AssumeRoleResponse.class);
        }

        ApiClient apiClient = new ApiClient()
            .setCredentials(Credentials.getCredentials(volcEngineProperties.getAccessKey(), volcEngineProperties.getSecretKey()))
            .setRegion(volcEngineProperties.getRegion());

        StsApi api = new StsApi(apiClient);
        AssumeRoleRequest assumeRoleRequest = new AssumeRoleRequest();
        assumeRoleRequest.setDurationSeconds(43200);
        assumeRoleRequest.setRoleSessionName(volcEngineProperties.getRoleSessionName());
        assumeRoleRequest.setRoleTrn(roleTrn);

        try {
            // 复制代码运行示例，请自行打印API返回值。
            AssumeRoleResponse assumeRoleResponse = api.assumeRole(assumeRoleRequest);
            long delayTime = (diffSeconds(assumeRoleResponse.getCredentials().getExpiredTime()) - 60);
            log.info("凭证缓存redis延迟时间={}", delayTime);
            RedisUtil.set(key, JSON.toJSONString(assumeRoleResponse), delayTime);
            return assumeRoleResponse;
        } catch (ApiException e) {
            log.error(e.getResponseBody(), e);
            throw new BizException("凭证异常，请检查!");
        }
    }

    private Long diffSeconds(String dateTimeString) {
        // 解析时间字符串
        ZonedDateTime inputTime = ZonedDateTime.parse(dateTimeString, DateTimeFormatter.ISO_OFFSET_DATE_TIME);
        // 获取当前时间
        ZonedDateTime currentTime = ZonedDateTime.now();
        // 计算时间差（秒数）
        return Duration.between(inputTime, currentTime).abs().getSeconds();
    }

}
