package com.dangbei.aisearch.infrastructure.common.helper;

import cn.hutool.core.collection.CollUtil;
import com.dangbei.aisearch.infrastructure.config.properties.AttachmentDocProperties;
import com.dangbei.aisearch.infrastructure.config.properties.KnowledgeDocProperties;
import com.dangbei.aisearch.infrastructure.config.properties.StorageDirProperties;
import com.dangbei.aisearch.infrastructure.config.properties.common.DocConfig;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 文件类型配置帮助类
 *  - 知识库文件
 *  - 附件文件
 *
 * <AUTHOR>
 * @date 2025-04-24 14:46
 **/
@Component
public class DocConfigHelper {

    @Resource
    private KnowledgeDocProperties knowledgeDocProperties;
    @Resource
    private AttachmentDocProperties attachmentDocProperties;
    @Resource
    private StorageDirProperties storageDirProperties;

    /**
     * 是否是知识库支持的类型
     * @param type 文件类型（后缀）
     * @return boolean true=支持, false=不支持
     */
    public boolean isKnowledgeSupportType(String type) {
        if (StringUtils.isBlank(type)) {
            return false;
        }
        if (CollUtil.isEmpty(knowledgeDocProperties.getDocConfig())) {
            return false;
        }
        return knowledgeDocProperties.getDocConfig().stream().anyMatch(docConfig -> docConfig.getType().equals(type));
    }

    /**
     * 是否是知识库支持的路径前缀
     * @param path 文件路径前缀
     * @return boolean true=支持, false=不支持
     */
    public boolean isKnowledgeSupportPathPrefix(String path) {
        if (StringUtils.isBlank(path)) {
            return false;
        }
        if (StringUtils.isBlank(storageDirProperties.getFilePrefix())) {
            return true;
        }
        return path.startsWith(storageDirProperties.getFilePrefix()) || path.startsWith("/" + storageDirProperties.getFilePrefix());
    }

    /**
     * 是否是文件类型的附件支持的路径前缀
     * @param path 文件路径前缀
     * @return boolean true=支持, false=不支持
     */
    public boolean isAttachmentFileSupportPathPrefix(String path) {
        if (StringUtils.isBlank(path)) {
            return false;
        }
        if (StringUtils.isBlank(storageDirProperties.getFilePrefix())) {
            return true;
        }
        return path.startsWith(storageDirProperties.getFilePrefix()) || path.startsWith("/" + storageDirProperties.getFilePrefix());
    }

    /**
     * 是否是附件支持的类型
     * @param type 文件类型（后缀）
     * @return boolean true=支持, false=不支持
     */
    public boolean isAttachmentSupportType(String type) {
        if (StringUtils.isBlank(type)) {
            return false;
        }
        if (CollUtil.isEmpty(attachmentDocProperties.getDocConfig())) {
            return false;
        }
        return attachmentDocProperties.getDocConfig().stream().anyMatch(docConfig -> docConfig.getType().equals(type));
    }

    /**
     * 是否是附件支持的图片类型
     * @param type 文件类型（后缀）
     * @return boolean true=支持, false=不支持
     */
    public boolean isAttachmentSupportImageType(String type) {
        if (StringUtils.isBlank(type)) {
            return false;
        }
        if (CollUtil.isEmpty(attachmentDocProperties.getDocConfig())) {
            return false;
        }
        return attachmentDocProperties.getDocConfig().stream()
            .filter(DocConfig::isImg)
            .anyMatch(docConfig -> docConfig.getType().equals(type));
    }


    /**
     * 是否是附件支持的非图片类型
     * @param type 文件类型（后缀）
     * @return boolean true=支持, false=不支持
     */
    public boolean isAttachmentSupportUnImageType(String type) {
        return isAttachmentSupportType(type) && !isAttachmentSupportImageType(type);
    }



}
