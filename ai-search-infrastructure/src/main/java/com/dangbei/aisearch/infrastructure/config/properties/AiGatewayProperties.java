package com.dangbei.aisearch.infrastructure.config.properties;

import com.alibaba.cola.dto.DTO;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Component;

/**
 * AI网关配置
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-01-02
 */
@Data
@Component
@NacosConfigurationProperties(dataId = "${nacos.config.data-id:com.dangbei.ai-search.application.yaml}",
    groupId = "${nacos.config.group:ai-search}",
    prefix = "ai-gateway",
    autoRefreshed = true)
public class AiGatewayProperties {

    /**
     * 开关
     */
    private boolean enable = true;

    /**
     * API Key
     */
    private String baseUrl = "http://ai-gateway-test.dangbei.net";

    /**
     * API Key
     */
    private String apiKey = "LTAI5t7dU6589pVarBrLdBjL";

    /**
     * 最大连接数
     */
    private Integer maxIdleConnections = 30;

    /**
     * 超时时间
     */
    private Integer keepAliveMinute = 10;

    /**
     * 聊天参数配置
     */
    private ChatOptions chatOptions = new ChatOptions();

    /**
     * 内置OkHttp配置
     */
    private InnerOkHttpConfig innerOkHttpConfig = new InnerOkHttpConfig();

    @Data
    public static class InnerOkHttpConfig extends DTO {

        /**
         * 调度器配置
         */
        private DispatcherConfig dispatcher = new DispatcherConfig();

        /**
         * http连接池配置
         */
        private ConnectionPoolConfig connectionPool = new ConnectionPoolConfig();

    }

    /**
     * 调度器配置
     */
    @Data
    public static class DispatcherConfig extends DTO {
        /**
         * 最大并发请求总数
         */
        private int maxRequests = 200;

        /**
         * 每个主机的最大并发请求数
         */
        private int maxRequestsPerHost = 200;

        /**
         * 核心线程数
         */
        private int corePoolSize = 100;

        /**
         * 最大线程数
         */
        private int maximumPoolSize = 300;

        /**
         * 空闲线程存活时间
         */
        private long keepAliveTime = 600L;

        /**
         * 阻塞队列长度
         */
        private int workQueueSize = 0;
    }

    /**
     * http连接池配置
     */
    @Data
    public static class ConnectionPoolConfig extends DTO {

        /**
         * 最大连接数
         */
        private Integer maxIdleConnections = 30;

        /**
         * 超时时间
         */
        private Integer keepAliveMinute = 10;

    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class ChatOptions extends DTO {

        @Schema(description = "最大token数")
        private Integer maxTokens = 8000;

        @Schema(description = "温度")
        private Double temperature;

        @Schema(description = "topP")
        private Double topP;

    }

}
