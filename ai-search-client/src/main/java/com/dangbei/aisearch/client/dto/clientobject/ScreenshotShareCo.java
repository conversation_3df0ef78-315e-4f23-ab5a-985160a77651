package com.dangbei.aisearch.client.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-12
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ScreenshotShareCo extends ClientObject {

    @Schema(description = "分享ID")
    private String shareId;
}
