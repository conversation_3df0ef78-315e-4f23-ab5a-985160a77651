package com.dangbei.aisearch.client.dto.cmd;

import com.alibaba.cola.dto.Command;
import com.dangbei.aisearch.client.enums.SharedKnowledgeRoleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 修改成员角色命令
 * <AUTHOR>
 * @date 2025-05-28
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class UpdateMemberRoleCmd extends Command {

    @NotBlank(message = "知识库ID不能为空")
    @Schema(description = "知识库ID")
    private String knowledgeId;

    @NotBlank(message = "用户ID不能为空")
    @Schema(description = "用户ID")
    private String userId;

    /**
     * {@link SharedKnowledgeRoleEnum}
     */
    @NotNull(message = "新角色不能为空")
    @Schema(description = "新角色：2-管理员，3-成员")
    private Integer newRole;
}
