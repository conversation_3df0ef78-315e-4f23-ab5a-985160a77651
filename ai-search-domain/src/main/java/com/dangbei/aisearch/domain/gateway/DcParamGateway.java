package com.dangbei.aisearch.domain.gateway;

import com.dangbei.aisearch.client.dto.clientobject.DcParamCo;
import com.dangbei.aisearch.domain.common.base.BaseGateway;
import com.dangbei.aisearch.domain.entity.DcParamEntity;

import java.util.List;

/**
 * DcParam 网关层
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-01-11
 */
public interface DcParamGateway extends BaseGateway<Long, DcParamEntity> {

    /**
     * 根据配置类型和配置值获取配置
     * @param type      配置类型
     * @param paramCode 配置值
     * @return 配置列表
     */
    DcParamCo getByTypeAndParamCode(String type, String paramCode);

    /**
     * 根据配置类型获取配置列表
     * @param type 配置类型
     * @return 配置列表
     */
    List<DcParamCo> listByType(String type);

    /**
     * 根据配置类型集合获取配置列表
     * @param types 配置类型集合
     * @return 配置列表
     */
    List<DcParamCo> listByTypes(List<String> types);

    /**
     * 根据配置类型和配置值获取配置值
     * @param type      配置类型
     * @param paramCode 配置值
     * @return 配置值对象
     */
    <ParamValClass> ParamValClass getParamVal(String type, String paramCode, Class<ParamValClass> paramValClass);

    /**
     * 根据配置类型和配置值获取配置值
     * @param type      配置类型
     * @param paramCode 配置值
     * @return 配置值对象列表
     */
    <ParamValClass> List<ParamValClass> listParamVal(String type, String paramCode, Class<ParamValClass> paramValClass);
}
