package com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dangbei.aisearch.infrastructure.common.base.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * I18nConfig DO对象
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-14
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("ais_i18n_config")
public class I18nConfigDO extends BaseDO<Long> {

    @Schema(description = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "属性名")
    @TableField(value = "code")
    private String code;

    @Schema(description = "国家代码")
    @TableField(value = "locale")
    private String locale;

    @Schema(description = "对应内容")
    @TableField(value = "message")
    private String message;

    @Override
    public Long getPrimaryId() {
        return this.id;
    }

    @Override
    public void setPrimaryId(Long id) {
        this.id = id;
    }
}
