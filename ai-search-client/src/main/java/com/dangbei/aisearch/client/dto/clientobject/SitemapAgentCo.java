package com.dangbei.aisearch.client.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-04-08
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SitemapAgentCo extends ClientObject {

    @Schema(description = "智能体唯一ID")
    private String agentId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}
