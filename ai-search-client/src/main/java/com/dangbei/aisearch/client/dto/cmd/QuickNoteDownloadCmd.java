package com.dangbei.aisearch.client.dto.cmd;

import com.alibaba.cola.dto.Command;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-27
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QuickNoteDownloadCmd extends Command {

    @NotBlank(message = "速记编号不能为空")
    @Schema(description = "速记编号")
    private String noteNo;
}
