package com.dangbei.aisearch.infrastructure.gatewayimpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dangbei.aisearch.domain.entity.AgentVersionEntity;
import com.dangbei.aisearch.domain.gateway.AgentVersionGateway;
import com.dangbei.aisearch.infrastructure.common.base.BaseGatewayImpl;
import com.dangbei.aisearch.infrastructure.convertor.AgentVersionConvertor;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.AgentVersionDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.AgentVersionMapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * AgentVersion 网关层实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-26
 */
@Component
public class AgentVersionGatewayImpl extends BaseGatewayImpl<Long, AgentVersionEntity, AgentVersionDO, AgentVersionMapper, AgentVersionConvertor> implements AgentVersionGateway {

    @Override
    public AgentVersionEntity getLatestVersionByAgentId(String agentId) {
        LambdaQueryWrapper<AgentVersionDO> queryWrapper = Wrappers.lambdaQuery(AgentVersionDO.class)
            .eq(AgentVersionDO::getAgentId, agentId)
            .orderByDesc(AgentVersionDO::getId)
            .last("limit 1");
        return convertor.toEntity(baseMapper.selectOne(queryWrapper));
    }

    @Override
    public List<AgentVersionEntity> getAllVersionByAgentId(String agentId) {
        LambdaQueryWrapper<AgentVersionDO> queryWrapper = Wrappers.lambdaQuery(AgentVersionDO.class)
            .eq(AgentVersionDO::getAgentId, agentId)
            .orderByDesc(AgentVersionDO::getCreateTime);
        return convertor.toEntityList(baseMapper.selectList(queryWrapper));
    }
}
