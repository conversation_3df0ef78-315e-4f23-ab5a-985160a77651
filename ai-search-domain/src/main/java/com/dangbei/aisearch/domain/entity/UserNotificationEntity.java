package com.dangbei.aisearch.domain.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.dangbei.aisearch.client.dto.NotificationJumpConfig;
import com.dangbei.aisearch.domain.common.base.BaseEntity;
import com.dangbei.aisearch.domain.gateway.UserNotificationGateway;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * UserNotification 领域对象
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-05-27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(doNotUseGetters = true)
public class UserNotificationEntity extends BaseEntity<Long> {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "类型(1-普通文本,2-审批)")
    private Integer type;

    @Schema(description = "业务类型(1-知识库加入申请,2-知识库申请结果)")
    private Integer bizType;

    @Schema(description = "发送用户ID")
    private String senderId;

    @Schema(description = "接收用户ID")
    private String userId;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "内容")
    private String content;

    @Schema(description = "关联ID")
    private String relatedId;

    @Schema(description = "跳转配置JSON")
    private NotificationJumpConfig jumpConfig;

    @Schema(description = "是否已读(0-未读,1-已读)")
    private Integer isRead;

    @Schema(description = "阅读时间")
    private LocalDateTime readTime;

    private transient UserNotificationGateway userNotificationGateway = SpringUtil.getBean(UserNotificationGateway.class);

    @Override
    public Long getPrimaryId() {
        return this.id;
    }

    @Override
    public void setPrimaryId(Long id) {
        this.id = id;
    }

    @Override
    protected UserNotificationGateway getGateWay() {
        return this.userNotificationGateway;
    }
}
