package com.dangbei.aisearch.infrastructure.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.KnowledgeApplyDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.KnowledgeApplyMapper;
import com.dangbei.aisearch.infrastructure.repository.KnowledgeApplyRepository;
import org.springframework.stereotype.Repository;

/**
 * KnowledgeApply 仓库实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-05-27
 */
@Repository
public class KnowledgeApplyRepositoryImpl extends ServiceImpl<KnowledgeApplyMapper, KnowledgeApplyDO> implements KnowledgeApplyRepository {

}
