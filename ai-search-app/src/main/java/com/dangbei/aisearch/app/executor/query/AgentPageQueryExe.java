package com.dangbei.aisearch.app.executor.query;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.PageSingleResponse;
import com.alibaba.cola.exception.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dangbei.aisearch.app.assembler.AgentAssembler;
import com.dangbei.aisearch.client.constant.Const;
import com.dangbei.aisearch.client.dto.clientobject.AgentCo;
import com.dangbei.aisearch.client.dto.clientobject.AgentListCo;
import com.dangbei.aisearch.client.dto.cmd.query.AgentPageQuery;
import com.dangbei.aisearch.infrastructure.common.util.PageUtil;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.AgentDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.AgentMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-02-28 15:12
 **/
@Component
public class AgentPageQueryExe extends AgentExe {

    @Resource
    private AgentMapper agentMapper;
    @Resource
    private AgentAssembler agentAssembler;

    public PageSingleResponse<AgentListCo> execute(AgentPageQuery pageQuery) {
        Long defaultAllCategoryId = Const.AgentCategory.DEFAULT_ALL_CATEGORY_ID;
        if (Objects.isNull(pageQuery.getCategoryId())) {
            pageQuery.setCategoryId(defaultAllCategoryId);
        }
        Page<AgentDO> page = Page.of(pageQuery.getPageIndex(), pageQuery.getPageSize());
        Page<AgentDO> agentDOPage;
        if (defaultAllCategoryId.equals(pageQuery.getCategoryId())) {
            agentDOPage = agentMapper.getAllSortByUsageCount(page, pageQuery);
        } else {
            Assert.notNull(pageQuery.getCategoryId(), "分类Id不能为空");
            agentDOPage = agentMapper.pageQueryAgentsByCategory(page, pageQuery);
        }

        List<String> agentIds = getAgentIds(agentDOPage);
        // agentId-->conversationId
        Map<String, String> agentIdConversationIdMap = getAgentConversation(agentIds);
        return PageUtil.convertToPageSingleResponse(agentDOPage, agentDO -> {
            var co = agentAssembler.toListCo(agentDO);
            // 如果智能体已经创建了会话了，需要填充 conversationId，用于前端直接跳转到会话页面
            co.setConversationId(agentIdConversationIdMap.get(agentDO.getAgentId()));
            co.setHasAppBgUrl(StrUtil.isNotBlank(agentDO.getAppBgUrl()));
            return co;
        });
    }

    public List<AgentCo> execute(List<String> agentIds) {
        if (CollUtil.isEmpty(agentIds)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<AgentDO> wrapper = Wrappers.lambdaQuery();
        wrapper.in(AgentDO::getAgentId, agentIds);
        List<AgentDO> list = agentMapper.selectList(wrapper);
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }

        Map<String, String> agent2Conversation = getAgentConversation(list.stream().map(AgentDO::getAgentId).toList());
        return list.stream().map(dataObj -> {
            var co = agentAssembler.toCo(dataObj);
            // 如果智能体已经创建了会话了，需要填充 conversationId，用于前端直接跳转到会话页面
            co.setConversationId(agent2Conversation.get(dataObj.getAgentId()));
            co.setHasAppBgUrl(StrUtil.isNotBlank(dataObj.getAppBgUrl()));
            return co;
        }).collect(Collectors.toList());
    }
}
