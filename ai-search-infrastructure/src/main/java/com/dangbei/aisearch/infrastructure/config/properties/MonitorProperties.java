package com.dangbei.aisearch.infrastructure.config.properties;

import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * 监控告警配置
 * <AUTHOR> href="mailto:yinyanta<PERSON>@dangbei.com"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-04
 */
@Data
@Component
@NacosConfigurationProperties(dataId = "${nacos.config.data-id:com.dangbei.ai-search.application.yaml}",
    groupId = "${nacos.config.group:ai-search}",
    prefix = "monitor-warning",
    autoRefreshed = true)
public class MonitorProperties {

    /**
     * 是否开启TTFT告警（time to first token）
     */
    private boolean ttftWarnEnabled = false;

    /**
     * 监控 TTFT 最大超时时间（毫秒）
     */
    private long ttftMaxMs = 2000;

}
