package com.dangbei.aisearch.app.executor.query;

import com.alibaba.cola.dto.MultiResponse;
import com.dangbei.aisearch.app.util.UserContextUtil;
import com.dangbei.aisearch.client.dto.clientobject.SharedKnowledgeDocInfoCo;
import com.dangbei.aisearch.client.dto.cmd.query.SharedKnowledgeDocInfosQuery;
import com.dangbei.aisearch.client.enums.SharedKnowledgePermissionEnum;
import com.dangbei.aisearch.common.util.TimeUtil;
import com.dangbei.aisearch.domain.entity.SharedKnowledgeDocEntity;
import com.dangbei.aisearch.domain.gateway.SharedKnowledgeDocGateway;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 共享知识库文档信息批量查询执行器
 * <AUTHOR>
 * @date 2025-05-28
 **/
@Slf4j
@Component
public class SharedKnowledgeDocInfosQueryExe {

    @Resource
    private SharedKnowledgeDocGateway sharedKnowledgeDocGateway;

    public MultiResponse<SharedKnowledgeDocInfoCo> execute(SharedKnowledgeDocInfosQuery query) {
        if (CollectionUtils.isEmpty(query.getDocIds())) {
            return MultiResponse.of(new ArrayList<>());
        }
        String currentUserId = UserContextUtil.getNonNullUserId();
        // 批量查询文档实体
        List<SharedKnowledgeDocEntity> docEntities = sharedKnowledgeDocGateway.listByDocIds(query.getDocIds());
        if (CollectionUtils.isEmpty(docEntities)) {
            return MultiResponse.of(new ArrayList<>());
        }
        // 转换为CO对象
        List<SharedKnowledgeDocInfoCo> docInfos = new ArrayList<>();
        for (SharedKnowledgeDocEntity docEntity : docEntities) {
            // 权限校验 - 检查是否有查看权限
            if (!docEntity.getKnowledgeEntity().canVisit(currentUserId)) {
                continue;
            }
            SharedKnowledgeDocInfoCo docInfo = convertToDocInfoCo(docEntity, currentUserId);
            docInfos.add(docInfo);
        }

        return MultiResponse.of(docInfos);
    }

    private SharedKnowledgeDocInfoCo convertToDocInfoCo(SharedKnowledgeDocEntity docEntity, String currentUserId) {
        return SharedKnowledgeDocInfoCo.builder()
            .docId(docEntity.getDocId())
            .docName(docEntity.getDocName())
            .docType(docEntity.getDocType())
            .filePath(docEntity.getDocPath())
            .docSize(docEntity.getDocSize())
            .wordNum(docEntity.getWordNum())
            .processStatus(docEntity.getProcessStatus())
            .createTime(TimeUtil.getTimestamp(docEntity.getCreateTime()))
            .summary(docEntity.getSummary())
            .canDelete(docEntity.canDelete(currentUserId))
            .canRename(docEntity.checkOperationPermission(currentUserId, SharedKnowledgePermissionEnum.RENAME_DOC))
            .canDownload(docEntity.canDownload(currentUserId))
            .build();
    }
}
