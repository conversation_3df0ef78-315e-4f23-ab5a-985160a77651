package com.dangbei.aisearch.app.executor;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.exception.Assert;
import com.dangbei.aisearch.app.util.UserDeviceUtil;
import com.dangbei.aisearch.client.dto.cmd.ConversationUpdateCmd;
import com.dangbei.aisearch.domain.entity.ConversationEntity;
import com.dangbei.aisearch.domain.gateway.ConversationGateway;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 会话编辑命令执行器
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-25
 */
@Component
public class ConversationUpdateCmdExe {

    @Resource
    private ConversationGateway conversationGateway;

    public Response execute(ConversationUpdateCmd cmd) {
        ConversationEntity conversationEntity = conversationGateway.getByConversationId(cmd.getConversationId());
        Assert.notNull(conversationEntity, "会话不存在");
        UserDeviceUtil.checkUserDevice(conversationEntity.getUserId(), conversationEntity.getDeviceId());
        ConversationEntity entity = new ConversationEntity();
        entity.setId(conversationEntity.getId());
        entity.setTitle(cmd.getTitle());
        entity.update();
        return Response.buildSuccess();
    }
}
