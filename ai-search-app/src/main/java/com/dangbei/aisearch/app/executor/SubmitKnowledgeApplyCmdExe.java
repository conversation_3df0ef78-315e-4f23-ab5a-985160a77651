package com.dangbei.aisearch.app.executor;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.exception.Assert;
import com.dangbei.aisearch.app.util.UserContextUtil;
import com.dangbei.aisearch.client.dto.NotificationJumpConfig;
import com.dangbei.aisearch.client.dto.cmd.SubmitKnowledgeApplyCmd;
import com.dangbei.aisearch.client.enums.NotificationBizTypeEnum;
import com.dangbei.aisearch.domain.entity.KnowledgeApplyEntity;
import com.dangbei.aisearch.domain.entity.SharedKnowledgeEntity;
import com.dangbei.aisearch.domain.gateway.KnowledgeMemberGateway;
import com.dangbei.aisearch.domain.gateway.SharedKnowledgeGateway;
import com.dangbei.aisearch.domain.gateway.UserNotificationGateway;
import com.dangbei.aisearch.infrastructure.config.properties.NotificationProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.util.Set;

/**
 * 提交知识库申请执行器
 * <AUTHOR>
 * @date 2025-05-27
 **/
@Slf4j
@Component
public class SubmitKnowledgeApplyCmdExe {

    @Resource
    private SharedKnowledgeGateway sharedKnowledgeGateway;
    @Resource
    private KnowledgeMemberGateway knowledgeMemberGateway;
    @Resource
    private UserNotificationGateway userNotificationGateway;
    @Resource
    private NotificationProperties notificationProperties;

    /**
     * 提交知识库申请
     * @param cmd 申请命令
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Response execute(SubmitKnowledgeApplyCmd cmd) {
        // 获取当前用户
        String currUserId = UserContextUtil.getNonNullUserId();
        // 获取知识库
        SharedKnowledgeEntity knowledge = sharedKnowledgeGateway.getByKnowledgeId(cmd.getKnowledgeId());
        Assert.notNull(knowledge, "知识库不存在");
        // 创建申请
        KnowledgeApplyEntity applyEntity = new KnowledgeApplyEntity();
        applyEntity.setKnowledgeId(cmd.getKnowledgeId());
        applyEntity.initializeApply(cmd.getKnowledgeId(), currUserId);
        applyEntity.save();

        boolean transactionActive = TransactionSynchronizationManager.isActualTransactionActive();
        if (transactionActive) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    // 获取知识库的管理员和创建者
                    Set<String> userIds = knowledgeMemberGateway.listCreatorAdminUserIdsExcludeCurrUserId(cmd.getKnowledgeId(), currUserId);
                    String applyId = applyEntity.getApplyId();
                    String knowledgeName = knowledge.getName();
                    // 发送通知
                    userNotificationGateway.sendApprovalBatch(
                        NotificationBizTypeEnum.KNOWLEDGE_APPLY,
                        currUserId,
                        userIds,
                        notificationProperties.getKnowledge().getApplyText(knowledgeName),
                        applyId,
                        new NotificationJumpConfig(notificationProperties.getKnowledge().getApplyJumpConfig(), knowledge.getKnowledgeId()));
                }
            });
        }
        return Response.buildSuccess();
    }
}
