package com.dangbei.aisearch.domain.common.base;

import com.alibaba.cola.common.lazy.PropertyLoader;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * 实体基类
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/05/29
 */
@Data
public abstract class BaseEntity<I extends Serializable> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 创建人
     */
    private String createPerson;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private String updatePerson;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 逻辑删除字段(0：未删除；主键：已删除)
     */
    private I isDeleted;

    /**
     * 判断是否为数据库中加载的,延迟加载只对数据库中的对象有效，对新增对象不生效
     */
    private Boolean loadFlag = false;

    /**
     * 记录已经加载的字段
     */
    private Set<String> loadSet = new HashSet<>();

    /**
     * 以 OrderEntity 为例 orderItemList 字段为例,当查询一个 Order 实体时，数据库只返回 Order 相关的数据，并不会进行加载 orderItemList
     * 延迟加载的字段需要显式调用，不应该由 BeanUtils.copy 自动调用或者 mapstruct 自动调用,否则可能触发重复加载
     * 使用 loadSet 来记录已经加载的字段，避免重复加载
     */
    protected <T> T lazyLoad(String property, PropertyLoader<T> propertyLoader) {
        // 延迟加载只对数据库中的对象有效
        if (loadFlag && !loadSet.contains(property)) {
            propertyLoader.load();
            loadSet.add(property);
        }
        return propertyLoader.get();
    }

    /**
     * 获取主键
     * @return {@link I }
     */
    public abstract I getPrimaryId();

    /**
     * 设置主键
     * @param id id
     */
    public abstract void setPrimaryId(I id);

    /**
     * 返回领域对象对应的网关服务
     * @return 网关服务
     */
    @SuppressWarnings("rawtypes")
    protected abstract BaseGateway getGateWay();

    /**
     * 领域对象新增
     */
    @SuppressWarnings("unchecked")
    public I save() {
        // 1. 自身保存
        I id = (I) getGateWay().insert(this);
        // 2 设置主键id
        setPrimaryId(id);
        // 3. 保存子成员
        saveRela();
        return id;
    }

    /**
     * 保存子成员，对子成员分别调用 save
     */
    protected void saveRela() {
    }

    /**
     * 领域对象的更新
     */
    @SuppressWarnings("unchecked")
    public void update() {
        getGateWay().update(this);
        updateRela();
    }

    /**
     * 更新子成员，对子成员分别调用 update
     */
    protected void updateRela() {
    }

    /**
     * 保存或更新领域对象
     */
    public I saveOrUpdate() {
        if (getPrimaryId() == null) {
            return save();
        } else {
            update();
            return getPrimaryId();
        }
    }

    /**
     * 领域对象的删除
     */
    @SuppressWarnings("unchecked")
    public void delete() {
        getGateWay().deleteById(getPrimaryId());
    }
}
