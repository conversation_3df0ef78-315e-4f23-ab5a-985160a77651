package com.dangbei.aisearch.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-20
 */
@Getter
@AllArgsConstructor
public enum ShareSourceEnum {

    CONVERSATION("", "会话"),

    SCREENSHOT("screenshot_", "小旺截图"),

    AGENT("agent_", "智能体"),

    COMMUNITY_POST("post_", "广场帖子"),

    RECOMMEND_CHAT("rec_", "推荐对话"),

    QUICK("quick_", "桌面端快捷对话分享"),

    KNOWLEDGE("know_", "知识库"),
    ;

    private final String prefix;

    private final String desc;

    public static boolean isConversation(String shareId) {
        return !StringUtils.contains(shareId, "_");
    }
}
