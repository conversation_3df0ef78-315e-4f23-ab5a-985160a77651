package com.dangbei.aisearch.app.service;

import com.alibaba.cola.dto.PageSingleResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.dangbei.aisearch.app.executor.QuickNoteAddCmdExe;
import com.dangbei.aisearch.app.executor.QuickNoteDeleteCmdExe;
import com.dangbei.aisearch.app.executor.QuickNoteDownloadPdfCmdExe;
import com.dangbei.aisearch.app.executor.QuickNoteDownloadWordCmdExe;
import com.dangbei.aisearch.app.executor.QuickNoteUpdateCmdExe;
import com.dangbei.aisearch.app.executor.query.QuickNoteGetByNoteNoQueryExe;
import com.dangbei.aisearch.app.executor.query.QuickNotePageQueryExe;
import com.dangbei.aisearch.client.dto.clientobject.QuickNoteCo;
import com.dangbei.aisearch.client.dto.clientobject.QuickNoteListCo;
import com.dangbei.aisearch.client.dto.cmd.QuickNoteAddCmd;
import com.dangbei.aisearch.client.dto.cmd.QuickNoteUpdateCmd;
import com.dangbei.aisearch.client.dto.cmd.query.QuickNotePageQuery;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * QuickNote 服务实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-01-07
 */
@Service
public class QuickNoteService {

    @Resource
    private QuickNoteAddCmdExe quickNoteAddCmdExe;
    @Resource
    private QuickNoteUpdateCmdExe quickNoteUpdateCmdExe;
    @Resource
    private QuickNoteGetByNoteNoQueryExe quickNoteGetByNoteNoQueryExe;
    @Resource
    private QuickNotePageQueryExe quickNotePageQueryExe;
    @Resource
    private QuickNoteDeleteCmdExe quickNoteDeleteCmdExe;
    @Resource
    private QuickNoteDownloadPdfCmdExe quickNoteDownloadPdfCmdExe;
    @Resource
    private QuickNoteDownloadWordCmdExe quickNoteDownloadWordCmdExe;

    /**
     * 创建速记
     */
    public SingleResponse<QuickNoteCo> create(QuickNoteAddCmd addCmd) {
        return quickNoteAddCmdExe.execute(addCmd);
    }

    /**
     * 更新速记
     */
    public Response update(QuickNoteUpdateCmd updateCmd) {
        return quickNoteUpdateCmdExe.execute(updateCmd);
    }

    /**
     * 根据速记编号查询
     */
    public SingleResponse<QuickNoteCo> getByNoteNo(String noteNo) {
        return quickNoteGetByNoteNoQueryExe.execute(noteNo);
    }

    /**
     * 分页查询速记列表
     * @param pageQuery 分页查询
     * @return {@link SingleResponse }<{@link QuickNoteListCo }>
     */
    public PageSingleResponse<QuickNoteListCo> pageQuery(QuickNotePageQuery pageQuery) {
        return quickNotePageQueryExe.execute(pageQuery);
    }

    /**
     * 删除
     * @param noteNo 速记编号
     * @return {@link Response }
     */
    public Response delete(String noteNo) {
        return quickNoteDeleteCmdExe.execute(noteNo);
    }

    /**
     * 下载为pdf
     * @param noteNo   速记编号
     * @param response 响应
     */
    public void downloadPdf(String noteNo, HttpServletResponse response) {
        quickNoteDownloadPdfCmdExe.downloadPdf(noteNo, response);
    }

    /**
     * 下载为word
     * @param noteNo   速记编号
     * @param response 响应
     */
    public void downloadWord(String noteNo, HttpServletResponse response) {
        quickNoteDownloadWordCmdExe.downloadWord(noteNo, response);
    }
}
