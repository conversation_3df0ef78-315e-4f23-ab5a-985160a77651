package com.dangbei.aisearch.app.executor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.dashscope.aigc.generation.GenerationParam;
import com.alibaba.dashscope.common.Message;
import com.alibaba.dashscope.common.ResponseFormat;
import com.alibaba.dashscope.common.Role;
import com.alibaba.fastjson2.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.dangbei.aisearch.app.model.ChatContext;
import com.dangbei.aisearch.client.dto.clientobject.SplitSearchKeywordResult;
import com.dangbei.aisearch.common.enums.RoleEnum;
import com.dangbei.aisearch.common.util.MarkdownUtils;
import com.dangbei.aisearch.domain.entity.ChatMessageEntity;
import com.dangbei.aisearch.infrastructure.config.properties.DashScopeProperties;
import com.dangbei.aisearch.infrastructure.factory.AutoCloseGeneration;
import com.dangbei.aisearch.infrastructure.factory.PoolDashScopeObjectFactory;
import com.dangbei.aisearch.infrastructure.i18n.I18nUtil;
import com.dangbei.aisearch.infrastructure.prompt.PromptParam;
import com.dangbei.aisearch.infrastructure.prompt.PromptUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * 拆解搜索词命令执行器
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-22
 */
@Slf4j
@Component
public class SplitSearchKeywordCmdExe {

    @Resource
    private PromptUtil promptUtil;
    @Resource
    private DashScopeProperties dashScopeProperties;
    @Resource(name = "saveChatMessageAsyncExecutor")
    private Executor saveChatMessageAsyncExecutor;
    @NacosValue(value = "${ai-search.search-judge-timeout:5}", autoRefreshed = true)
    private Integer searchJudgeTimeout;

    public SplitSearchKeywordResult execute(ChatContext ctx, String additionInfo) {
        // 创建一个CompletableFuture任务
        CompletableFuture<SplitSearchKeywordResult> future = CompletableFuture.supplyAsync(() ->
            splitKeywordTask(ctx, additionInfo), saveChatMessageAsyncExecutor);

        try {
            // 在主线程中等待任务完成，设置超时时间为3秒
            return future.get(searchJudgeTimeout, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.warn("任务超时，自动断开：" + e.getMessage(), e);
            future.cancel(true);
        } catch (InterruptedException | ExecutionException e) {
            log.warn(e.getMessage(), e);
            if (!future.isCancelled()) {
                future.cancel(true);
            }
        }
        return new SplitSearchKeywordResult();
    }

    private SplitSearchKeywordResult splitKeywordTask(ChatContext ctx, String additionInfo) {
        Map<String, Object> paramMap = Maps.newHashMap();
        List<Message> recentMsgTurns = getRecentMsgTurns(ctx.getHistory(), 3);
        if (CollUtil.isNotEmpty(recentMsgTurns)) {
            StringBuilder append = new StringBuilder();
            recentMsgTurns.stream()
                .filter(msg -> Role.USER.getValue().equals(msg.getRole()))
                .forEach(msg -> append.append("用户提问：").append(msg.getContent()).append("\n\n"));
            paramMap.put("question_context", append.toString());
        }

        // 用户当前提问，结合知识库
        String question = ctx.getChatCmd().getQuestion();
        if (StrUtil.isNotBlank(additionInfo)) {
            question = additionInfo;
        }

        DashScopeProperties.ModelConfig splitKeywordConfig = dashScopeProperties.getSplitKeywordConfig();
        String prompt = promptUtil.format(PromptParam.builder()
            .promptTmpl(I18nUtil.isChinese() ? splitKeywordConfig.getSystem() : splitKeywordConfig.getEnSystem())
            .question(question)
            .extParam(paramMap)
            .build());

        List<Message> msgList = new ArrayList<>();
        msgList.add(Message.builder().role(Role.SYSTEM.getValue()).content(prompt).build());
        msgList.add(Message.builder().role(Role.USER.getValue()).content(question).build());

        log.debug("搜索判定提示词: {}", JSON.toJSONString(msgList));
        GenerationParam param = GenerationParam.builder()
            .apiKey(dashScopeProperties.getApiKey())
            .model(splitKeywordConfig.getModel())
            .messages(msgList)
            .resultFormat(GenerationParam.ResultFormat.MESSAGE)
            .responseFormat(ResponseFormat.from(ResponseFormat.JSON_OBJECT))
            .build();

        try (AutoCloseGeneration gen = PoolDashScopeObjectFactory.getGeneration()) {
            long start = System.currentTimeMillis();
            String modelResult = MarkdownUtils.removeMarkdownTags(gen.call(param).getOutput().getChoices().get(0).getMessage().getContent());
            int costTime = Convert.toInt(System.currentTimeMillis() - start) / 1000;
            log.info("""
                联网判定耗时：{}秒
                结果：{}
                """, costTime, modelResult);

            boolean isJson = JSONUtil.isTypeJSONObject(modelResult);
            if (isJson) {
                return JSON.parseObject(modelResult, SplitSearchKeywordResult.class);
            }

            boolean isJsonArr = JSONUtil.isTypeJSONArray(modelResult);
            if (isJsonArr) {
                return JSON.parseArray(modelResult, SplitSearchKeywordResult.class).get(0);
            }

            log.warn("联网判定结果格式异常，请排查={}", modelResult);
            return new SplitSearchKeywordResult();
        } catch (Exception ex) {
            log.warn("splitSearchKeyword异常：" + ex.getMessage(), ex);
            return new SplitSearchKeywordResult();
        }
    }

    private List<Message> getRecentMsgTurns(List<ChatMessageEntity> history, Integer turns) {
        if (CollUtil.isEmpty(history)) {
            return new ArrayList<>();
        }

        LinkedList<Message> recentMessages = new LinkedList<>();
        // 倒序遍历消息列表，找到最近的 12 条 user/assistant 消息（6轮）
        for (ChatMessageEntity entity : history) {
            if (RoleEnum.USER.eq(entity.getRole())) {
                recentMessages.addFirst(Message.builder().role(Role.USER.getValue()).content(entity.getContent()).build());
            }
            if (RoleEnum.ASSISTANT.eq(entity.getRole())) {
                recentMessages.addFirst(Message.builder().role(Role.ASSISTANT.getValue()).content(entity.getContent()).build());
            }
            if (recentMessages.size() >= turns * 2) {
                break; // 找到三轮对话，退出
            }
        }
        return recentMessages;
    }

}
