package com.dangbei.aisearch.app.executor;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.exception.Assert;
import com.dangbei.aisearch.client.dto.cmd.FeedbackSubmitCmd;
import com.dangbei.aisearch.domain.entity.FeedbackEntity;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-19
 */
@Component
public class FeedbackSubmitCmdExe {

    public Response execute(FeedbackSubmitCmd submitCmd) {
        Assert.isTrue(StringUtils.isNotBlank(submitCmd.getContent())
            || CollectionUtils.isNotEmpty(submitCmd.getImageUrls()), "反馈内容和图片不能同时为空");
        Assert.isTrue(CollectionUtils.size(submitCmd.getImageUrls()) <= 4, "图片不能超过4张");

        FeedbackEntity feedbackEntity = new FeedbackEntity();
        feedbackEntity.setMobile(submitCmd.getMobile());
        feedbackEntity.setContent(submitCmd.getContent());
        feedbackEntity.setImageUrls(submitCmd.getImageUrls());
        feedbackEntity.save();
        return Response.buildSuccess();
    }
}
