package com.dangbei.aisearch.app.agentapprove.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.dangbei.aisearch.client.dto.AgentChatExample;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-28 16:23
 **/
@Data
public class AiContentApproveTemplate {

    @Schema(description = "智能体名称")
    @TableField(value = "name")
    private String name;

    @Schema(description = "智能体简介")
    private String intro;

    @Schema(description = "初始化推荐问题")
    private List<String> followUp;

    @Schema(description = "用户给智能体设定提示词")
    private String userPrompt;

    @Schema(description = "智能体性格描述")
    private String personality;

    @Schema(description = "智能体对话示例")
    private List<AgentChatExample> chatExample;
}
