package com.dangbei.aisearch.client.dto.clientobject;

import com.alibaba.cola.dto.DTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * IP检查
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-04-11
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class IpCheckCo extends DTO {

    @Schema(description = "是否国外IP")
    private boolean isForeign = false;

}
