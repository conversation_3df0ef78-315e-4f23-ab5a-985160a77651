package com.dangbei.aisearch.infrastructure.gatewayimpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dangbei.aisearch.client.dto.UserDeviceDTO;
import com.dangbei.aisearch.common.enums.BanTypeEnum;
import com.dangbei.aisearch.domain.entity.BanEntity;
import com.dangbei.aisearch.domain.entity.UserInfoEntity;
import com.dangbei.aisearch.domain.gateway.BanGateway;
import com.dangbei.aisearch.domain.gateway.UserInfoGateway;
import com.dangbei.aisearch.infrastructure.common.base.BaseGatewayImpl;
import com.dangbei.aisearch.infrastructure.convertor.BanConvertor;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.BanDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.BanMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * Ban 网关层实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-20
 */
@Component
public class BanGatewayImpl extends BaseGatewayImpl<Long, BanEntity, BanDO, BanMapper, BanConvertor> implements BanGateway {

    @Resource
    private UserInfoGateway userInfoGateway;

    @Override
    public boolean isBanned(Integer banType, String banValue) {
        LambdaQueryWrapper<BanDO> queryWrapper = Wrappers.lambdaQuery(BanDO.class)
            .eq(BanDO::getBanType, banType)
            .eq(BanDO::getBanValue, banValue)
            .ge(BanDO::getBanEndTime, LocalDateTime.now());
        return Objects.nonNull(baseMapper.selectOne(queryWrapper));
    }

    @Override
    public boolean isBanned(List<String> ips) {
        if (CollectionUtils.isEmpty(ips)) {
            return false;
        }
        LambdaQueryWrapper<BanDO> queryWrapper = Wrappers.lambdaQuery(BanDO.class)
            .eq(BanDO::getBanType, BanTypeEnum.IP.getType())
            .in(BanDO::getBanValue, ips)
            .ge(BanDO::getBanEndTime, LocalDateTime.now())
            .last(LIMIT_ONE);
        return Objects.nonNull(baseMapper.selectOne(queryWrapper));
    }

    @Override
    public boolean isBanned(List<String> ips, UserDeviceDTO userDeviceDTO) {
        // 检查 IP 是否被封禁
        if (isBanned(ips)) {
            return true;
        }

        // 检查用户 ID 是否被封禁
        String userId = userDeviceDTO.getUserId();
        if (StringUtils.isNotBlank(userId)) {
            // 检查用户 ID 是否被封禁
            if (isBanned(BanTypeEnum.USER_ID.getType(), userId)) {
                return true;
            }

            // 检查手机号是否被封禁
            UserInfoEntity userInfo = userInfoGateway.getByUserId(userId);
            if (Objects.nonNull(userInfo) &&
                StringUtils.isNotBlank(userInfo.getMobile()) &&
                isBanned(BanTypeEnum.MOBILE.getType(), userInfo.getMobile())) {
                return true;
            }
        }

        // 检查设备 ID 是否被封禁
        String deviceId = userDeviceDTO.getDeviceId();
        if (StringUtils.isNotBlank(deviceId) &&
            isBanned(BanTypeEnum.DEVICE_ID.getType(), deviceId)) {
            return true;
        }
        return false;
    }
}
