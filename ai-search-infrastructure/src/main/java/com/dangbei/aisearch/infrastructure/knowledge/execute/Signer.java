package com.dangbei.aisearch.infrastructure.knowledge.execute;

import com.volcengine.auth.ISignerV4;
import com.volcengine.auth.impl.SignerV4Impl;
import com.volcengine.model.Credentials;
import com.volcengine.service.SignableRequest;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-07 13:35
 **/
public class Signer {
    public static SignableRequest prepareRequest(String host,
                                                 String path,
                                                 String method,
                                                 List<NameValuePair> params,
                                                 String body,
                                                 String ak,
                                                 String sk) throws Exception {
        SignableRequest request = new SignableRequest();
        request.setMethod(method);
        request.setHeader("Accept", "application/json");
        request.setHeader("Content-Type", "application/json");
        request.setHeader("Host", "api-knowledgebase.mlp.cn-beijing.volces.com");
        request.setHeader("User-Agent", "volc-sdk-java/v1.0.197");
        request.setEntity(new StringEntity(body, "utf-8"));

        URIBuilder builder = request.getUriBuilder();
        builder.setScheme("https");
        builder.setHost(host);
        builder.setPath(path);
        if (params != null) {
            builder.setParameters(params);
        }

        RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(5000).setConnectTimeout(2000).build();
        request.setConfig(requestConfig);

        Credentials credentials = new Credentials("cn-beijing-1", "air");
        credentials.setAccessKeyID(ak);
        credentials.setSecretAccessKey(sk);

        // 签名
        ISignerV4 ISigner = new SignerV4Impl();
        ISigner.sign(request, credentials);

        return request;
    }
}
