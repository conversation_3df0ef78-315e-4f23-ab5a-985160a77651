package com.dangbei.aisearch.domain.service;

import com.dangbei.aisearch.domain.entity.BaseDocEntity;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;

/**
 * 文档内容安全检测接口
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
public interface DocContentSecurityChecker {

    /**
     * 检查文档内容是否安全
     *
     * @param docEntity 文档实体
     * @param strictMode 是否严格模式, 严格模式，接口调用的异常均视为不通过
     * @return {@link SecurityCheckResult }
     */
    SecurityCheckResult checkDocContentSecurity(BaseDocEntity docEntity, boolean strictMode);

    /**
     * 检查文件是否安全
     * @param docEntity 文档实体
     * @param strictMode 是否严格模式, 严格模式，接口调用的异常均视为不通过
     * @return {@link SecurityCheckResult }
     */
    SecurityCheckResult checkDocFileSecurity(BaseDocEntity docEntity, boolean strictMode);

    /**
     * 文档安全检测结果
     */
    @Getter
    @Data
    @Builder
    class SecurityCheckResult {
        private final boolean safe;
        private final String failReason;

        public static SecurityCheckResult safe() {
            return new SecurityCheckResult(true, null);
        }
        public static SecurityCheckResult unsafe(String reason) {
            return new SecurityCheckResult(false, reason);
        }
    }
}
