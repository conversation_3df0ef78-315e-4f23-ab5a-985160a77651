package com.dangbei.aisearch.infrastructure.gatewayimpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dangbei.aisearch.domain.entity.UserInfoEntity;
import com.dangbei.aisearch.domain.gateway.UserInfoGateway;
import com.dangbei.aisearch.infrastructure.common.base.BaseGatewayImpl;
import com.dangbei.aisearch.infrastructure.convertor.UserInfoConvertor;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.UserInfoDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.UserInfoMapper;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * UserInfo 网关层实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-01-07
 */
@Component
public class UserInfoGatewayImpl extends BaseGatewayImpl<Long, UserInfoEntity, UserInfoDO, UserInfoMapper, UserInfoConvertor> implements UserInfoGateway {

    @Override
    public UserInfoEntity getByMobile(String mobile) {
        LambdaQueryWrapper<UserInfoDO> queryWrapper = Wrappers.lambdaQuery(UserInfoDO.class).eq(UserInfoDO::getMobile, mobile);
        return convertor.toEntity(baseMapper.selectOne(queryWrapper));
    }

    @Override
    public UserInfoEntity getByUserId(String userId) {
        LambdaQueryWrapper<UserInfoDO> queryWrapper = Wrappers.lambdaQuery(UserInfoDO.class).eq(UserInfoDO::getUserId, userId);
        return convertor.toEntity(baseMapper.selectOne(queryWrapper));
    }

    @Override
    public Map<String, UserInfoEntity> getUserMap(Collection<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Maps.newHashMap();
        }
        LambdaQueryWrapper<UserInfoDO> queryWrapper = Wrappers.lambdaQuery(UserInfoDO.class).in(UserInfoDO::getUserId, userIds);
        return convertor.toEntityList(baseMapper.selectList(queryWrapper))
            .stream().collect(Collectors.toMap(UserInfoEntity::getUserId, Function.identity(), (v1, v2) -> v2));
    }
}
