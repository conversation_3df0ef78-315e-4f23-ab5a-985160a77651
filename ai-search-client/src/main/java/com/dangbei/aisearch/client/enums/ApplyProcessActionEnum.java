package com.dangbei.aisearch.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 申请处理动作枚举
 * <AUTHOR>
 * @date 2025-05-27
 **/
@Getter
@AllArgsConstructor
public enum ApplyProcessActionEnum {

    APPROVE(1, "通过"),
    REJECT(2, "拒绝");

    private final Integer code;
    private final String desc;

    public static ApplyProcessActionEnum getByCode(Integer code) {
        for (ApplyProcessActionEnum action : values()) {
            if (Objects.equals(action.getCode(), code)) {
                return action;
            }
        }
        return null;
    }

    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }

    public static boolean isApprove(Integer code) {
        return APPROVE.getCode().equals(code);
    }
}
