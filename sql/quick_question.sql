CREATE TABLE `ais_quick_question`
(
    `id`                  bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `user_id`             varchar(64)  NOT NULL COMMENT '用户ID',
    `title`               varchar(256) NOT NULL COMMENT '标题',
    `content`             text         NOT NULL COMMENT '内容',
    `last_chat_time`      datetime              DEFAULT NULL COMMENT '上次对话时间',
    `create_person`       varchar(16)           DEFAULT '' COMMENT '创建人',
    `create_time`         datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '业务创建时间',
    `update_person`       varchar(16)           DEFAULT '' COMMENT '修改人',
    `update_time`         datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '业务更新时间',
    `is_deleted`          bigint(20)   NOT NULL DEFAULT '0' COMMENT '逻辑删除标识，0-未删除,其他-已删除',
    `db_modify_timestamp` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库变更时间',
    PRIMARY KEY (`id`)
) AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT = '快捷提问表';

alter table ais_conversation
    add column source varchar(32) default null comment '来源';
