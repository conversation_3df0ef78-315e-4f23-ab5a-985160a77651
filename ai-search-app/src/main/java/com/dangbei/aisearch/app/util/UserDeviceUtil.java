package com.dangbei.aisearch.app.util;

import com.alibaba.cola.exception.Assert;
import com.dangbei.aisearch.client.dto.UserDeviceDTO;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-01-17
 */
@UtilityClass
public class UserDeviceUtil {

    public static UserDeviceDTO getUserDeviceInfo() {
        UserDeviceDTO userDeviceDTO = getNullableUserDeviceInfo();
        Assert.isTrue(!StringUtils.isAllBlank(userDeviceDTO.getDeviceId(), userDeviceDTO.getUserId()), "请求参数错误");
        return userDeviceDTO;
    }

    public static UserDeviceDTO getNullableUserDeviceInfo() {
        UserDeviceDTO userDeviceDTO = new UserDeviceDTO();
        userDeviceDTO.setDeviceId(RequestThreadLocalUtil.getDeviceId());
        userDeviceDTO.setUserId(UserContextUtil.getUserId());
        return userDeviceDTO;
    }

    public static String getUserIdDefaultDeviceId() {
        UserDeviceDTO userDeviceDTO = getUserDeviceInfo();
        return Objects.nonNull(userDeviceDTO.getUserId()) ? userDeviceDTO.getUserId() : userDeviceDTO.getDeviceId();
    }

    public static void checkUserDevice(String userId, String deviceId) {
        UserDeviceDTO userDeviceDTO = getUserDeviceInfo();
        if (StringUtils.isNotBlank(userDeviceDTO.getUserId())) {
            Assert.equals(userDeviceDTO.getUserId(), userId, "非法数据访问");
        } else if (StringUtils.isNotBlank(userDeviceDTO.getDeviceId())) {
            Assert.equals(userDeviceDTO.getDeviceId(), deviceId, "非法数据访问");
        }
    }

    public static boolean isOwn(String userId, String deviceId) {
        UserDeviceDTO userDeviceDTO = getUserDeviceInfo();
        if (StringUtils.isNotBlank(userDeviceDTO.getUserId())) {
            return Objects.equals(userDeviceDTO.getUserId(), userId);
        } else if (StringUtils.isNotBlank(userDeviceDTO.getDeviceId())) {
            return Objects.equals(userDeviceDTO.getDeviceId(), deviceId);
        }
        return false;
    }
}
