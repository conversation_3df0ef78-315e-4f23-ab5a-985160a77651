//package com.dangbei.aisearch.adapter.web;
//
//import com.alibaba.cola.dto.PageSingleResponse;
//import com.alibaba.cola.dto.Response;
//import com.alibaba.cola.dto.SingleResponse;
//import com.dangbei.aisearch.app.service.QuickNoteService;
//import com.dangbei.aisearch.client.dto.clientobject.QuickNoteCo;
//import com.dangbei.aisearch.client.dto.clientobject.QuickNoteListCo;
//import com.dangbei.aisearch.client.dto.cmd.QuickNoteAddCmd;
//import com.dangbei.aisearch.client.dto.cmd.QuickNoteDeleteCmd;
//import com.dangbei.aisearch.client.dto.cmd.QuickNoteDownloadCmd;
//import com.dangbei.aisearch.client.dto.cmd.QuickNoteUpdateCmd;
//import com.dangbei.aisearch.client.dto.cmd.query.QuickNotePageQuery;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import org.springframework.web.bind.annotation.DeleteMapping;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//import javax.servlet.http.HttpServletResponse;
//import javax.validation.Valid;
//
///**
// * QuickNote 服务接口
// * <AUTHOR> href="<EMAIL>"><EMAIL></a>
// * @version 1.0.0
// * @since 2025-01-07
// */
//@Tag(name = "QuickNoteController", description = "QuickNoteController服务")
//@RequestMapping("/quickNoteApi/v1")
//@RestController
//public class QuickNoteController {
//
//    @Resource
//    private QuickNoteService quickNoteService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建速记", description = "创建速记")
//    public SingleResponse<QuickNoteCo> create(@Valid @RequestBody QuickNoteAddCmd addCmd) {
//        return quickNoteService.create(addCmd);
//    }
//
//    @PostMapping("/update")
//    @Operation(summary = "更新速记", description = "更新速记")
//    public Response update(@Valid @RequestBody QuickNoteUpdateCmd updateCmd) {
//        return quickNoteService.update(updateCmd);
//    }
//
//    @GetMapping("/getByNoteNo")
//    @Operation(summary = "根据速记编号查询", description = "根据速记编号查询")
//    public SingleResponse<QuickNoteCo> getByNoteNo(@RequestParam String noteNo) {
//        return quickNoteService.getByNoteNo(noteNo);
//    }
//
//    @PostMapping("/pageQuery")
//    @Operation(summary = "分页查询速记列表", description = "分页查询速记列表")
//    public PageSingleResponse<QuickNoteListCo> pageQuery(@Valid @RequestBody QuickNotePageQuery pageQuery) {
//        return quickNoteService.pageQuery(pageQuery);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除速记", description = "删除速记")
//    public Response delete(@Valid @RequestBody QuickNoteDeleteCmd deleteCmd) {
//        return quickNoteService.delete(deleteCmd.getNoteNo());
//    }
//
//    @PostMapping("/downloadPdf")
//    @Operation(summary = "下载为pdf", description = "下载为pdf")
//    public void downloadPdf(@Valid @RequestBody QuickNoteDownloadCmd downloadCmd, HttpServletResponse response) {
//        quickNoteService.downloadPdf(downloadCmd.getNoteNo(), response);
//    }
//
//    @PostMapping("/downloadWord")
//    @Operation(summary = "下载为word", description = "下载为word")
//    public void downloadWord(@Valid @RequestBody QuickNoteDownloadCmd downloadCmd, HttpServletResponse response) {
//        quickNoteService.downloadWord(downloadCmd.getNoteNo(), response);
//    }
//}
