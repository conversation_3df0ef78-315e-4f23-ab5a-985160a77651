package com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dangbei.aisearch.client.dto.clientobject.DocIdExtCo;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.SharedKnowledgeDocDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * SharedKnowledgeDoc Mapper接口
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-05-27
 */
public interface SharedKnowledgeDocMapper extends BaseMapper<SharedKnowledgeDocDO> {

    /**
     * 查询docIdExt相同的文档数据
     * @param docIdExtList 外部文档ID列表
     * @param docIdList 文档ID列表
     * @return docIdExt相同的文档数据
     */
    List<DocIdExtCo> getDocIdExtUseCount(@Param("docIdExtList") List<String> docIdExtList,
                                         @Param("docIdList") List<String> docIdList);

    /**
     * 根据知识库ID和用户ID统计文档数量
     * 统计所有处理完成的文档加上当前用户上传的未处理完成的文档
     * @param knowledgeId 知识库ID
     * @param currentUserId 当前用户ID
     * @return 文档数量
     */
    Long countDocsByKnowledgeIdAndUserId(@Param("knowledgeId") String knowledgeId,
                                        @Param("currentUserId") String currentUserId);
}
