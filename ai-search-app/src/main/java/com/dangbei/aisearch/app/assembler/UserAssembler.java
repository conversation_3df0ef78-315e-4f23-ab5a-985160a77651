package com.dangbei.aisearch.app.assembler;

import com.dangbei.aisearch.client.dto.clientobject.UserInfoCo;
import com.dangbei.aisearch.domain.entity.UserInfoEntity;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-08
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface UserAssembler {

    UserInfoCo toCo(UserInfoEntity userInfoEntity);
}
