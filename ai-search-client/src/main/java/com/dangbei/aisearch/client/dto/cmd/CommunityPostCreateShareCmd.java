package com.dangbei.aisearch.client.dto.cmd;

import com.alibaba.cola.dto.Command;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * .
 * <p>
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-31
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CommunityPostCreateShareCmd extends Command {

    @NotBlank(message = "postId不能为空")
    @Schema(description = "帖子ID")
    private String postId;

}
