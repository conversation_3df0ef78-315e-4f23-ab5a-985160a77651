package com.dangbei.aisearch.client.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.List;

/**
 * 智能体菜单数据类型枚举
 * <AUTHOR>
 * @since 2025-03-13
 **/
@Getter
@RequiredArgsConstructor
public enum AgentCategoryDataTypeEnum {

    WATER_FALL_1("瀑布流（大图）", 0),
    WATER_FALL_2("瀑布流（左图右文）", 2),
    POSITION("资源位", 1);

    private final String desc;
    private final Integer value;

    public static List<Integer> getWebShowList() {
        return Arrays.asList(WATER_FALL_1.getValue(), WATER_FALL_2.getValue());
    }

}
