package com.dangbei.aisearch.client.dto.cmd;

import com.alibaba.cola.dto.DTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 文件上传配置获取命令
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-25
 */
@Data
public class FileUploadConfigCmd extends DTO {

    @NotBlank(message = "智能体编码不能为空")
    @Schema(description = "智能体编码")
    private String botCode;

}
