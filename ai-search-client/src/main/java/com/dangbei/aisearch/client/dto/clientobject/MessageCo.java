package com.dangbei.aisearch.client.dto.clientobject;

import com.alibaba.cola.dto.DTO;
import com.dangbei.aisearch.client.dto.ChatMsgExt;
import com.dangbei.aisearch.client.dto.cmd.ChatCmd;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 消息对象
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-25
 */
@Data
@Accessors(chain = true)
public class MessageCo extends DTO {

    @Schema(description = "消息ID")
    private String msgId;

    @Schema(description = "角色 " +
        "user-代表该条消息内容是用户发送的；" +
        "assistant-代表该条消息内容是智能体发送的")
    private String role;

    @Schema(description = "消息类型 " +
        "question-用户输入内容；" +
        "answer-智能体返回给用户的消息内容；")
    private String type;

    @Schema(description = "消息的内容")
    private String content;

    @Schema(description = "消息内容的类型" +
        "text-文本；" +
        "object_string-多模态内容，即文本和文件的组合、文本和图片的组合；" +
        "card-卡片；")
    private String contentType;

    @Schema(description = "附件列表")
    private List<ChatCmd.FileItem> files;

    @Schema(description = "引用列表")
    private List<ChatCmd.ReferenceItem> reference;

    @Schema(description = "知识库列表")
    private List<ChatCmd.KnowledgeItem> knowledgeList;

    @Schema(description = "会话ID")
    private String conversationId;

    @Schema(description = "Chat ID")
    private String chatId;

    @Schema(description = "消息的创建时间，单位秒")
    private Long createdAt;

    @Schema(description = "消息的更新时间，单位秒")
    private Long updatedAt;

    @Schema(description = "扩展字段")
    private ChatMsgExt ext;

    @Schema(description = "用户提问")
    private String question;

    @Schema(description = "推荐状态(1:推荐;0:不推荐)")
    private Integer recommendStatus;
}
