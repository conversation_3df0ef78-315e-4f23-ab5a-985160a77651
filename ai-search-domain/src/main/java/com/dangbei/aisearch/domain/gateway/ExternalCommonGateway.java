package com.dangbei.aisearch.domain.gateway;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.dangbei.aisearch.client.dto.clientobject.IpAreaCo;
import com.dangbei.devbase.client.dto.GreenCheckResp;
import com.dangbei.devbase.client.dto.GreenFileCheckResp;
import com.dangbei.devbase.client.dto.GreenFileResultResp;
import com.dangbei.devbase.client.dto.TencentCaptchaVerifyResp;
import com.dangbei.devbase.client.dto.TencentDocumentReviewQueryResp;
import com.dangbei.devbase.client.dto.TencentDocumentReviewSubmitResp;
import com.dangbei.devbase.client.dto.TencentTextSyncReviewResp;
import com.dangbei.devbase.client.dto.clientobject.TencentEncryptedCaptchaAppIdCo;
import com.dangbei.devbase.client.enums.GreenTextServiceEnum;
import com.dangbei.platform.wsserver.client.dto.cmd.PushBinaryMessageCmd;
import com.dangbei.platform.wsserver.client.dto.cmd.PushMessageCmd;

import java.util.Map;

/**
 * 外部通用网关
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-11-27
 */
public interface ExternalCommonGateway {

    /**
     * 获取分布式 ID
     * @return 分布式 ID
     */
    String getDistributedId();

    /**
     * 滑块验证校验
     * @param captchaVerifyParam 验证参数
     * @param sceneId            场景id
     * @return {@link boolean }
     */
    boolean captchaVerify(String captchaVerifyParam, String sceneId);

    /**
     * 短信发送
     * @param mobile           手机号
     * @param templateCode     短信模板code
     * @param templateParamMap 短信模板参数
     * @return {@link Response }
     */
    Response sendSms(String mobile, String templateCode, Map<String, Object> templateParamMap);

    /**
     * 根据IP获取省份信息
     * @param ip IP地址
     * @return 省份信息
     */
    IpAreaCo getIpArea(String ip);

    /**
     * 钉钉通知
     * @param sceneCode 场景值编码
     * @param title     标题
     * @param content   内容
     * @return Response
     */
    Response dingTalk(String sceneCode, String title, String content);

    /**
     * 文本内容安全过滤
     * @param content         文本内容
     * @param confidenceScore 置信分
     * @param service         绿网文本服务
     * @return Response
     */
    SingleResponse<GreenCheckResp> textGreenCheck(String content, Float confidenceScore, GreenTextServiceEnum service);

    /**
     * 图片内容安全过滤
     * @param imgUrl          图片地址
     * @param confidenceScore 置信分
     * @return Response
     */
    SingleResponse<GreenCheckResp> imageGreenCheck(String imgUrl, Float confidenceScore);

    /**
     * 提交文档内容检测任务
     * @param fileUrl 文件公网url
     * @param docType 文档类型
     * @return {@link GreenFileCheckResp }
     */
    SingleResponse<GreenFileCheckResp> fileCheck(String fileUrl, String docType);


    /**
     * 获取文档内容检测结果
     * @param taskId 任务ID
     * @return {@link GreenFileResultResp }
     */
    SingleResponse<GreenFileResultResp> fileCheckResult(String taskId);

    /**
     * 腾讯提交文档审核任务
     * @param fileUrl 文件公网url
     * @param docType 文档类型
     * @return {@link TencentDocumentReviewSubmitResp }
     */
    SingleResponse<TencentDocumentReviewSubmitResp> tencentFileCheck(String fileUrl, String docType);

    /**
     * 腾讯查询文档审核任务结果
     * @param jobId 任务ID
     * @return {@link TencentDocumentReviewQueryResp }
     */
    SingleResponse<TencentDocumentReviewQueryResp> queryTencentFileCheckResult(String jobId);

    /**
     * 执行文本同步审核
     * 参考文档：<a href="https://cloud.tencent.com/document/product/460/56285">文本内容同步审核</a>
     * @param content 审核内容 < 10000
     * @return 包含审核结果的响应
     */
    SingleResponse<TencentTextSyncReviewResp> tencentSyncReviewContent(String content);

    /**
     * 二进制消息推送
     * @param cmd 二进制消息推送命令
     * @return Response
     */
    Response pushBinaryMessage(PushBinaryMessageCmd cmd);

    /**
     * 二进制消息推送
     * @param cmd 二进制消息推送命令
     * @return Response
     */
    Response pushMessage(PushMessageCmd cmd);

    /**
     * 腾讯云验证码校验
     * @param ticket       验证码票据
     * @param randStr      随机字符串
     * @param captchaAppId 验证码应用ID
     * @return {@link TencentCaptchaVerifyResp }>
     */
    TencentCaptchaVerifyResp tencentCaptchaVerify(String ticket,
                                                  String randStr,
                                                  String captchaAppId);


    /**
     * 获取腾讯云验证码加密的验证码应用ID
     * @param captchaAppId 验证码应用ID
     * @return <{@link TencentEncryptedCaptchaAppIdCo }>
     */
    TencentEncryptedCaptchaAppIdCo getTencentCaptchaEncryptedAppId(String captchaAppId);
}
