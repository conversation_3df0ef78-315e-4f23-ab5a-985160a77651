package com.dangbei.aisearch.app.executor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.cola.exception.Assert;
import com.alibaba.fastjson2.JSON;
import com.coze.openapi.client.chat.CreateChatReq;
import com.coze.openapi.client.chat.model.ChatEvent;
import com.coze.openapi.client.chat.model.ChatEventType;
import com.coze.openapi.client.connversations.message.model.Message;
import com.coze.openapi.client.connversations.message.model.MessageObjectString;
import com.coze.openapi.client.connversations.message.model.MessageObjectStringType;
import com.coze.openapi.client.connversations.message.model.MessageType;
import com.coze.openapi.service.service.CozeAPI;
import com.dangbei.aisearch.app.model.ChatContext;
import com.dangbei.aisearch.app.model.MessageModel;
import com.dangbei.aisearch.app.util.UserDeviceUtil;
import com.dangbei.aisearch.client.dto.cmd.ChatCmd;
import com.dangbei.aisearch.common.enums.ChatTypeEnum;
import com.dangbei.aisearch.common.enums.MsgContentTypeEnum;
import com.dangbei.aisearch.common.util.ObjectMapperUtil;
import com.dangbei.aisearch.common.util.SafeExecuteUtil;
import com.dangbei.aisearch.domain.gateway.ExternalCommonGateway;
import com.dangbei.aisearch.infrastructure.config.properties.CozeBotClassifyProperties;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.reactivex.Flowable;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.ClientAbortException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

import static com.alibaba.cola.common.constant.RequestConstant.REQUEST_ID;

/**
 * 桌面端聊天控制器
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-04-01
 */
@Slf4j
@Component
public class DesktopChatCmdExe {

    @Resource
    private CozeAPI cozeApi;
    @Resource(name = "chatAsyncExecutor")
    protected Executor asyncExecutor;
    @Resource
    private ExternalCommonGateway externalCommonGateway;
    @Resource
    private CozeBotClassifyProperties cozeBotClassifyProperties;

    public Object execute(ChatCmd cmd) {
        Assert.notBlank(cmd.getQuestion(), "问题不能为空");

        // 创建SseEmitter
        SseEmitter emitter = new SseEmitter(9 * 60 * 1000L);
        emitter.onCompletion(() -> log.debug("emitter completed"));
        emitter.onError(throwable -> handleEmitterError(throwable, emitter));
        emitter.onTimeout(() -> log.warn("emitter timeout"));

        Map<String, CozeBotClassifyProperties.BotConfig> botConfig = cozeBotClassifyProperties.getBotConfig();
        CozeBotClassifyProperties.BotConfig config = botConfig.get(cmd.getBotCode());
        Assert.notNull(config, "智能体不存在，请检查!");
        String botId = config.getBotId();
        Assert.notBlank(botId, "智能体不存在，请检查!");

        String userId = UserDeviceUtil.getUserIdDefaultDeviceId();
        CreateChatReq req = CreateChatReq.builder()
            .conversationID(null)
            .botID(botId)
            .userID(userId)
            .messages(buildCozeMsg(cmd.getQuestion(), cmd.getFiles(), cmd.getReference()))
            .stream(cmd.getStream())
            .autoSaveHistory(Boolean.TRUE)
            .build();

        log.info("扣子对话Req：{}", ObjectMapperUtil.toJson(req));

        ChatContext chatCtx = new ChatContext(emitter, cmd, null);
        chatCtx.setChatType(ChatTypeEnum.QUICK.getType());
        return streamChat(chatCtx, req, config);

    }

    private SseEmitter streamChat(ChatContext context, CreateChatReq req, CozeBotClassifyProperties.BotConfig config) {
        Flowable<ChatEvent> resp = cozeApi.chat().stream(req);
        CompletableFuture.runAsync(() -> {
            try {
                context.setQuestionMsgId(externalCommonGateway.getDistributedId());
                context.setAnswerMsgId(externalCommonGateway.getDistributedId());

                resp.blockingForEach(event -> {
                    log.info("originData={}", ObjectMapperUtil.toJson(event));
                    MessageModel messageModel = context.getMessageModel();
                    acceptEvent(messageModel, event, context.getQuestionMsgId());

                    // 内容输出完成且有系统推荐问题，推一条loading
                    if (completedAnswer(messageModel) && config.isHasFollowUp()) {
                        context.sendFollowUpLoadingSSE();
                    }

                    MessageModel.Msg msg = messageModel.getMsg();
                    if (Objects.nonNull(msg)) {
                        // 发送流式消息
                        sendCurrentMsgSSE(context, messageModel);

                        // 记录会话ID
                        context.getChatCmd().setConversationId(msg.getConversationId());

                        // 将要保存的对话记录放到列表
                        if (isOutPutAnswer(messageModel)) {
                            // 拼接回答
                            context.appendAnswer(msg.getContent());
                        }
                    }

                });
                // 发送对话结束消息
                context.sendChatCompletedSSE();
                context.getEmitter().complete();
            } catch (Exception ex) {
                handleInterrupt(ex, context);
            } finally {
                // 保存对话记录
                SafeExecuteUtil.execute(context::asyncStoreChatMsg);
            }
        }, asyncExecutor);
        return context.getEmitter();
    }

    private void handleInterrupt(Exception e, ChatContext context) {
        if (isClientInterrupt(e)) {
            log.debug("流式对话被打断：{}", e.getMessage(), e);
        } else if (isBrokenPipe(e)) {
            log.debug("流式对话异常Broken pipe：{}", e.getMessage(), e);
        } else {
            log.error(e.getMessage(), e);
        }
        context.getEmitter().complete();
    }

    private boolean isClientInterrupt(Exception ex) {
        return ex instanceof RuntimeException && ex.getCause() instanceof ClientAbortException;
    }

    private boolean isBrokenPipe(Exception ex) {
        return ex instanceof ClientAbortException && ex.getCause() instanceof IOException
            && StrUtil.contains(ex.getMessage(), "Broken pipe");
    }

    private boolean isOutPutAnswer(MessageModel messageModel) {
        MessageModel.Msg msg = messageModel.getMsg();
        return MessageType.ANSWER.getValue().equals(msg.getType())
            && !isProgress(msg.getContent(), msg.getContentType())
            && ChatEventType.CONVERSATION_MESSAGE_DELTA.equals(messageModel.getChatEvent().getEvent());
    }

    /**
     * 是否是进度信息
     * @return boolean
     */
    @JsonIgnore
    public boolean isProgress(String content, String contentType) {
        return Objects.equals(contentType, MsgContentTypeEnum.PROGRESS.getValue())
            || (JSONUtil.isTypeJSONObject(content) &&
            Objects.equals(JSON.parseObject(content).getString("contentType"), "progress"));
    }

    /**
     * 发送消息
     * @throws IOException IOException
     */
    public void sendCurrentMsgSSE(ChatContext context, MessageModel messageModel) throws IOException {
        if (!needSendEmitter(messageModel)) {
            return;
        }

        // 保存推荐问题
        ChatEvent chatEvent = messageModel.getChatEvent();
        MessageModel.Msg msg = messageModel.getMsg();
        if (isFollowUp(msg)) {
            messageModel.getFollowUp().add(msg.getContent());
        }

        // 普通消息
        context.sendSSE(chatEvent.getEvent().getValue(), ObjectMapperUtil.toJson(msg));
    }

    @JsonIgnore
    public boolean isFollowUp(MessageModel.Msg msg) {
        return Objects.equals(msg.getType(), MsgContentTypeEnum.FOLLOW_UP.getValue());
    }

    /**
     * 接受扣子chatEvent信息
     * @param messageModel  chatEvent
     * @param chatEvent     chatEvent
     * @param questionMsgId 提问消息ID
     */
    @JsonIgnore
    public void acceptEvent(MessageModel messageModel, ChatEvent chatEvent, String questionMsgId) {
        messageModel.setMsg(JSON.parseObject(JSON.toJSONString(chatEvent.getMessage()), MessageModel.Msg.class));
        MessageModel.Msg msg = messageModel.getMsg();

        if (Objects.nonNull(msg)) {
            msg.setRequestId(MDC.get(REQUEST_ID));
            msg.setParentMsgId(questionMsgId);
        }

        // 1.保存ChatEvent
        messageModel.setChatEvent(chatEvent);

        // 2.contentType修改为follow_up
        if (Objects.nonNull(msg) && MessageType.FOLLOW_UP.getValue().equals(msg.getType())) {
            msg.setContentType(MsgContentTypeEnum.FOLLOW_UP.getValue());
        }
    }

    /**
     * 是否需要发送SSE消息
     * @return true=需要发送，false=不发送
     */
    @JsonIgnore
    private boolean needSendEmitter(MessageModel messageModel) {
        ChatEvent chatEvent = messageModel.getChatEvent();
        MessageModel.Msg msg = messageModel.getMsg();
        // 工具输出，跳过
        if (ChatEventType.CONVERSATION_MESSAGE_COMPLETED.equals(chatEvent.getEvent())
            && (MessageType.FUNCTION_CALL.getValue().equals(msg.getType())
            || MessageType.TOOL_RESPONSE.getValue().equals(msg.getType())
            || MessageType.VERBOSE.getValue().equals(msg.getType()))) {
            return false;
        }

        // 流式已经输出所有内容，跳过完整内容输出
        if (ChatEventType.CONVERSATION_MESSAGE_COMPLETED.equals(chatEvent.getEvent())
            && MessageType.ANSWER.getValue().equals(msg.getType())) {
            return false;
        }
        return true;
    }

    /**
     * 增量回答是否为conversation.message.completed
     * @return true=是，false=否
     */
    @JsonIgnore
    public boolean completedAnswer(MessageModel messageModel) {
        ChatEvent chatEvent = messageModel.getChatEvent();
        MessageModel.Msg msg = messageModel.getMsg();
        return Objects.nonNull(chatEvent)
            && ChatEventType.CONVERSATION_MESSAGE_COMPLETED.equals(chatEvent.getEvent())
            && Objects.nonNull(msg)
            && MessageType.ANSWER.getValue().equals(msg.getType());
    }

    /**
     * 错误处理方法，统一处理 emitter 错误并完成 emitter
     * @param throwable 异常
     * @param emitter   SseEmitter
     */
    private void handleEmitterError(Throwable throwable, SseEmitter emitter) {
        log.warn(throwable.getMessage(), throwable);
        emitter.completeWithError(throwable);
    }

    private List<Message> buildCozeMsg(String question, List<ChatCmd.FileItem> files, List<ChatCmd.ReferenceItem> referenceList) {
        Assert.isTrue(StrUtil.isNotBlank(question) || CollUtil.isNotEmpty(files), "输入不能为空!");
        if (CollUtil.isEmpty(files) && CollUtil.isEmpty(referenceList)) {
            return Collections.singletonList(Message.buildUserQuestionText(question));
        } else {
            List<MessageObjectString> objList = new ArrayList<>();
            if (StrUtil.isNotBlank(question)) {
                objList.add(MessageObjectString.buildText(question));
            }
            for (ChatCmd.FileItem item : files) {
                MessageObjectStringType msgType = MessageObjectStringType.fromString(item.getType());
                if (MessageObjectStringType.FILE.equals(msgType)) {
                    if (StringUtils.isNotBlank(item.getFileId())) {
                        objList.add(MessageObjectString.buildFileByID(item.getFileId()));
                    }
                    if (StringUtils.isNotBlank(item.getFileUrl())) {
                        objList.add(MessageObjectString.buildFileByURL(item.getFileUrl()));
                    }
                }
                if (MessageObjectStringType.IMAGE.equals(msgType)) {
                    if (StringUtils.isNotBlank(item.getFileId())) {
                        objList.add(MessageObjectString.buildImageByID(item.getFileId()));
                    }
                    if (StringUtils.isNotBlank(item.getFileUrl())) {
                        objList.add(MessageObjectString.buildImageByURL(item.getFileUrl()));
                    }
                }
            }
            if (CollUtil.isNotEmpty(referenceList)) {
                for (ChatCmd.ReferenceItem item : referenceList) {
                    MessageObjectStringType msgType = MessageObjectStringType.fromString(item.getType());
                    if (MessageObjectStringType.FILE.equals(msgType)) {
                        if (StringUtils.isNotBlank(item.getFileUrl())) {
                            objList.add(MessageObjectString.buildFileByURL(item.getFileUrl()));
                        }
                    }
                    if (MessageObjectStringType.IMAGE.equals(msgType)) {
                        if (StringUtils.isNotBlank(item.getFileUrl())) {
                            objList.add(MessageObjectString.buildImageByURL(item.getFileUrl()));
                        }
                    }
                    if (MessageObjectStringType.TEXT.equals(msgType)) {
                        if (StringUtils.isNotBlank(item.getText())) {
                            objList.add(MessageObjectString.buildText(item.getText()));
                        }
                    }
                }
            }
            return Collections.singletonList(Message.buildUserQuestionObjects(objList));
        }
    }

}
