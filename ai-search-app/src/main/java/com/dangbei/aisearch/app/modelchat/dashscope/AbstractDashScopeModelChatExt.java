package com.dangbei.aisearch.app.modelchat.dashscope;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import com.alibaba.dashscope.aigc.generation.GenerationOutput;
import com.alibaba.dashscope.aigc.generation.GenerationParam;
import com.alibaba.dashscope.aigc.generation.GenerationResult;
import com.alibaba.dashscope.aigc.generation.GenerationUsage;
import com.alibaba.dashscope.aigc.generation.SearchInfo;
import com.alibaba.dashscope.common.Message;
import com.dangbei.aisearch.app.model.ChatContext;
import com.dangbei.aisearch.app.model.CustomSearchOptions;
import com.dangbei.aisearch.app.modelchat.AbstractModelChatExt;
import com.dangbei.aisearch.client.dto.clientobject.TokenUsageCo;
import com.dangbei.aisearch.common.enums.ProviderEnum;
import com.dangbei.aisearch.common.util.ObjectMapperUtil;
import com.dangbei.aisearch.infrastructure.config.properties.DashScopeProperties;
import com.dangbei.aisearch.infrastructure.factory.AutoCloseGeneration;
import com.dangbei.aisearch.infrastructure.factory.PoolDashScopeObjectFactory;
import com.dangbei.aisearch.infrastructure.prompt.RagResponse;
import com.dangbei.aisearch.infrastructure.search.SearchResponse;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.theokanning.openai.Usage;
import com.theokanning.openai.completion.chat.AssistantMessage;
import com.theokanning.openai.completion.chat.ChatCompletionChoice;
import com.theokanning.openai.completion.chat.ChatCompletionRequest;
import com.theokanning.openai.completion.chat.ChatCompletionResult;
import io.reactivex.Flowable;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 百炼模型对话扩展点
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-22
 */
@Slf4j
public abstract class AbstractDashScopeModelChatExt extends AbstractModelChatExt {

    public static final Gson gson = new GsonBuilder().create();

    @Resource
    protected DashScopeProperties dashScopeProperties;

    /**
     * 构建查询参数
     * @param ctx 上下文
     * @return 调用Param
     */
    protected abstract ChatCompletionRequest buildParam(ChatContext ctx);

    @Override
    public Flowable<ChatCompletionResult> chatCompletion(ChatContext ctx) throws Exception {

        try (AutoCloseGeneration gen = PoolDashScopeObjectFactory.getGeneration()) {
            ChatCompletionRequest openAiRequest = buildParam(ctx);

            // 填充模型信息
            ctx.setUsageInfo(new TokenUsageCo(ProviderEnum.DASH_SCOPE.getCode(), openAiRequest.getModel()));

            // RAG检索内容处理
            RagResponse ragResponse = ragSearch(openAiRequest, ctx);

            GenerationParam request = convertRequest(openAiRequest);

            // 结构转换
            return gen.streamCall(request).map(res -> convertCompletionResult(res, request, ctx, ragResponse));
        }
    }

    /**
     * 将Openai输入结构转为百炼输入结构
     * @param request openAi输入结构
     * @return 百炼输入结构
     */
    private GenerationParam convertRequest(ChatCompletionRequest request) {
        Type type = new TypeToken<List<Message>>() {
        }.getType();

        // 转换messages
        String json = ObjectMapperUtil.toJson(request.getMessages());
        List<Message> messages = gson.fromJson(json, type);

        GenerationParam param = GenerationParam.builder()
            .apiKey(dashScopeProperties.getApiKey())
            .model(request.getModel())
            .messages(messages)
            .temperature(Convert.toFloat(request.getTemperature()))
            .topP(request.getTemperature())
            .enableSearch(request.isEnableSearch())
            .incrementalOutput(Boolean.TRUE)
            .maxTokens(request.getMaxTokens())
            .resultFormat(GenerationParam.ResultFormat.MESSAGE)
            .searchOptions(CustomSearchOptions.builder()
                .enableSource(true)
                .enableCitation(true)
                .citationFormat("[<number>]")
                .searchStrategy("pro_ultra")
                .prependSearchResult(true)
                .build())
            .build();

        log.info("调用百炼GenerationParam\n{}", gson.toJson(param));
        return param;
    }

    /**
     * 将百炼CompletionResult转为Openai输出结构
     * @param aliResult 阿里百炼流式Result
     * @param request   百炼请求对象
     * @param ctx       对话上下文
     * @return Openai输出结构
     */
    @NotNull
    private static ChatCompletionResult convertCompletionResult(GenerationResult aliResult,
                                                                GenerationParam request,
                                                                ChatContext ctx,
                                                                RagResponse ragResponse) {
        if (Objects.isNull(aliResult)) {
            return new ChatCompletionResult();
        }

        ChatCompletionResult target = new ChatCompletionResult();
        target.setId(aliResult.getRequestId());

        // 使用量
        GenerationOutput aliOutput = aliResult.getOutput();
        target.setUsage(convertUsage(aliResult.getUsage()));

        // 时间
        target.setCreated(DateUtil.currentSeconds());

        // 模型
        target.setModel(request.getModel());

        // 服务提供商
        target.setLlmProvider(ProviderEnum.DASH_SCOPE.getCode());

        // 联网结果转换
        SearchResponse cnvAliResponse = convertSearchInfo(aliOutput.getSearchInfo(), Collections.singletonList(ctx.getChatCmd().getQuestion()));
        SearchResponse searchResponse = Objects.nonNull(cnvAliResponse) ? cnvAliResponse : ragResponse.getSearchResponse();

        // RAG结果转换
        ragResponse.setSearchResponse(searchResponse);
        target.setRagSearchInfo(ragResponse);

        // 模型回答
        target.setChoices(convertChoices(aliResult.getOutput().getChoices()));

        return target;
    }

    private static SearchResponse convertSearchInfo(SearchInfo aliSearchInfo, List<String> query) {
        if (Objects.nonNull(aliSearchInfo)) {
            List<SearchInfo.SearchResult> aliSearchList = aliSearchInfo.getSearchResults();
            if (CollUtil.isNotEmpty(aliSearchList)) {
                SearchResponse searchInfo = new SearchResponse(query);
                List<SearchResponse.SearchResult> searchResults = new ArrayList<>();
                for (SearchInfo.SearchResult searchResult : aliSearchList) {
                    SearchResponse.SearchResult build = new SearchResponse.SearchResult()
                        .setIdIndex(Convert.toStr(searchResult.getIndex()))
                        .setUrl(searchResult.getUrl())
                        .setTitle(searchResult.getTitle())
                        .setSummary(searchResult.getTitle())
                        .setSiteName(searchResult.getSiteName())
                        .setIcon(searchResult.getIcon());
                    searchResults.add(build);
                }
                searchInfo.setSearchResults(searchResults);
                return searchInfo;
            }
        }
        return null;
    }

    private static Usage convertUsage(GenerationUsage aliUsage) {
        if (Objects.nonNull(aliUsage)) {
            Usage targetUsage = new Usage();
            if (Objects.nonNull(aliUsage.getInputTokens())) {
                targetUsage.setPromptTokens(aliUsage.getInputTokens());
            }
            if (Objects.nonNull(aliUsage.getOutputTokens())) {
                targetUsage.setCompletionTokens(aliUsage.getOutputTokens());
            }
            if (Objects.nonNull(aliUsage.getTotalTokens())) {
                targetUsage.setTotalTokens(aliUsage.getTotalTokens());
            }
            return targetUsage;
        }
        return null;
    }

    private static List<ChatCompletionChoice> convertChoices(List<GenerationOutput.Choice> aliChoices) {
        if (CollUtil.isNotEmpty(aliChoices)) {
            List<ChatCompletionChoice> choices = new ArrayList<>(1);
            for (int i = 0; i < aliChoices.size(); i++) {
                GenerationOutput.Choice choice = aliChoices.get(i);
                Message message = choice.getMessage();
                AssistantMessage assMessage = new AssistantMessage();
                assMessage.setContent(message.getContent());
                assMessage.setReasoningContent(message.getReasoningContent());
                assMessage.setName(message.getName());
                // TODO Took call

                ChatCompletionChoice targetChoice = new ChatCompletionChoice();
                targetChoice.setIndex(i);
                targetChoice.setFinishReason(choice.getFinishReason());
                targetChoice.setMessage(assMessage);
                choices.add(targetChoice);
            }
            return choices;
        }
        return null;
    }


}
