package com.dangbei.aisearch.infrastructure.gatewayimpl;

import com.dangbei.aisearch.domain.entity.LoginRecordEntity;
import com.dangbei.aisearch.domain.gateway.LoginRecordGateway;
import com.dangbei.aisearch.infrastructure.common.base.BaseGatewayImpl;
import com.dangbei.aisearch.infrastructure.convertor.LoginRecordConvertor;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.LoginRecordDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.LoginRecordMapper;
import org.springframework.stereotype.Component;

/**
 * LoginRecord 网关层实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-05-19
 */
@Component
public class LoginRecordGatewayImpl extends BaseGatewayImpl<Long, LoginRecordEntity, LoginRecordDO, LoginRecordMapper, LoginRecordConvertor> implements LoginRecordGateway {

}
