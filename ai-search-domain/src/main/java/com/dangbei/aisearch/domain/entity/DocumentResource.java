package com.dangbei.aisearch.domain.entity;

/**
 * 文档资源接口
 * <AUTHOR>
 * @date 2025-06-01
 **/
public interface DocumentResource {

    /**
     * 获取文档ID
     * @return 文档ID
     */
    String getDocId();

    /**
     * 获取文档路径
     * @return 文档路径
     */
    String getDocPath();

    /**
     * 获取文档类型
     * @return 文档类型（后缀）
     */
    String getDocType();

    /**
     * 获取文档大小（字节）
     * @return 文档大小
     */
    Integer getDocSize();

    /**
     * 获取文档存储类型
     * @return 存储类型（oss/tos等）
     */
    String getStorageType();
} 