package com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.dangbei.aisearch.client.dto.AgentChatExample;
import com.dangbei.aisearch.client.dto.AgentFollowUp;
import com.dangbei.aisearch.infrastructure.common.base.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * Agent DO对象
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-28
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "ais_agent", autoResultMap = true)
public class AgentDO extends BaseDO<Long> {

    @Schema(description = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "智能体唯一ID")
    @TableField(value = "agent_id")
    private String agentId;

    @Schema(description = "智能体名称")
    @TableField(value = "name")
    private String name;

    @Schema(description = "智能体简介")
    @TableField(value = "intro")
    private String intro;

    @Schema(description = "智能体描述")
    @TableField(value = "description")
    private String description;

    @Schema(description = "智能体图标")
    @TableField(value = "icon_url")
    private String iconUrl;

    @Schema(description = "初始化推荐问题 [{\"type\" 1, \"value\": \"你好\"}]")
    @TableField(value = "follow_up", typeHandler = Fastjson2TypeHandler.class)
    private List<AgentFollowUp> followUp;

    @Schema(description = "系统提示词")
    @TableField(value = "system_prompt")
    private String systemPrompt;

    @Schema(description = "APP端背景图")
    @TableField(value = "app_bg_url")
    private String appBgUrl;

    @Schema(description = "智能体背景主色调")
    @TableField(value = "app_bg_color_tone")
    private String appBgColorTone;

    @Schema(description = "智能体音色：https://www.volcengine.com/docs/6561/1257544")
    @TableField(value = "voice_type")
    private String voiceType;

    @Schema(description = "展示创建人")
    @TableField(value = "create_show_person")
    private String createShowPerson;

    @Schema(description = "用户给智能体设定提示词 用于前端展示")
    @TableField(value = "user_prompt")
    private String userPrompt;

    @Schema(description = "智能体性格描述")
    @TableField(value = "personality")
    private String personality;

    @Schema(description = "智能体开场白")
    @TableField(value = "greeting")
    private String greeting;

    @Schema(description = "智能体性别：0-男 1-女 2-非人类角色")
    @TableField(value = "gender")
    private Integer gender;

    @Schema(description = "智能体角色类型：0-角色类 1-助理类")
    @TableField(value = "role")
    private Integer role;

    @Schema(description = "智能体来源：0-官方创建 1-用户创建 2-大屏同步")
    @TableField(value = "source")
    private Integer source;

    @Schema(description = "智能体公开状态：0-公开 1-私密 2-部分公开")
    @TableField(value = "visibility")
    private Integer visibility;

    @Schema(description = "智能体技能 online")
    @TableField(value = "skills", typeHandler = Fastjson2TypeHandler.class)
    private List<String> skills;

    @Schema(description = "智能体对话示例")
    @TableField(value = "chat_example", typeHandler = Fastjson2TypeHandler.class)
    private List<AgentChatExample> chatExample;

    @Schema(description = "智能体语音开关 0-关 1-开")
    @TableField(value = "voice_enabled")
    private Integer voiceEnabled;

    @Schema(description = "上架状态：-1-冻结 0-正常 1-用户删除 2-运营下架")
    @TableField(value = "online_status")
    private Integer onlineStatus;

    @Schema(description = "创建者用户")
    @TableField(value = "create_user_id")
    private String createUserId;

    @Schema(description = "关联的最新版本号")
    @TableField(value = "latest_version_id")
    private Long latestVersionId;

    @Override
    public Long getPrimaryId() {
        return this.id;
    }

    @Override
    public void setPrimaryId(Long id) {
        this.id = id;
    }
}
