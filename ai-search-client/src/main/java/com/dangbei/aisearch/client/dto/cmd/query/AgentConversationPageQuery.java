package com.dangbei.aisearch.client.dto.cmd.query;

import com.alibaba.cola.dto.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-28
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AgentConversationPageQuery extends PageQuery {
    /**
     * 是否过滤掉非正常状态的智能体
     */
    @Schema(description = "是否过滤掉非正常状态的智能体,true=过滤/false=不过滤", defaultValue = "false")
    private Boolean filterNonNormalStatus = false;
}
