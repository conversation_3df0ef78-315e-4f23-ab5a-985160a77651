package com.dangbei.aisearch.app.executor.query;

import com.alibaba.cola.dto.PageSingleResponse;
import com.alibaba.cola.exception.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dangbei.aisearch.app.util.UserContextUtil;
import com.dangbei.aisearch.client.dto.clientobject.KnowledgeApplyCo;
import com.dangbei.aisearch.client.dto.cmd.query.KnowledgeApplyListQuery;
import com.dangbei.aisearch.client.enums.KnowledgeApplyStatusEnum;
import com.dangbei.aisearch.client.enums.SharedKnowledgePermissionEnum;
import com.dangbei.aisearch.domain.entity.SharedKnowledgeEntity;
import com.dangbei.aisearch.domain.entity.UserInfoEntity;
import com.dangbei.aisearch.domain.gateway.SharedKnowledgeGateway;
import com.dangbei.aisearch.domain.gateway.UserInfoGateway;
import com.dangbei.aisearch.infrastructure.common.util.PageUtil;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.KnowledgeApplyDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.KnowledgeApplyMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 知识库申请列表查询执行器
 * <AUTHOR>
 * @date 2025-05-27
 **/
@Slf4j
@Component
public class KnowledgeApplyListQueryExe {

    @Resource
    private KnowledgeApplyMapper knowledgeApplyMapper;
    @Resource
    private SharedKnowledgeGateway sharedKnowledgeGateway;
    @Resource
    private UserInfoGateway userInfoGateway;

    /**
     * 执行查询
     * @param query 查询条件
     * @return 申请列表分页数据
     */
    public PageSingleResponse<KnowledgeApplyCo> execute(KnowledgeApplyListQuery query) {
        // 获取当前用户
        String userId = UserContextUtil.getNonNullUserId();
        // 获取知识库
        SharedKnowledgeEntity knowledge = sharedKnowledgeGateway.getByKnowledgeId(query.getKnowledgeId());
        Assert.notNull(knowledge, "知识库不存在");
        // 验证权限
        knowledge.validatePermission(userId, SharedKnowledgePermissionEnum.APPROVE_APPLY);

        Page<KnowledgeApplyDO> page = new Page<>(query.getPageIndex(), query.getPageSize());
        LambdaQueryWrapper<KnowledgeApplyDO> queryWrapper = buildQueryWrapper(query);
        Page<KnowledgeApplyDO> applyPage = knowledgeApplyMapper.selectPage(page, queryWrapper);

        // 批量获取用户信息
        Map<String, UserInfoEntity> userInfoMap = batchGetUserInfo(applyPage.getRecords());
        return PageUtil.convertToPageSingleResponse(applyPage, applyDO -> convertToCo(applyDO, userInfoMap));
    }

    /**
     * 构建查询条件
     * @param query 查询参数
     * @return 查询条件
     */
    private LambdaQueryWrapper<KnowledgeApplyDO> buildQueryWrapper(KnowledgeApplyListQuery query) {
        return Wrappers.lambdaQuery(KnowledgeApplyDO.class)
            .eq(KnowledgeApplyDO::getKnowledgeId, query.getKnowledgeId())
            .eq(Objects.nonNull(query.getStatus()), KnowledgeApplyDO::getStatus, query.getStatus())
            .orderByDesc(KnowledgeApplyDO::getId);
    }

    /**
     * 批量获取用户信息
     * @param applyList 申请列表
     * @return 用户信息映射
     */
    private Map<String, UserInfoEntity> batchGetUserInfo(List<KnowledgeApplyDO> applyList) {
        Set<String> userIds = new HashSet<>();
        for (KnowledgeApplyDO apply : applyList) {
            if (StringUtils.isNotBlank(apply.getApplicantId())) {
                userIds.add(apply.getApplicantId());
            }
            if (StringUtils.isNotBlank(apply.getApproverId())) {
                userIds.add(apply.getApproverId());
            }
        }
        return userInfoGateway.getUserMap(userIds);
    }

    /**
     * 转换单个申请为客户端对象
     * @param apply       申请实体
     * @param userInfoMap 用户信息映射
     * @return 客户端对象
     */
    private KnowledgeApplyCo convertToCo(KnowledgeApplyDO apply, Map<String, UserInfoEntity> userInfoMap) {
        // 从缓存中获取申请人信息
        UserInfoEntity applicant = userInfoMap.get(apply.getApplicantId());
        // 从缓存中获取审批人信息
        UserInfoEntity approver = userInfoMap.get(apply.getApproverId());

        String userId = UserContextUtil.getNonNullUserId();
        return KnowledgeApplyCo.builder()
            .applyId(apply.getApplyId())
            .applicantNickname(Objects.nonNull(applicant) ? applicant.getNickname() : null)
            .applicantAvatar(Objects.nonNull(applicant) ? applicant.getAvatar() : null)
            .status(apply.getStatus())
            .statusName(KnowledgeApplyStatusEnum.getDescByCode(apply.getStatus()))
            .applyTime(apply.getApplyTime())
            .approverNickname(Objects.nonNull(approver) ? approver.getNickname() : null)
            .approveTime(apply.getProcessTime())
            .applicantIsSelf(Objects.nonNull(applicant) && userId.equals(applicant.getUserId()))
            .approverIsSelf(Objects.nonNull(approver) && userId.equals(approver.getUserId()))
            .build();
    }
}
