package com.dangbei.aisearch.infrastructure.knowledge.dto.query;

import com.alibaba.cola.dto.Query;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-10 17:58
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
public class VolcDocPointQuery extends Query {

    /**
     * 项目空间
     */
    @Builder.Default
    private String namespace = "default";
    /**
     * 火山知识库名称
     */
    private String knowledgeIdExt;

    /**
     * 火山知识库文档id
     */
    private List<String> docIdExtList;

    @Builder.Default
    private Integer limit = -1;

    @Builder.Default
    private Integer offset = 0;
}
