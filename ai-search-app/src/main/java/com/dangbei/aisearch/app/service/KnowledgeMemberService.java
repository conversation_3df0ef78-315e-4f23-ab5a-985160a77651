package com.dangbei.aisearch.app.service;

import com.alibaba.cola.dto.PageSingleResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.dangbei.aisearch.app.executor.RemoveMemberCmdExe;
import com.dangbei.aisearch.app.executor.UpdateMemberRoleExe;
import com.dangbei.aisearch.app.executor.query.KnowledgeMemberListQueryExe;
import com.dangbei.aisearch.app.executor.query.MemberPermissionsQueryExe;
import com.dangbei.aisearch.client.dto.clientobject.KnowledgeMemberCo;
import com.dangbei.aisearch.client.dto.clientobject.MemberPermissionsCo;
import com.dangbei.aisearch.client.dto.cmd.RemoveMemberCmd;
import com.dangbei.aisearch.client.dto.cmd.UpdateMemberRoleCmd;
import com.dangbei.aisearch.client.dto.cmd.query.KnowledgeMemberListQuery;
import com.dangbei.aisearch.client.dto.cmd.query.MemberPermissionsQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 知识库成员管理服务
 * <AUTHOR>
 * @date 2025-05-28
 **/
@Slf4j
@Service
public class KnowledgeMemberService {

    @Resource
    private KnowledgeMemberListQueryExe knowledgeMemberListQueryExe;
    @Resource
    private UpdateMemberRoleExe updateMemberRoleExe;
    @Resource
    private RemoveMemberCmdExe removeMemberCmdExe;
    @Resource
    private MemberPermissionsQueryExe memberPermissionsQueryExe;

    /**
     * 查询知识库成员列表
     * @param query 查询参数
     * @return 成员列表
     */
    public PageSingleResponse<KnowledgeMemberCo> getMemberList(KnowledgeMemberListQuery query) {
        return knowledgeMemberListQueryExe.execute(query);
    }

    /**
     * 修改成员角色
     * @param cmd 修改命令
     * @return 操作结果
     */
    public Response updateMemberRole(UpdateMemberRoleCmd cmd) {
        return updateMemberRoleExe.execute(cmd);
    }

    /**
     * 移除成员
     * @param cmd 移除命令
     * @return 操作结果
     */
    public Response removeMember(RemoveMemberCmd cmd) {
        return removeMemberCmdExe.execute(cmd);
    }

    /**
     * 查询成员权限
     * @param query 查询参数
     * @return 成员权限
     */
    public SingleResponse<MemberPermissionsCo> getMemberPermissions(MemberPermissionsQuery query) {
        return memberPermissionsQueryExe.execute(query);
    }
}
