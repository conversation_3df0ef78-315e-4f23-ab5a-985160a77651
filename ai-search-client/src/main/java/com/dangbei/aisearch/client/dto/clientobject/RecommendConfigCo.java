package com.dangbei.aisearch.client.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-26
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RecommendConfigCo extends ClientObject {

    /**
     * 帮我写配置
     */
    private WriteConfigCo write;

    /**
     * 智能体配置
     */
    private List<AgentConfigCo> agents;

}
