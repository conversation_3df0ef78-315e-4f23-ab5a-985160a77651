package com.dangbei.aisearch.domain.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.dangbei.aisearch.client.dto.WriteOption;
import com.dangbei.aisearch.client.dto.WritePlaceholder;
import com.dangbei.aisearch.domain.common.base.BaseEntity;
import com.dangbei.aisearch.domain.common.base.BaseGateway;
import com.dangbei.aisearch.domain.gateway.WriteMetaGateway;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * WriteMeta 领域对象
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-26
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(doNotUseGetters = true)
public class WriteMetaEntity extends BaseEntity<Long> {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "场景值(write:帮我写;quick_question:快捷提问)")
    private String scene;

    @Schema(description = "用户Id")
    private String userId;

    @Schema(description = "1-文本类型 2-下拉类型")
    private Integer type;

    @Schema(description = "元数据编码（唯一）")
    private String value;

    @Schema(description = "文本配置 示例值：{\"placeholder\":\"[主题]\",\"default_text\":\"\"}")
    private WritePlaceholder placeholder;

    @Schema(description = "下拉配置 示例值：[{\"show_name\":\"正式的\",\"value\":\"Formal\",\"is_default\":true},{\"show_name\":\"随意的\",\"value\":\"Casual\",\"is_default\":false},{\"show_name\":\"专业的\",\"value\":\"Professional\",\"is_default\":false}]")
    private List<WriteOption> options;

    private transient WriteMetaGateway writeMetaGateway = SpringUtil.getBean(WriteMetaGateway.class);

    @Override
    public Long getPrimaryId() {
        return this.id;
    }

    @Override
    public void setPrimaryId(Long id) {
        this.id = id;
    }

    @Override
    protected BaseGateway getGateWay() {
        return this.writeMetaGateway;
    }
}
