package com.dangbei.aisearch.starter;

import com.alibaba.fastjson2.JSONObject;
import com.dangbei.aisearch.infrastructure.config.properties.VolcStsProperties;
import com.dangbei.aisearch.infrastructure.volc.sts.VolcStsTokenClient;
import com.dangbei.aisearch.infrastructure.volc.sts.VolcStsTokenRequest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2025-03-24 21:13
 **/
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class VolcStsTest {

    @Resource
    private VolcStsTokenClient volcStsTokenClient;

    @Resource
    private VolcStsProperties volcStsProperties;

    @Test
    public void test() throws IOException {
        var build = VolcStsTokenRequest.builder()
            .appid(volcStsProperties.getAppId())
            .duration(3500).build();
        var call = volcStsTokenClient.getJwtToken(build);
        var response = call.execute();
        System.out.println(JSONObject.toJSONString(response));
        System.out.println(response.isSuccessful());
        System.out.println(JSONObject.toJSONString(response.body()));
    }
}
