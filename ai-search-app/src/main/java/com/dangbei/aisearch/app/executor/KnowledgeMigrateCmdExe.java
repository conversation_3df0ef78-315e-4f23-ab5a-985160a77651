package com.dangbei.aisearch.app.executor;

import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.exception.Assert;
import com.alibaba.cola.exception.BizException;
import com.dangbei.aisearch.client.dto.cmd.KnowledgeMigrateCmd;
import com.dangbei.aisearch.client.enums.DocStorageTypeEnum;
import com.dangbei.aisearch.common.enums.DocProcessStatusEnum;
import com.dangbei.aisearch.domain.entity.UserKnowledgeDocEntity;
import com.dangbei.aisearch.domain.entity.UserKnowledgeDocMigrateEntity;
import com.dangbei.aisearch.domain.gateway.UserInfoGateway;
import com.dangbei.aisearch.domain.gateway.UserKnowledgeDocGateway;
import com.dangbei.aisearch.domain.gateway.UserKnowledgeDocMigrateGateway;
import com.dangbei.aisearch.domain.gateway.UserKnowledgeGateway;
import com.dangbei.aisearch.infrastructure.common.helper.DocAccessHelper;
import com.dangbei.aisearch.infrastructure.knowledge.VolcanoKnowledgeClient;
import com.dangbei.aisearch.infrastructure.knowledge.dto.cmd.AddVolcDocCmd;
import com.dangbei.aisearch.infrastructure.knowledge.dto.co.AddVolcDocCo;
import com.dangbei.aisearch.infrastructure.knowledge.dto.co.VolcDocInfoCo;
import com.dangbei.aisearch.infrastructure.knowledge.dto.query.VolcDocInfoQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

import static com.dangbei.aisearch.infrastructure.knowledge.impl.VolcanoKnowledgeClientImpl.getData;

/**
 * <AUTHOR>
 * @date 2025-03-20 11:16
 **/
@Component
@Slf4j
@Deprecated // 走火山知识库的扩容，无需迁移
public class KnowledgeMigrateCmdExe {
    @Resource
    private UserInfoGateway userInfoGateway;
    @Resource
    private UserKnowledgeGateway userKnowledgeGateway;
    @Resource
    private UserKnowledgeDocGateway userKnowledgeDocGateway;
    @Resource
    private UserKnowledgeDocMigrateGateway knowledgeDocMigrateGateway;
    @Resource
    private DocAccessHelper docAccessHelper;
    @Resource
    private VolcanoKnowledgeClient volcanoKnowledgeClient;
    @Resource(name = "migrateDocAsyncExecutor")
    private Executor migrateDocAsyncExecutor;

    private static final Integer MAX_POLLING_SECONDS = 180;

    public Response execute(KnowledgeMigrateCmd cmd) {
        var targetUser = userInfoGateway.getByUserId(cmd.getUserId());
        Assert.notNull(targetUser, "用户不存在");
        var targetKnowledge = userKnowledgeGateway.getByKnowledgeId(cmd.getOldKnowledgeId());
        Assert.notNull(targetKnowledge, "知识库不存在");
        List<UserKnowledgeDocEntity> docs = userKnowledgeDocGateway.getByKnowledgeIdAndStatus(cmd.getOldKnowledgeId(), DocProcessStatusEnum.COMPLETED);

        // 插入迁移记录
        List<UserKnowledgeDocMigrateEntity> migrateEntities = knowledgeDocMigrateGateway.prepare(cmd.getUserId(), targetKnowledge.getKnowledgeIdExt(), cmd.getNewKnowledgeIdExt(), docs);
        log.info("用户：{}, 知识库文档迁移条数：{}", cmd.getUserId(), migrateEntities.size());
        // 异步执行迁移
        CompletableFuture.runAsync(() -> {
            doMigrate(migrateEntities);
        }, migrateDocAsyncExecutor);

        return Response.buildSuccess();
    }

    private void doMigrate(List<UserKnowledgeDocMigrateEntity> migrateEntities) {
        if (CollectionUtils.isEmpty(migrateEntities)) {
            return;
        }
        for (UserKnowledgeDocMigrateEntity migrateEntity : migrateEntities) {
            // 生成新的 oss 临时文档链接
            var tmpUrl = docAccessHelper.getAccessUrl(DocStorageTypeEnum.TOS, migrateEntity.getDocPath());
            // 上传火山文档
            var success = addDocToVolc(migrateEntity, tmpUrl);
            if (!success) {
                log.error("用户：{}, 文档：{} 火山知识库文档迁移失败", migrateEntity.getUserId(), migrateEntity.getDocId());
                continue;
            }
            // 异步轮训状态 2 分钟
            CompletableFuture.runAsync(() -> {
                pollingDocStatus(migrateEntity);
            }, migrateDocAsyncExecutor);
            try {
                TimeUnit.SECONDS.sleep(1);
            } catch (InterruptedException ignore) {}
        }
    }

    private void pollingDocStatus(UserKnowledgeDocMigrateEntity migrateEntity) {
        // 五秒查询一次文档解析状态
        VolcDocInfoQuery build = VolcDocInfoQuery.builder()
            .knowledgeIdExt(migrateEntity.getNewKnowledgeIdExt())
            .docIdExt(migrateEntity.getNewDocIdExt())
            .returnTokenUsage(true)
            .build();

        long startAt = System.currentTimeMillis();
        while (true) {
            try {
                VolcDocInfoCo volcDocInfo = getData(volcanoKnowledgeClient.docInfo(build), VolcDocInfoCo.class);
                if (Objects.isNull(volcDocInfo)) {
                    continue;
                }
                if (DocProcessStatusEnum.COMPLETED.equals(volcDocInfo.convertDocStatus())) {
                    // 解析成功
                    migrateEntity.setProcessStatus(DocProcessStatusEnum.COMPLETED.getCode());
                    migrateEntity.update();
                }
                if (DocProcessStatusEnum.FAILED.equals(volcDocInfo.convertDocStatus())) {
                    // 解析失败
                    migrateEntity.setProcessStatus(DocProcessStatusEnum.FAILED.getCode());
                    migrateEntity.update();
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            // 轮询超时跳出
            if ((System.currentTimeMillis() - startAt) / 1000 > MAX_POLLING_SECONDS) {
                break;
            }
            ThreadUtil.sleep(5000);
        }
    }

    private boolean addDocToVolc(UserKnowledgeDocMigrateEntity migrateEntity, String tmpUrl) {
        AddVolcDocCmd cmd = AddVolcDocCmd.builder()
            .knowledgeIdExt(migrateEntity.getNewKnowledgeIdExt())
            .docName(migrateEntity.getDocName())
            .docIdExt(migrateEntity.getNewDocIdExt())
            .docType(migrateEntity.getDocType())
            .url(tmpUrl)
            // 开启去重
            .dedup(AddVolcDocCmd.Dedup.builder().contentDedup(true).autoSkip(true).build())
            .build();
        try {
            AddVolcDocCo addVolcDocCo = getData(volcanoKnowledgeClient.addDoc(cmd), AddVolcDocCo.class);
            Assert.notNull(addVolcDocCo);

            if (!addVolcDocCo.getDocId().equals(migrateEntity.getNewDocIdExt())) {
                migrateEntity.setNewDocIdExt(addVolcDocCo.getDocId());
                migrateEntity.update();
            }
        } catch (BizException e) {
            log.error(e.getMessage(), e);
            return false;
        }
        return true;
    }
}
