package com.dangbei.aisearch.client.dto.clientobject;

import com.alibaba.cola.dto.DTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 消息列表
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-25
 */
@Data
@Accessors(chain = true)
public class MessageListCo extends DTO {

    @Schema(description = "是否还有更多")
    private Boolean hasMore;

    @Schema(description = "返回的消息列表中，第一条消息的 MegId")
    private String firstMsgId;

    @Schema(description = "返回的消息列表中，最后一条消息的 MegId")
    private String lastMsgId;

    @Schema(description = "会话标题")
    private String conversationTitle;

    @Schema(description = "标题是否已总结(1:是;0:否)")
    private Integer titleSummaryFlag;

    @Schema(description = "消息列表")
    private List<MessageCo> msgList;

}
