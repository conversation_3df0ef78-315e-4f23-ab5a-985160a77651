package com.dangbei.aisearch.infrastructure.gatewayimpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dangbei.aisearch.client.enums.WriteMetaSceneEnum;
import com.dangbei.aisearch.client.enums.WriteMetaTypeEnum;
import com.dangbei.aisearch.domain.entity.WriteMetaEntity;
import com.dangbei.aisearch.domain.gateway.WriteMetaGateway;
import com.dangbei.aisearch.infrastructure.common.base.BaseGatewayImpl;
import com.dangbei.aisearch.infrastructure.convertor.WriteMetaConvertor;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.WriteMetaDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.WriteMetaMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * WriteMeta 网关层实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-26
 */
@Component
public class WriteMetaGatewayImpl extends BaseGatewayImpl<Long, WriteMetaEntity, WriteMetaDO, WriteMetaMapper, WriteMetaConvertor> implements WriteMetaGateway {

    @Override
    public List<String> listQuickMetaPlaceholderByUserId(String userId) {
        LambdaQueryWrapper<WriteMetaDO> queryWrapper = Wrappers.lambdaQuery(WriteMetaDO.class)
            .select(WriteMetaDO::getPlaceholder)
            .eq(WriteMetaDO::getScene, WriteMetaSceneEnum.QUICK_QUESTION.getScene())
            .eq(WriteMetaDO::getType, WriteMetaTypeEnum.TEXT.getType())
            .eq(WriteMetaDO::getUserId, userId);
        return baseMapper.selectList(queryWrapper)
            .stream()
            .filter(writeMetaDO -> Objects.nonNull(writeMetaDO.getPlaceholder()))
            .filter(writeMetaDO -> StringUtils.isNotBlank(writeMetaDO.getPlaceholder().getPlaceholder()))
            .map(writeMetaDO -> writeMetaDO.getPlaceholder().getPlaceholder())
            .collect(Collectors.toList());
    }

    @Override
    public List<WriteMetaEntity> listQuickMetaByUserId(String userId) {
        LambdaQueryWrapper<WriteMetaDO> queryWrapper = Wrappers.lambdaQuery(WriteMetaDO.class)
            .eq(WriteMetaDO::getScene, WriteMetaSceneEnum.QUICK_QUESTION.getScene())
            .eq(WriteMetaDO::getUserId, userId);
        return convertor.toEntityList(baseMapper.selectList(queryWrapper));
    }
}
