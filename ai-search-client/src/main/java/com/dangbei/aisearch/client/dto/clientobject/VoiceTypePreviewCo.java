package com.dangbei.aisearch.client.dto.clientobject;

import com.alibaba.cola.dto.DTO;
import com.dangbei.aisearch.client.enums.AgentVoiceEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 音色预览接口
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-04-11
 */
@Data
@Accessors(chain = true)
public class VoiceTypePreviewCo extends DTO {

    @Schema(description = "音色值")
    private String voiceType;

    @Schema(description = "音色预览地址")
    private String voiceName;

    @Schema(description = "音色预览地址")
    private String trialUrl;

    @Schema(description = "是否默认")
    private boolean defaultFlag = false;

    /**
     * 音色对象构建
     * @param voiceEnum 音色枚举
     * @param isDefault 是否默认
     * @return 音色对象
     */
    public static VoiceTypePreviewCo of(AgentVoiceEnum voiceEnum, boolean isDefault) {
        return new VoiceTypePreviewCo()
            .setVoiceType(voiceEnum.getValue())
            .setVoiceName(voiceEnum.getDesc())
            .setTrialUrl(voiceEnum.getTrialUrl())
            .setDefaultFlag(isDefault);
    }

}
