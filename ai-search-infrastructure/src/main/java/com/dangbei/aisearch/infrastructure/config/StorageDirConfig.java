package com.dangbei.aisearch.infrastructure.config;

import com.dangbei.aisearch.client.enums.StorageDirTypeEnum;
import com.dangbei.aisearch.infrastructure.config.properties.StorageDirProperties;
import lombok.Getter;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * StorageDirTypeEnum 配置类
 * {@link StorageDirTypeEnum}
 * <AUTHOR>
 * @date 2025-04-25 15:01
 **/
@Configuration
@Getter
public class StorageDirConfig {

    @Resource
    private StorageDirProperties storageDirProperties;

    @PostConstruct
    public void init() {
        StorageDirTypeEnum.setFilePrefix(storageDirProperties.getFilePrefix());
        StorageDirTypeEnum.setPublicPrefix(storageDirProperties.getPublicPrefix());
    }
}
