package com.dangbei.aisearch.app.executor;

import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.Assert;
import com.dangbei.aisearch.app.util.UserContextUtil;
import com.dangbei.aisearch.client.dto.clientobject.CommunityPostPreviewCo;
import com.dangbei.aisearch.client.dto.cmd.MessageLightUpPreviewCmd;
import com.dangbei.aisearch.common.util.MarkdownUtils;
import com.dangbei.aisearch.domain.entity.ChatMessageEntity;
import com.dangbei.aisearch.domain.entity.UserInfoEntity;
import com.dangbei.aisearch.domain.gateway.UserInfoGateway;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 点亮消息预览命令执行器
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-04-01
 */
@Component
public class MessageLightUpPreviewCmdExe {

    @Resource
    private UserInfoGateway userInfoGateway;
    @Resource
    private MessageLightUpCmdExe messageLightUpCmdExe;
    @Resource
    private CommunityPublishPostCmdExe communityPublishPostCmdExe;
    @Resource
    private SummaryConversationTitleCmdExe summaryConversationTitleCmdExe;

    public SingleResponse<CommunityPostPreviewCo> execute(MessageLightUpPreviewCmd cmd) {
        // 用户信息
        UserInfoEntity userInfo = userInfoGateway.getByUserId(UserContextUtil.getUserId());
        Assert.notNull(userInfo, "用户信息不存在");

        // 校验权限，获取回答消息
        ChatMessageEntity entity = messageLightUpCmdExe.preCheck(cmd.getMsgId());
        Assert.notBlank(entity.getContent(), "点亮失败，回答内容为空");

        // 查询提问消息
        ChatMessageEntity userMessage = entity.getUserMessage();
        if (Objects.nonNull(userMessage)) {
            Assert.isTrue(communityPublishPostCmdExe.textGreenCheck(userMessage.getContent()), "点亮失败，请选择其他内容点亮");
        }

        // 总结标题
        String title = isUserQuestionBlank(userMessage) ? userMessage.getContent() : summaryConversationTitleCmdExe.summaryTitle(entity.getContent(), 12);

        return SingleResponse.of(
            new CommunityPostPreviewCo()
                .setTitle(title)
                .setCreateTime(LocalDateTime.now())
                .setUserId(userInfo.getUserId())
                .setNickname(userInfo.getNickname())
                .setAvatar(userInfo.getAvatar())
                .setAnswerContent(MarkdownUtils.removeMarkdownTags(entity.getContent()))
        );
    }

    private boolean isUserQuestionBlank(ChatMessageEntity userMessage) {
        return Objects.nonNull(userMessage) && StrUtil.isNotBlank(userMessage.getContent());
    }

}
