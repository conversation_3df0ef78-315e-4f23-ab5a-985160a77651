package com.dangbei.aisearch.client.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025-05-20 15:02
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
public class AgentSkillCo extends ClientObject {

    @Schema(description = "技能类型")
    private String type;

    @Schema(description = "技能描述")
    private String desc;
}
