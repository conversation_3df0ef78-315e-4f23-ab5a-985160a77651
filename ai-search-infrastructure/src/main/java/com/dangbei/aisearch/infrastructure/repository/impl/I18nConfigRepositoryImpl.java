package com.dangbei.aisearch.infrastructure.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.I18nConfigDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.I18nConfigMapper;
import com.dangbei.aisearch.infrastructure.repository.I18nConfigRepository;
import org.springframework.stereotype.Repository;

/**
 * I18nConfig 仓库实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-14
 */
@Repository
public class I18nConfigRepositoryImpl extends ServiceImpl<I18nConfigMapper, I18nConfigDO> implements I18nConfigRepository {

}
