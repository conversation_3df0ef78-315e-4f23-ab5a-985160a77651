package com.dangbei.aisearch.client.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025-03-26 16:24
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
public class GenAvatarTaskCo extends ClientObject {

    @Schema(description = "任务ID")
    private String taskId;
}
