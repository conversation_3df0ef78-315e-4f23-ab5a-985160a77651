package com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dangbei.aisearch.client.dto.clientobject.DocIdExtCo;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.UserKnowledgeDocDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * UserKnowledgeDoc Mapper接口
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-10
 */
public interface UserKnowledgeDocMapper extends BaseMapper<UserKnowledgeDocDO> {

    /**
     * 查询docIdExt相同的文档数据
     * @param docIdList    文档ID列表
     * @param docIdExtList 外部文档ID列表
     * @return docIdExt相同的文档数据
     */
    List<DocIdExtCo> getDocIdExtUseCount(@Param("docIdExtList") List<String> docIdExtList,
                                         @Param("docIdList") List<String> docIdList);

}
