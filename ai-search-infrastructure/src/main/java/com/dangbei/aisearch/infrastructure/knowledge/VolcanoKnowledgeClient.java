package com.dangbei.aisearch.infrastructure.knowledge;

import com.dangbei.aisearch.infrastructure.knowledge.dto.ApiResult;
import com.dangbei.aisearch.infrastructure.knowledge.dto.cmd.AddVolcDocCmd;
import com.dangbei.aisearch.infrastructure.knowledge.dto.cmd.DeleteVolcDocCmd;
import com.dangbei.aisearch.infrastructure.knowledge.dto.cmd.VolcDocRerankCmd;
import com.dangbei.aisearch.infrastructure.knowledge.dto.co.AddVolcDocCo;
import com.dangbei.aisearch.infrastructure.knowledge.dto.co.VolcKnowledgeInfoCo;
import com.dangbei.aisearch.infrastructure.knowledge.dto.co.SearchVolcKnowledgeCo;
import com.dangbei.aisearch.infrastructure.knowledge.dto.co.VolcDocInfoCo;
import com.dangbei.aisearch.infrastructure.knowledge.dto.co.VolcDocPointCo;
import com.dangbei.aisearch.infrastructure.knowledge.dto.co.VolcRerankCo;
import com.dangbei.aisearch.infrastructure.knowledge.dto.query.SearchVolcKnowledgeQuery;
import com.dangbei.aisearch.infrastructure.knowledge.dto.query.VolcDocInfoQuery;
import com.dangbei.aisearch.infrastructure.knowledge.dto.query.VolcDocPointQuery;
import com.dangbei.aisearch.infrastructure.knowledge.dto.query.VolcKnowledgeInfoQuery;

/**
 * 火山方舟知识库接口
 * <AUTHOR>
 * @date 2025-03-06 16:37
 **/
public interface VolcanoKnowledgeClient {

    /**
     * 接口用于对知识库进行检索和前后处理，当前会默认对原始文本加工后的知识内容进行检索。
     * <a href="https://www.volcengine.com/docs/84313/1350012">...</a>
     */
    ApiResult<SearchVolcKnowledgeCo> searchKnowledge(SearchVolcKnowledgeQuery query);

    /**
     * 接口用于向已创建的知识库导入文档。
     * <a href="https://www.volcengine.com/docs/84313/1254624">...</a>
     */
    ApiResult<AddVolcDocCo> addDoc(AddVolcDocCmd addVolcDocCmd);

    /**
     * 接口用于查看知识库下的某个文档的信息。
     * <a href="https://www.volcengine.com/docs/84313/1254615">...</a>
     */
    ApiResult<VolcDocInfoCo> docInfo(VolcDocInfoQuery volcDocInfoQuery);

    /**
     * 接口用于从知识库删除文档，文档删除会自动触发索引中的数据删除。
     * <a href="https://www.volcengine.com/docs/84313/1254608">...</a>
     */
    ApiResult<Void> deleteDoc(DeleteVolcDocCmd deleteVolcDocCmd);


    /**
     * 接口用于查看知识库下的切片列表，默认按照point_id从小到大排序。
     * <a href="https://www.volcengine.com/docs/84313/1254630">...</a>
     */
    ApiResult<VolcDocPointCo> listPoint(VolcDocPointQuery volcDocPointQuery);


    /**
     * 接口用于重新批量计算输入文本与检索到的文本之间的 score 值，以对召回结果进行重排序。
     * 判断依据 chunk content 能回答 query 提问的概率，分数越高即模型认为该文本片能回答 query 提问的概率越大。
     * <a href="https://www.volcengine.com/docs/84313/1254474">...</a>
     */
    ApiResult<VolcRerankCo> rerank(VolcDocRerankCmd volcDocRerankCmd);

    /**
     * 接口用于查看知识库详情，根据知识库名称返回知识库的描述，以及知识库配置的pipeline详细信息
     * <a href="https://www.volcengine.com/docs/82379/1399530">...</a>
     */
    ApiResult<VolcKnowledgeInfoCo> knowledgeInfo(VolcKnowledgeInfoQuery volcKnowledgeInfoQuery);

}
