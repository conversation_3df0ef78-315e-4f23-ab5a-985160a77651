package com.dangbei.aisearch.client.enums;

import com.dangbei.aisearch.client.dto.CommunityPostReportDict;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 帖子举报类型枚举
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-04-11
 */
@Getter
@AllArgsConstructor
public enum CommunityPostReportTypeEnum {
    INFRINGEMENT(1, "侵权"),
    BAD(2, "不良"),
    ILLEGAL(3, "违法"),
    OTHER(4, "其他");

    private final Integer code;
    private final String name;

    public static CommunityPostReportTypeEnum getCode(Integer code) {
        return Arrays.stream(CommunityPostReportTypeEnum.values())
            .filter(e -> e.getCode().equals(code))
            .findFirst()
            .orElse(null);
    }

    public static String getName(Integer code) {
        return Arrays.stream(CommunityPostReportTypeEnum.values())
            .filter(e -> e.getCode().equals(code))
            .map(CommunityPostReportTypeEnum::getName).findFirst()
            .orElse(StringUtils.EMPTY);
    }

    public static List<CommunityPostReportDict> getDict() {
        return Arrays.stream(CommunityPostReportTypeEnum.values())
            .map(e -> {
                CommunityPostReportDict dict = new CommunityPostReportDict();
                dict.setValue(e.getCode());
                dict.setName(e.getName());
                return dict;
            })
            .collect(Collectors.toList());
    }
}
