package com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.SharedKnowledgeDO;
import org.apache.ibatis.annotations.Param;

/**
 * SharedKnowledge Mapper接口
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-05-27
 */
public interface SharedKnowledgeMapper extends BaseMapper<SharedKnowledgeDO> {

    /**
     * 查询用户所有知识库（包括创建的和加入的）
     *
     * @param page 分页对象
     * @param userId 用户ID
     * @param name 知识库名称（模糊查询）
     * @return 分页结果
     */
    Page<SharedKnowledgeDO> pageQueryUserAllKnowledge(Page<SharedKnowledgeDO> page, @Param("userId") String userId, @Param("name") String name);

}
