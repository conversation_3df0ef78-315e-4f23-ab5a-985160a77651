package com.dangbei.aisearch.app.service;

import com.alibaba.cola.dto.PageSingleResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.dangbei.aisearch.app.executor.MarkNotificationReadCmdExe;
import com.dangbei.aisearch.app.executor.query.NotificationListQueryExe;
import com.dangbei.aisearch.app.executor.query.NotificationUnReadQueryExe;
import com.dangbei.aisearch.client.dto.clientobject.NotificationListCo;
import com.dangbei.aisearch.client.dto.clientobject.UnreadCountCo;
import com.dangbei.aisearch.client.dto.cmd.MarkNotificationReadCmd;
import com.dangbei.aisearch.client.dto.cmd.query.NotificationListQuery;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-05-30
 */
@Service
public class UserNotificationService {

    @Resource
    private NotificationListQueryExe notificationListQueryExe;
    @Resource
    private NotificationUnReadQueryExe notificationUnReadQueryExe;
    @Resource
    private MarkNotificationReadCmdExe markNotificationReadCmdExe;

    public PageSingleResponse<NotificationListCo> getNotificationList(NotificationListQuery query) {
        return notificationListQueryExe.execute(query);
    }

    public SingleResponse<UnreadCountCo> getUnreadCount() {
        return notificationUnReadQueryExe.execute();
    }

    public Response markNotificationRead(MarkNotificationReadCmd cmd) {
        return markNotificationReadCmdExe.execute(cmd);
    }
}
